/*!
 * ZUI: Lite edition - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */
/*! Some code copy from Bootstrap v3.0.0 by @fat and @mdo. (Copyright 2013 Twitter, Inc. Licensed under http://www.apache.org/licenses/)*/
!function(t,e,i){"use strict";if("undefined"==typeof t)throw new Error("ZUI requires jQuery");t.zui||(t.zui=function(e){t.isPlainObject(e)&&t.extend(t.zui,e)});var o={all:-1,left:0,middle:1,right:2},n=0;t.zui({uuid:function(){return 1e3*(new Date).getTime()+n++%1e3},callEvent:function(e,o,n){if(t.isFunction(e)){n!==i&&(e=t.proxy(e,n));var a=e(o);return o&&(o.result=a),!(a!==i&&!a)}return 1},clientLang:function(){var i,o=e.config;if("undefined"!=typeof o&&o.clientLang&&(i=o.clientLang),!i){var n=t("html").attr("lang");i=n?n:navigator.userLanguage||navigator.userLanguage||"zh_cn"}return i.replace("-","_").toLowerCase()},strCode:function(t){var e=0;if(t&&t.length)for(var i=0;i<t.length;++i)e+=i*t.charCodeAt(i);return e},getMouseButtonCode:function(t){return"number"!=typeof t&&(t=o[t]),t!==i&&null!==t||(t=-1),t}}),t.fn.callEvent=function(e,o,n){var a=t(this),s=e.indexOf(".zui."),r=s<0?e:e.substring(0,s),l=t.Event(r,o);if(n===i&&s>0&&(n=a.data(e.substring(s+1))),n&&n.options){var d=n.options[r];t.isFunction(d)&&(l.result=t.zui.callEvent(d,l,n))}return a.trigger(l),l},t.fn.callComEvent=function(e,o,n){n===i||t.isArray(n)||(n=[n]);var a=this,s=a.triggerHandler(o,n),r=e.options[o];return r&&(s=r.apply(e,n)),s}}(jQuery,window,void 0),function(t){"use strict";t.fn.fixOlPd=function(e){return e=e||10,this.each(function(){var i=t(this);i.css("paddingLeft",Math.ceil(Math.log10(i.children().length))*e+10)})},t(function(){t(".ol-pd-fix,.article ol").fixOlPd()})}(jQuery),+function(t){"use strict";var e='[data-dismiss="alert"]',i="zui.alert",o=function(i){t(i).on("click",e,this.close)};o.prototype.close=function(e){function o(){s.trigger("closed."+i).remove()}var n=t(this),a=n.attr("data-target");a||(a=n.attr("href"),a=a&&a.replace(/.*(?=#[^\s]*$)/,""));var s=t(a);e&&e.preventDefault(),s.length||(s=n.hasClass("alert")?n:n.parent()),s.trigger(e=t.Event("close."+i)),e.isDefaultPrevented()||(s.removeClass("in"),t.support.transition&&s.hasClass("fade")?s.one(t.support.transition.end,o).emulateTransitionEnd(150):o())};var n=t.fn.alert;t.fn.alert=function(e){return this.each(function(){var n=t(this),a=n.data(i);a||n.data(i,a=new o(this)),"string"==typeof e&&a[e].call(n)})},t.fn.alert.Constructor=o,t.fn.alert.noConflict=function(){return t.fn.alert=n,this},t(document).on("click."+i+".data-api",e,o.prototype.close)}(window.jQuery),+function(t){"use strict";function e(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var i in e)if(void 0!==t.style[i])return{end:e[i]};return!1}t.fn.emulateTransitionEnd=function(e){var i=!1,o=this;t(this).one("bsTransitionEnd",function(){i=!0});var n=function(){i||t(o).trigger(t.support.transition.end)};return setTimeout(n,e),this},t(function(){t.support.transition=e(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}})})}(jQuery),+function(t){"use strict";var e="zui.collapse",i=function(e,o){this.$element=t(e),this.options=t.extend({},i.DEFAULTS,o),this.transitioning=null,this.options.parent&&(this.$parent=t(this.options.parent)),this.options.toggle&&this.toggle()};i.DEFAULTS={toggle:!0},i.prototype.dimension=function(){var t=this.$element.hasClass("width");return t?"width":"height"},i.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var i=t.Event("show."+e);if(this.$element.trigger(i),!i.isDefaultPrevented()){var o=this.$parent&&this.$parent.find(".in");if(o&&o.length){var n=o.data(e);if(n&&n.transitioning)return;o.collapse("hide"),n||o.data(e,null)}var a=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[a](0),this.transitioning=1;var s=function(){this.$element.removeClass("collapsing").addClass("in")[a]("auto"),this.transitioning=0,this.$element.trigger("shown."+e)};if(!t.support.transition)return s.call(this);var r=t.camelCase(["scroll",a].join("-"));this.$element.one(t.support.transition.end,t.proxy(s,this)).emulateTransitionEnd(350)[a](this.$element[0][r])}}},i.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var i=t.Event("hide."+e);if(this.$element.trigger(i),!i.isDefaultPrevented()){var o=this.dimension();this.$element[o](this.$element[o]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse").removeClass("in"),this.transitioning=1;var n=function(){this.transitioning=0,this.$element.trigger("hidden."+e).removeClass("collapsing").addClass("collapse")};return t.support.transition?void this.$element[o](0).one(t.support.transition.end,t.proxy(n,this)).emulateTransitionEnd(350):n.call(this)}}},i.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()};var o=t.fn.collapse;t.fn.collapse=function(o){return this.each(function(){var n=t(this),a=n.data(e),s=t.extend({},i.DEFAULTS,n.data(),"object"==typeof o&&o);a||n.data(e,a=new i(this,s)),"string"==typeof o&&a[o]()})},t.fn.collapse.Constructor=i,t.fn.collapse.noConflict=function(){return t.fn.collapse=o,this},t(document).on("click."+e+".data-api","[data-toggle=collapse]",function(i){var o,n=t(this),a=n.attr("data-target")||i.preventDefault()||(o=n.attr("href"))&&o.replace(/.*(?=#[^\s]+$)/,""),s=t(a),r=s.data(e),l=r?"toggle":n.data(),d=n.attr("data-parent"),h=d&&t(d);r&&r.transitioning||(h&&h.find('[data-toggle=collapse][data-parent="'+d+'"]').not(n).addClass("collapsed"),n[s.hasClass("in")?"addClass":"removeClass"]("collapsed")),s.collapse(l)})}(window.jQuery),function(t,e){"use strict";var i=1200,o=992,n=768,a=e(t),s=function(){var t=a.width();e("html").toggleClass("screen-desktop",t>=o&&t<i).toggleClass("screen-desktop-wide",t>=i).toggleClass("screen-tablet",t>=n&&t<o).toggleClass("screen-phone",t<n).toggleClass("device-mobile",t<o).toggleClass("device-desktop",t>=o)},r="",l=navigator.userAgent;l.match(/(iPad|iPhone|iPod)/i)?r+=" os-ios":l.match(/android/i)?r+=" os-android":l.match(/Win/i)?r+=" os-windows":l.match(/Mac/i)?r+=" os-mac":l.match(/Linux/i)?r+=" os-linux":l.match(/X11/i)&&(r+=" os-unix"),"ontouchstart"in document.documentElement&&(r+=" is-touchable"),e("html").addClass(r),a.resize(s),s()}(window,jQuery),function(t){"use strict";var e={zh_cn:'您的浏览器版本过低，无法体验所有功能，建议升级或者更换浏览器。 <a href="http://browsehappy.com/" target="_blank" class="alert-link">了解更多...</a>',zh_tw:'您的瀏覽器版本過低，無法體驗所有功能，建議升級或者更换瀏覽器。<a href="http://browsehappy.com/" target="_blank" class="alert-link">了解更多...</a>',en:'Your browser is too old, it has been unable to experience the colorful internet. We strongly recommend that you upgrade a better one. <a href="http://browsehappy.com/" target="_blank" class="alert-link">Learn more...</a>'},i=function(){var t=this.isIE()||this.isIE10()||!1;if(t)for(var e=10;e>5;e--)if(this.isIE(e)){t=e;break}this.ie=t,this.cssHelper()};i.prototype.cssHelper=function(){var e=this.ie,i=t("html");i.toggleClass("ie",e).removeClass("ie-6 ie-7 ie-8 ie-9 ie-10"),e&&i.addClass("ie-"+e).toggleClass("gt-ie-7 gte-ie-8 support-ie",e>=8).toggleClass("lte-ie-7 lt-ie-8 outdated-ie",e<8).toggleClass("gt-ie-8 gte-ie-9",e>=9).toggleClass("lte-ie-8 lt-ie-9",e<9).toggleClass("gt-ie-9 gte-ie-10",e>=10).toggleClass("lte-ie-9 lt-ie-10",e<10)},i.prototype.tip=function(i){var o=t("#browseHappyTip");o.length||(o=t('<div id="browseHappyTip" class="alert alert-dismissable alert-danger-inverse alert-block" style="position: relative; z-index: 99999"><button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button><div class="container"><div class="content text-center"></div></div></div>'),o.prependTo("body")),o.find(".content").html(i||this.browseHappyTip||e[t.zui.clientLang()||"zh_cn"])},i.prototype.isIE=function(t){if(10===t)return this.isIE10();var e=document.createElement("b");return e.innerHTML="<!--[if IE "+(t||"")+"]><i></i><![endif]-->",1===e.getElementsByTagName("i").length},i.prototype.isIE10=function(){return!1},t.zui({browser:new i}),t(function(){t("body").hasClass("disabled-browser-tip")||t.zui.browser.ie&&t.zui.browser.ie<8&&t.zui.browser.tip()})}(jQuery),function(){"use strict";Date.ONEDAY_TICKS=864e5,Date.prototype.format||(Date.prototype.format=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),"S+":this.getMilliseconds()};/(y+)/i.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length)));for(var i in e)new RegExp("("+i+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?e[i]:("00"+e[i]).substr((""+e[i]).length)));return t}),Date.prototype.addMilliseconds||(Date.prototype.addMilliseconds=function(t){return this.setTime(this.getTime()+t),this}),Date.prototype.addDays||(Date.prototype.addDays=function(t){return this.addMilliseconds(t*Date.ONEDAY_TICKS),this}),Date.prototype.clone||(Date.prototype.clone=function(){var t=new Date;return t.setTime(this.getTime()),t}),Date.isLeapYear||(Date.isLeapYear=function(t){return t%4===0&&t%100!==0||t%400===0}),Date.getDaysInMonth||(Date.getDaysInMonth=function(t,e){return[31,Date.isLeapYear(t)?29:28,31,30,31,30,31,31,30,31,30,31][e]}),Date.prototype.isLeapYear||(Date.prototype.isLeapYear=function(){return Date.isLeapYear(this.getFullYear())}),Date.prototype.clearTime||(Date.prototype.clearTime=function(){return this.setHours(0),this.setMinutes(0),this.setSeconds(0),this.setMilliseconds(0),this}),Date.prototype.getDaysInMonth||(Date.prototype.getDaysInMonth=function(){return Date.getDaysInMonth(this.getFullYear(),this.getMonth())}),Date.prototype.addMonths||(Date.prototype.addMonths=function(t){var e=this.getDate();return this.setDate(1),this.setMonth(this.getMonth()+t),this.setDate(Math.min(e,this.getDaysInMonth())),this}),Date.prototype.getLastWeekday||(Date.prototype.getLastWeekday=function(t){t=t||1;for(var e=this.clone();e.getDay()!=t;)e.addDays(-1);return e.clearTime(),e}),Date.prototype.isSameDay||(Date.prototype.isSameDay=function(t){return t.toDateString()===this.toDateString()}),Date.prototype.isSameWeek||(Date.prototype.isSameWeek=function(t){var e=this.getLastWeekday(),i=e.clone().addDays(7);return t>=e&&t<i}),Date.prototype.isSameYear||(Date.prototype.isSameYear=function(t){return this.getFullYear()===t.getFullYear()}),Date.create||(Date.create=function(t){return t instanceof Date||("number"==typeof t&&t<1e10&&(t*=1e3),t=new Date(t)),t}),Date.timestamp||(Date.timestamp=function(t){return"number"==typeof t?t<1e10&&(t*=1e3):t=Date.create(t).getTime(),t})}(),function(){"use strict";String.prototype.format||(String.prototype.format=function(t){var e=this;if(arguments.length>0){var i;if(arguments.length<=2&&"object"==typeof t)for(var o in t)void 0!==t[o]&&(i=new RegExp("("+(arguments[1]?arguments[1].replace("0",o):"{"+o+"}")+")","g"),e=e.replace(i,t[o]));else for(var n=0;n<arguments.length;n++)void 0!==arguments[n]&&(i=new RegExp("({["+n+"]})","g"),e=e.replace(i,arguments[n]))}return e}),String.prototype.isNum||(String.prototype.isNum=function(t){if(null!==t){var e,i;return i=/\d*/i,e=t.match(i),e==t}return!1}),String.prototype.endsWith||(String.prototype.endsWith=function(t,e){var i=this.toString();(void 0===e||e>i.length)&&(e=i.length),e-=t.length;var o=i.indexOf(t,e);return o!==-1&&o===e}),String.prototype.startsWith||(String.prototype.startsWith=function(t,e){return e=e||0,this.lastIndexOf(t,e)===e}),String.prototype.includes||(String.prototype.includes=function(){return String.prototype.indexOf.apply(this,arguments)!==-1})}(),/*!
 * jQuery resize event - v1.1
 * http://benalman.com/projects/jquery-resize-plugin/
 * Copyright (c) 2010 "Cowboy" Ben Alman
 * MIT & GPL http://benalman.com/about/license/
 */
function(t,e,i){"$:nomunge";function o(){n=e[r](function(){a.each(function(){var e=t(this),i=e.width(),o=e.height(),n=t.data(this,d);i===n.w&&o===n.h||e.trigger(l,[n.w=i,n.h=o])}),o()},s[h])}var n,a=t([]),s=t.resize=t.extend(t.resize,{}),r="setTimeout",l="resize",d=l+"-special-event",h="delay",p="throttleWindow";s[h]=250,s[p]=!0,t.event.special[l]={setup:function(){if(!s[p]&&this[r])return!1;var e=t(this);a=a.add(e),t.data(this,d,{w:e.width(),h:e.height()}),1===a.length&&o()},teardown:function(){if(!s[p]&&this[r])return!1;var e=t(this);a=a.not(e),e.removeData(d),a.length||clearTimeout(n)},add:function(e){function o(e,o,a){var s=t(this),r=t.data(this,d)||{};r.w=o!==i?o:s.width(),r.h=a!==i?a:s.height(),n.apply(this,arguments)}if(!s[p]&&this[r])return!1;var n;return t.isFunction(e)?(n=e,o):(n=e.handler,void(e.handler=o))}}}(jQuery,this),function(t,e){"use strict";var i,o,n="localStorage",a="page_"+t.location.pathname+t.location.search,s=function(){this.slience=!0;try{n in t&&t[n]&&t[n].setItem&&(this.enable=!0,i=t[n])}catch(s){}this.enable||(o={},i={getLength:function(){var t=0;return e.each(o,function(){t++}),t},key:function(t){var i,n=0;return e.each(o,function(e){return n===t?(i=e,!1):void n++}),i},removeItem:function(t){delete o[t]},getItem:function(t){return o[t]},setItem:function(t,e){o[t]=e},clear:function(){o={}}}),this.storage=i,this.page=this.get(a,{})};s.prototype.pageSave=function(){if(e.isEmptyObject(this.page))this.remove(a);else{var t,i=[];for(t in this.page){var o=this.page[t];null===o&&i.push(t)}for(t=i.length-1;t>=0;t--)delete this.page[i[t]];this.set(a,this.page)}},s.prototype.pageRemove=function(t){"undefined"!=typeof this.page[t]&&(this.page[t]=null,this.pageSave())},s.prototype.pageClear=function(){this.page={},this.pageSave()},s.prototype.pageGet=function(t,e){var i=this.page[t];return void 0===e||null!==i&&void 0!==i?i:e},s.prototype.pageSet=function(t,i){e.isPlainObject(t)?e.extend(!0,this.page,t):this.page[this.serialize(t)]=i,this.pageSave()},s.prototype.check=function(){if(!this.enable&&!this.slience)throw new Error("Browser not support localStorage or enable status been set true.");return this.enable},s.prototype.length=function(){return this.check()?i.getLength?i.getLength():i.length:0},s.prototype.removeItem=function(t){return i.removeItem(t),this},s.prototype.remove=function(t){return this.removeItem(t)},s.prototype.getItem=function(t){return i.getItem(t)},s.prototype.get=function(t,e){var i=this.deserialize(this.getItem(t));return"undefined"!=typeof i&&null!==i||"undefined"==typeof e?i:e},s.prototype.key=function(t){return i.key(t)},s.prototype.setItem=function(t,e){return i.setItem(t,e),this},s.prototype.set=function(t,e){return void 0===e?this.remove(t):(this.setItem(t,this.serialize(e)),this)},s.prototype.clear=function(){return i.clear(),this},s.prototype.forEach=function(t){for(var e=this.length(),o=e-1;o>=0;o--){var n=i.key(o);t(n,this.get(n))}return this},s.prototype.getAll=function(){var t={};return this.forEach(function(e,i){t[e]=i}),t},s.prototype.serialize=function(t){return"string"==typeof t?t:JSON.stringify(t)},s.prototype.deserialize=function(t){if("string"==typeof t)try{return JSON.parse(t)}catch(e){return t||void 0}},e.zui({store:new s})}(window,jQuery),+function(t){"use strict";var e="zui.tab",i=function(e){this.element=t(e)};i.prototype.show=function(){var i=this.element,o=i.closest("ul:not(.dropdown-menu)"),n=i.attr("data-target")||i.attr("data-tab");if(n||(n=i.attr("href"),n=n&&n.replace(/.*(?=#[^\s]*$)/,"")),!i.parent("li").hasClass("active")){var a=o.find(".active:last a")[0],s=t.Event("show."+e,{relatedTarget:a});if(i.trigger(s),!s.isDefaultPrevented()){var r=t(n);this.activate(i.parent("li"),o),this.activate(r,r.parent(),function(){i.trigger({type:"shown."+e,relatedTarget:a})})}}},i.prototype.activate=function(e,i,o){function n(){a.removeClass("active").find("> .dropdown-menu > .active").removeClass("active"),e.addClass("active"),s?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu")&&e.closest("li.dropdown").addClass("active"),o&&o()}var a=i.find("> .active"),s=o&&t.support.transition&&a.hasClass("fade");s?a.one(t.support.transition.end,n).emulateTransitionEnd(150):n(),a.removeClass("in")};var o=t.fn.tab;t.fn.tab=function(o){return this.each(function(){var n=t(this),a=n.data(e);a||n.data(e,a=new i(this)),"string"==typeof o&&a[o]()})},t.fn.tab.Constructor=i,t.fn.tab.noConflict=function(){return t.fn.tab=o,this},t(document).on("click.zui.tab.data-api",'[data-toggle="tab"], [data-tab]',function(e){e.preventDefault(),t(this).tab("show")})}(window.jQuery),+function(t){"use strict";function e(e,n,a){return this.each(function(){var s=t(this),r=s.data(i),l=t.extend({},o.DEFAULTS,s.data(),"object"==typeof e&&e);r||s.data(i,r=new o(this,l)),"string"==typeof e?r[e](n,a):l.show&&r.show(n,a)})}var i="zui.modal",o=function(e,o){var n=this;n.options=o,n.$body=t(document.body),n.$element=t(e),n.$backdrop=n.isShown=null,n.scrollbarWidth=0,void 0===o.moveable&&(n.options.moveable=n.$element.hasClass("modal-moveable")),o.remote&&n.$element.find(".modal-content").load(o.remote,function(){n.$element.trigger("loaded."+i)})};o.VERSION="3.2.0",o.TRANSITION_DURATION=300,o.BACKDROP_TRANSITION_DURATION=150,o.DEFAULTS={backdrop:!0,keyboard:!0,show:!0,position:"fit"};var n=function(e,i){var o=t(window);i.left=Math.max(0,Math.min(i.left,o.width()-e.outerWidth())),i.top=Math.max(0,Math.min(i.top,o.height()-e.outerHeight())),e.css(i)};o.prototype.toggle=function(t,e){return this.isShown?this.hide():this.show(t,e)},o.prototype.ajustPosition=function(e){var o=this,a=o.options;if("undefined"==typeof e&&(e=a.position),"undefined"!=typeof e){var s=o.$element.find(".modal-dialog"),r=Math.max(0,(t(window).height()-s.outerHeight())/2),l="fit"==e?2*r/3:"center"==e?r:e;if(s.hasClass("modal-moveable")){var d=null,h=a.rememberPos;h&&(h===!0?d=o.$element.data("modal-pos"):t.zui.store&&(d=t.zui.store.pageGet(i+".rememberPos."+h))),d||(d={left:Math.max(0,(t(window).width()-s.outerWidth())/2),top:l}),"inside"===a.moveable?n(s,d):s.css(d)}else s.css("margin-top",l)}},o.prototype.setMoveale=function(){t.fn.draggable||console.error("Moveable modal requires draggable.js.");var e=this,o=e.options,a=e.$element.find(".modal-dialog").removeClass("modal-dragged");a.toggleClass("modal-moveable",!!o.moveable),e.$element.data("modal-moveable-setup")||a.draggable({container:e.$element,handle:".modal-header",before:function(){a.css("margin-top","").addClass("modal-dragged")},finish:function(n){var a=o.rememberPos;a&&(e.$element.data("modal-pos",n.pos),t.zui.store&&a!==!0&&t.zui.store.pageSet(i+".rememberPos."+a,n.pos))},move:"inside"!==o.moveable||function(t){n(a,t)}})},o.prototype.show=function(e,n){var a=this,s=t.Event("show."+i,{relatedTarget:e});a.$element.trigger(s),a.isShown||s.isDefaultPrevented()||(a.isShown=!0,a.options.moveable&&a.setMoveale(),a.checkScrollbar(),a.$body.addClass("modal-open"),a.setScrollbar(),a.escape(),a.$element.on("click.dismiss."+i,'[data-dismiss="modal"]',t.proxy(a.hide,a)),a.backdrop(function(){var s=t.support.transition&&a.$element.hasClass("fade");a.$element.parent().length||a.$element.appendTo(a.$body),a.$element.show().scrollTop(0),s&&a.$element[0].offsetWidth,a.$element.addClass("in").attr("aria-hidden",!1),a.ajustPosition(n),a.enforceFocus();var r=t.Event("shown."+i,{relatedTarget:e});s?a.$element.find(".modal-dialog").one("bsTransitionEnd",function(){a.$element.trigger("focus").trigger(r)}).emulateTransitionEnd(o.TRANSITION_DURATION):a.$element.trigger("focus").trigger(r)}))},o.prototype.hide=function(e){e&&e.preventDefault(),e=t.Event("hide."+i),this.$element.trigger(e),this.isShown&&!e.isDefaultPrevented()&&(this.isShown=!1,this.$body.removeClass("modal-open"),this.resetScrollbar(),this.escape(),t(document).off("focusin."+i),this.$element.removeClass("in").attr("aria-hidden",!0).off("click.dismiss."+i),t.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(o.TRANSITION_DURATION):this.hideModal())},o.prototype.enforceFocus=function(){t(document).off("focusin."+i).on("focusin."+i,t.proxy(function(t){this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")},this))},o.prototype.escape=function(){this.isShown&&this.options.keyboard?t(document).on("keydown.dismiss."+i,t.proxy(function(e){if(27==e.which){var o=t.Event("escaping."+i),n=this.$element.triggerHandler(o,"esc");if(void 0!=n&&!n)return;this.hide()}},this)):this.isShown||t(document).off("keydown.dismiss."+i)},o.prototype.hideModal=function(){var t=this;this.$element.hide(),this.backdrop(function(){t.$element.trigger("hidden."+i)})},o.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},o.prototype.backdrop=function(e){var n=this,a=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var s=t.support.transition&&a;if(this.$backdrop=t('<div class="modal-backdrop '+a+'" />').appendTo(this.$body),this.$element.on("mousedown.dismiss."+i,t.proxy(function(t){t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus.call(this.$element[0]):this.hide.call(this))},this)),s&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;s?this.$backdrop.one("bsTransitionEnd",e).emulateTransitionEnd(o.BACKDROP_TRANSITION_DURATION):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var r=function(){n.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",r).emulateTransitionEnd(o.BACKDROP_TRANSITION_DURATION):r()}else e&&e()},o.prototype.checkScrollbar=function(){document.body.clientWidth>=window.innerWidth||(this.scrollbarWidth=this.scrollbarWidth||this.measureScrollbar())},o.prototype.setScrollbar=function(){var t=parseInt(this.$body.css("padding-right")||0,10);this.scrollbarWidth&&this.$body.css("padding-right",t+this.scrollbarWidth)},o.prototype.resetScrollbar=function(){this.$body.css("padding-right","")},o.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var a=t.fn.modal;t.fn.modal=e,t.fn.modal.Constructor=o,t.fn.modal.noConflict=function(){return t.fn.modal=a,this},t(document).on("click."+i+".data-api",'[data-toggle="modal"]',function(o){var n=t(this),a=n.attr("href"),s=null;try{s=t(n.attr("data-target")||a&&a.replace(/.*(?=#[^\s]+$)/,""))}catch(r){return}if(s.length){var l=s.data(i)?"toggle":t.extend({remote:!/#/.test(a)&&a},s.data(),n.data());n.is("a")&&o.preventDefault(),s.one("show."+i,function(t){t.isDefaultPrevented()||s.one("hidden."+i,function(){n.is(":visible")&&n.trigger("focus")})}),e.call(s,l,this,n.data("position"))}})}(jQuery),function(t,e){"use strict";if(!t.fn.modal)throw new Error("Modal trigger requires modal.js");var i="zui.modaltrigger",o="ajax",n=".zui.modal",a="string",s=function(e,i){e=t.extend({},s.DEFAULTS,t.ModalTriggerDefaults,i?i.data():null,e),this.isShown,this.$trigger=i,this.options=e,this.id=t.zui.uuid()};s.DEFAULTS={type:"custom",height:"auto",name:"triggerModal",fade:!0,position:"fit",showHeader:!0,delay:0,backdrop:!0,keyboard:!0,waittime:0,loadingIcon:"icon-spinner-indicator"},s.prototype.init=function(s){var r=this;if(s.url&&(!s.type||s.type!=o&&"iframe"!=s.type)&&(s.type=o),s.remote)s.type=o,typeof s.remote===a&&(s.url=s.remote);else if(s.iframe)s.type="iframe",typeof s.iframe===a&&(s.url=s.iframe);else if(s.custom&&(s.type="custom",typeof s.custom===a)){var l;try{l=t(s.custom)}catch(d){}l&&l.length?s.custom=l:t.isFunction(e[s.custom])&&(s.custom=e[s.custom])}var h=t("#"+s.name);h.length&&(r.isShown||h.off(n),h.remove()),h=t('<div id="'+s.name+'" class="modal modal-trigger '+(s.className||"")+'">'+("string"==typeof s.loadingIcon&&0===s.loadingIcon.indexOf("icon-")?'<div class="icon icon-spin loader '+s.loadingIcon+'"></div>':s.loadingIcon)+'<div class="modal-dialog"><div class="modal-content"><div class="modal-header"><button class="close" data-dismiss="modal">×</button><h4 class="modal-title"><i class="modal-icon"></i> <span class="modal-title-name"></span></h4></div><div class="modal-body"></div></div></div></div>').appendTo("body").data(i,r);var p=function(e,i){var o=s[e];t.isFunction(o)&&h.on(i+n,o)};p("onShow","show"),p("shown","shown"),p("onHide","hide"),p("hidden","hidden"),p("loaded","loaded"),h.on("shown"+n,function(){r.isShown=!0}).on("hidden"+n,function(){r.isShown=!1}),this.$modal=h,this.$dialog=h.find(".modal-dialog"),s.mergeOptions&&(this.options=s)},s.prototype.show=function(n){var s=t.extend({},this.options,{url:this.$trigger?this.$trigger.attr("href")||this.$trigger.attr("data-url")||this.$trigger.data("url"):this.options.url},n);this.init(s);var r=this,l=this.$modal,d=this.$dialog,h=s.custom,p=d.find(".modal-body").css("padding",""),c=d.find(".modal-header"),u=d.find(".modal-content");l.toggleClass("fade",s.fade).addClass(s.className).toggleClass("modal-loading",!this.isShown),d.toggleClass("modal-md","md"===s.size).toggleClass("modal-sm","sm"===s.size).toggleClass("modal-lg","lg"===s.size).toggleClass("modal-fullscreen","fullscreen"===s.size),c.toggle(s.showHeader),c.find(".modal-icon").attr("class","modal-icon icon-"+s.icon),c.find(".modal-title-name").text(s.title||""),s.size&&"fullscreen"===s.size&&(s.width="",s.height="");var f=function(){clearTimeout(this.resizeTask),this.resizeTask=setTimeout(function(){r.ajustPosition()},100)},g=function(t,e){return"undefined"==typeof t&&(t=s.delay),setTimeout(function(){d=l.find(".modal-dialog"),s.width&&"auto"!=s.width&&d.css("width",s.width),s.height&&"auto"!=s.height&&(d.css("height",s.height),"iframe"===s.type&&p.css("height",d.height()-c.outerHeight())),r.ajustPosition(s.position),l.removeClass("modal-loading"),"iframe"!=s.type&&d.off("resize."+i).on("resize."+i,f),e&&e()},t)};if("custom"===s.type&&h)if(t.isFunction(h)){var m=h({modal:l,options:s,modalTrigger:r,ready:g});typeof m===a&&(p.html(m),g())}else h instanceof t?(p.html(t("<div>").append(h.clone()).html()),g()):(p.html(h),g());else if(s.url){var v=function(){var t=l.callComEvent(r,"broken");t&&(p.html(t),g())};if(l.attr("ref",s.url),"iframe"===s.type){l.addClass("modal-iframe"),this.firstLoad=!0;var y="iframe-"+s.name;c.detach(),p.detach(),u.empty().append(c).append(p),p.css("padding",0).html('<iframe id="'+y+'" name="'+y+'" src="'+s.url+'" frameborder="no"  allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"  allowtransparency="true" scrolling="auto" style="width: 100%; height: 100%; left: 0px;"></iframe>'),s.waittime>0&&(r.waitTimeout=g(s.waittime,v));var w=document.getElementById(y);w.onload=w.onreadystatechange=function(){if(r.firstLoad&&l.addClass("modal-loading"),!this.readyState||"complete"==this.readyState){r.firstLoad=!1,s.waittime>0&&clearTimeout(r.waitTimeout);try{l.attr("ref",w.contentWindow.location.href);var t=e.frames[y].$;if(t&&"auto"===s.height&&"fullscreen"!=s.size){var o=t("body").addClass("body-modal");s.iframeBodyClass&&o.addClass(s.iframeBodyClass);var n=function(t){l.removeClass("fade");var e=o.outerHeight();t===!0&&s.onlyIncreaseHeight&&(e=Math.max(e,p.data("minModalHeight")||0),p.data("minModalHeight",e)),p.css("height",e),s.fade&&l.addClass("fade"),g()};l.callComEvent(r,"loaded",{modalType:"iframe",jQuery:t}),setTimeout(n,100),o.off("resize."+i).on("resize."+i,f)}else g();t.extend({closeModal:e.closeModal})}catch(a){g()}}}}else t.ajax(t.extend({url:s.url,success:function(e){try{var i=t(e);i.hasClass("modal-dialog")?d.replaceWith(i):i.hasClass("modal-content")?d.find(".modal-content").replaceWith(i):p.wrapInner(i)}catch(n){l.html(e)}l.callComEvent(r,"loaded",{modalType:o}),g()},error:v},s.ajaxOptions))}l.modal({show:"show",backdrop:s.backdrop,moveable:s.moveable,rememberPos:s.rememberPos,keyboard:s.keyboard})},s.prototype.close=function(i,o){(i||o)&&this.$modal.on("hidden"+n,function(){t.isFunction(i)&&i(),typeof o===a&&("this"===o?e.location.reload():e.location=o)}),this.$modal.modal("hide")},s.prototype.toggle=function(t){this.isShown?this.close():this.show(t)},s.prototype.ajustPosition=function(t){this.$modal.modal("ajustPosition",t||this.options.position)},t.zui({ModalTrigger:s,modalTrigger:new s}),t.fn.modalTrigger=function(e,o){return t(this).each(function(){var n=t(this),r=n.data(i),l=t.extend({title:n.attr("title")||n.text(),url:n.attr("href"),type:n.hasClass("iframe")?"iframe":""},n.data(),t.isPlainObject(e)&&e);r||n.data(i,r=new s(l,n)),typeof e==a?r[e](o):l.show&&r.show(o),n.on((l.trigger||"click")+".toggle."+i,function(t){r.toggle(l),n.is("a")&&t.preventDefault()})})};var r=t.fn.modal;t.fn.modal=function(e,i){return t(this).each(function(){var o=t(this);o.hasClass("modal")?r.call(o,e,i):o.modalTrigger(e,i)})};var l=function(e){var i=typeof e;return"undefined"===i?e=t(".modal.modal-trigger"):i===a&&(e=t(e)),e&&e instanceof t?e:null},d=function(e,o,n){if(t.isFunction(e)){var a=n;n=o,o=e,e=a}e=l(e),e&&e.length&&e.each(function(){t(this).data(i).close(o,n)})},h=function(t,e){e=l(e),e&&e.length&&e.modal("ajustPosition",t)};t.zui({closeModal:d,ajustModalPosition:h}),t(document).on("click."+i+".data-api",'[data-toggle="modal"]',function(e){var o=t(this),n=o.attr("href"),a=null;try{a=t(o.attr("data-target")||n&&n.replace(/.*(?=#[^\s]+$)/,""))}catch(s){}a&&a.length||(o.data(i)?o.trigger(".toggle."+i):o.modalTrigger({show:!0})),o.is("a")&&e.preventDefault()})}(window.jQuery,window),+function(t){"use strict";var e=function(t,e){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.init("tooltip",t,e)};e.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1},e.prototype.init=function(e,i,o){this.enabled=!0,this.type=e,this.$element=t(i),this.options=this.getOptions(o);for(var n=this.options.trigger.split(" "),a=n.length;a--;){var s=n[a];if("click"==s)this.$element.on("click."+this.type,this.options.selector,t.proxy(this.toggle,this));else if("manual"!=s){var r="hover"==s?"mouseenter":"focus",l="hover"==s?"mouseleave":"blur";this.$element.on(r+"."+this.type,this.options.selector,t.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,t.proxy(this.leave,this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.getOptions=function(e){return e=t.extend({},this.getDefaults(),this.$element.data(),e),e.delay&&"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),e},e.prototype.getDelegateOptions=function(){var e={},i=this.getDefaults();return this._options&&t.each(this._options,function(t,o){i[t]!=o&&(e[t]=o)}),e},e.prototype.enter=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget)[this.type](this.getDelegateOptions()).data("zui."+this.type);return clearTimeout(i.timeout),i.hoverState="in",i.options.delay&&i.options.delay.show?void(i.timeout=setTimeout(function(){"in"==i.hoverState&&i.show()},i.options.delay.show)):i.show()},e.prototype.leave=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget)[this.type](this.getDelegateOptions()).data("zui."+this.type);return clearTimeout(i.timeout),i.hoverState="out",i.options.delay&&i.options.delay.hide?void(i.timeout=setTimeout(function(){"out"==i.hoverState&&i.hide()},i.options.delay.hide)):i.hide()},e.prototype.show=function(e){var i=t.Event("show.zui."+this.type);if((e||this.hasContent())&&this.enabled){var o=this;if(o.$element.trigger(i),i.isDefaultPrevented())return;var n=o.tip();o.setContent(e),o.options.animation&&n.addClass("fade");var a="function"==typeof o.options.placement?o.options.placement.call(o,n[0],o.$element[0]):o.options.placement,s=/\s?auto?\s?/i,r=s.test(a);r&&(a=a.replace(s,"")||"top"),n.detach().css({top:0,left:0,display:"block"}).addClass(a),o.options.container?n.appendTo(o.options.container):n.insertAfter(o.$element);var l=o.getPosition(),d=n[0].offsetWidth,h=n[0].offsetHeight;if(r){var p=o.$element.parent(),c=a,u=document.documentElement.scrollTop||document.body.scrollTop,f="body"==o.options.container?window.innerWidth:p.outerWidth(),g="body"==o.options.container?window.innerHeight:p.outerHeight(),m="body"==o.options.container?0:p.offset().left;a="bottom"==a&&l.top+l.height+h-u>g?"top":"top"==a&&l.top-u-h<0?"bottom":"right"==a&&l.right+d>f?"left":"left"==a&&l.left-d<m?"right":a,n.removeClass(c).addClass(a)}var v=o.getCalculatedOffset(a,l,d,h);o.applyPlacement(v,a);var y=function(){var t=o.hoverState;o.$element.trigger("shown.zui."+o.type),o.hoverState=null,"out"==t&&o.leave(o)};t.support.transition&&o.$tip.hasClass("fade")?n.one("bsTransitionEnd",y).emulateTransitionEnd(150):y()}},e.prototype.applyPlacement=function(t,e){var i,o=this.tip(),n=o[0].offsetWidth,a=o[0].offsetHeight,s=parseInt(o.css("margin-top"),10),r=parseInt(o.css("margin-left"),10);isNaN(s)&&(s=0),isNaN(r)&&(r=0),t.top=t.top+s,t.left=t.left+r,o.offset(t).addClass("in");var l=o[0].offsetWidth,d=o[0].offsetHeight;if("top"==e&&d!=a&&(i=!0,t.top=t.top+a-d),/bottom|top/.test(e)){var h=0;t.left<0&&(h=t.left*-2,t.left=0,o.offset(t),l=o[0].offsetWidth,d=o[0].offsetHeight),this.replaceArrow(h-n+l,l,"left")}else this.replaceArrow(d-a,d,"top");i&&o.offset(t)},e.prototype.replaceArrow=function(t,e,i){this.arrow().css(i,t?50*(1-t/e)+"%":"")},e.prototype.setContent=function(t){var e=this.tip(),i=t||this.getTitle();this.options.tipId&&e.attr("id",this.options.tipId),this.options.tipClass&&e.addClass(this.options.tipClass),e.find(".tooltip-inner")[this.options.html?"html":"text"](i),e.removeClass("fade in top bottom left right")},e.prototype.hide=function(){function e(){"in"!=i.hoverState&&o.detach()}var i=this,o=this.tip(),n=t.Event("hide.zui."+this.type);if(this.$element.trigger(n),!n.isDefaultPrevented())return o.removeClass("in"),t.support.transition&&this.$tip.hasClass("fade")?o.one(t.support.transition.end,e).emulateTransitionEnd(150):e(),this.$element.trigger("hidden.zui."+this.type),this},e.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},e.prototype.hasContent=function(){return this.getTitle()},e.prototype.getPosition=function(){var e=this.$element[0];return t.extend({},"function"==typeof e.getBoundingClientRect?e.getBoundingClientRect():{width:e.offsetWidth,height:e.offsetHeight},this.$element.offset())},e.prototype.getCalculatedOffset=function(t,e,i,o){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-i/2}:"top"==t?{top:e.top-o,left:e.left+e.width/2-i/2}:"left"==t?{top:e.top+e.height/2-o/2,left:e.left-i}:{top:e.top+e.height/2-o/2,left:e.left+e.width}},e.prototype.getTitle=function(){var t,e=this.$element,i=this.options;return t=e.attr("data-original-title")||("function"==typeof i.title?i.title.call(e[0]):i.title)},e.prototype.tip=function(){return this.$tip=this.$tip||t(this.options.template)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},e.prototype.validate=function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)},e.prototype.enable=function(){this.enabled=!0},e.prototype.disable=function(){this.enabled=!1},e.prototype.toggleEnabled=function(){this.enabled=!this.enabled},e.prototype.toggle=function(e){var i=e?t(e.currentTarget)[this.type](this.getDelegateOptions()).data("zui."+this.type):this;i.tip().hasClass("in")?i.leave(i):i.enter(i)},e.prototype.destroy=function(){this.hide().$element.off("."+this.type).removeData("zui."+this.type)};var i=t.fn.tooltip;t.fn.tooltip=function(i,o){return this.each(function(){var n=t(this),a=n.data("zui.tooltip"),s="object"==typeof i&&i;a||n.data("zui.tooltip",a=new e(this,s)),"string"==typeof i&&a[i](o)})},t.fn.tooltip.Constructor=e,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=i,this}}(window.jQuery),+function(t){"use strict";var e=function(t,e){this.init("popover",t,e)};if(!t.fn.tooltip)throw new Error("Popover requires tooltip.js");e.DEFAULTS=t.extend({},t.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),e.prototype=t.extend({},t.fn.tooltip.Constructor.prototype),e.prototype.constructor=e,e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.setContent=function(){var t=this.tip(),e=this.getTarget();if(e)return e.find(".arrow").length<1&&t.addClass("no-arrow"),void t.html(e.html());var i=this.getTitle(),o=this.getContent();t.find(".popover-title")[this.options.html?"html":"text"](i),t.find(".popover-content")[this.options.html?"html":"text"](o),t.removeClass("fade top bottom left right in"),this.options.tipId&&t.attr("id",this.options.tipId),this.options.tipClass&&t.addClass(this.options.tipClass),t.find(".popover-title").html()||t.find(".popover-title").hide()},e.prototype.hasContent=function(){return this.getTarget()||this.getTitle()||this.getContent()},e.prototype.getContent=function(){var t=this.$element,e=this.options;return t.attr("data-content")||("function"==typeof e.content?e.content.call(t[0]):e.content)},e.prototype.getTarget=function(){var e=this.$element,i=this.options,o=e.attr("data-target")||("function"==typeof i.target?i.target.call(e[0]):i.target);return!!o&&("$next"==o?e.next(".popover"):t(o))},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")},e.prototype.tip=function(){return this.$tip||(this.$tip=t(this.options.template)),this.$tip};var i=t.fn.popover;t.fn.popover=function(i){return this.each(function(){var o=t(this),n=o.data("zui.popover"),a="object"==typeof i&&i;n||o.data("zui.popover",n=new e(this,a)),"string"==typeof i&&n[i]()})},t.fn.popover.Constructor=e,t.fn.popover.noConflict=function(){return t.fn.popover=i,this}}(window.jQuery),+function(t){"use strict";function e(){t(n).remove(),t(a).each(function(e){var n=i(t(this));n.hasClass("open")&&(n.trigger(e=t.Event("hide."+o)),e.isDefaultPrevented()||n.removeClass("open").trigger("hidden."+o))})}function i(e){var i=e.attr("data-target");i||(i=e.attr("href"),i=i&&/#/.test(i)&&i.replace(/.*(?=#[^\s]*$)/,""));var o;try{o=i&&t(i)}catch(n){}return o&&o.length?o:e.parent()}var o="zui.dropdown",n=".dropdown-backdrop",a="[data-toggle=dropdown]",s=function(e){t(e).on("click."+o,this.toggle)};s.prototype.toggle=function(n){var a=t(this);if(!a.is(".disabled, :disabled")){var s=i(a),r=s.hasClass("open");if(e(),!r){if("ontouchstart"in document.documentElement&&!s.closest(".navbar-nav").length&&t('<div class="dropdown-backdrop"/>').insertAfter(t(this)).on("click",e),s.trigger(n=t.Event("show."+o)),n.isDefaultPrevented())return;s.toggleClass("open").trigger("shown."+o),a.focus()}return!1}},s.prototype.keydown=function(e){if(/(38|40|27)/.test(e.keyCode)){var o=t(this);if(e.preventDefault(),e.stopPropagation(),!o.is(".disabled, :disabled")){var n=i(o),s=n.hasClass("open");if(!s||s&&27==e.keyCode)return 27==e.which&&n.find(a).focus(),o.click();var r=t("[role=menu] li:not(.divider):visible a",n);if(r.length){var l=r.index(r.filter(":focus"));38==e.keyCode&&l>0&&l--,40==e.keyCode&&l<r.length-1&&l++,~l||(l=0),r.eq(l).focus()}}}};var r=t.fn.dropdown;t.fn.dropdown=function(e){return this.each(function(){var i=t(this),o=i.data("dropdown");o||i.data("dropdown",o=new s(this)),"string"==typeof e&&o[e].call(i)})},t.fn.dropdown.Constructor=s,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=r,this};var l=o+".data-api";t(document).on("click."+l,e).on("click."+l,".dropdown form",function(t){t.stopPropagation()}).on("click."+l,a,s.prototype.toggle).on("keydown."+l,a+", [role=menu]",s.prototype.keydown)}(window.jQuery),+function(t){"use strict";var e=function(e,i){this.$element=t(e),this.$indicators=this.$element.find(".carousel-indicators"),this.options=i,this.paused=this.sliding=this.interval=this.$active=this.$items=null,"hover"==this.options.pause&&this.$element.on("mouseenter",t.proxy(this.pause,this)).on("mouseleave",t.proxy(this.cycle,this))};e.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,touchable:!0},e.prototype.touchable=function(){function e(e){var e=e||window.event;e.originalEvent&&(e=e.originalEvent);var a=t(this);switch(e.type){case"touchstart":o=e.touches[0].pageX,n=e.touches[0].pageY;break;case"touchend":var s=e.changedTouches[0].pageX-o,r=e.changedTouches[0].pageY-n;if(Math.abs(s)>Math.abs(r))i(a,s),Math.abs(s)>10&&e.preventDefault();else{var l=t(window);t("body,html").animate({scrollTop:l.scrollTop()-r},400)}}}function i(t,e){e>10?a.prev():e<-10&&a.next()}if(this.options.touchable){this.$element.on("touchstart touchmove touchend",e);var o,n,a=this}},e.prototype.cycle=function(e){return e||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(t.proxy(this.next,this),this.options.interval)),this},e.prototype.getActiveIndex=function(){return this.$active=this.$element.find(".item.active"),this.$items=this.$active.parent().children(),this.$items.index(this.$active)},e.prototype.to=function(e){var i=this,o=this.getActiveIndex();if(!(e>this.$items.length-1||e<0))return this.sliding?this.$element.one("slid",function(){i.to(e)}):o==e?this.pause().cycle():this.slide(e>o?"next":"prev",t(this.$items[e]))},e.prototype.pause=function(e){return e||(this.paused=!0),this.$element.find(".next, .prev").length&&t.support.transition.end&&(this.$element.trigger(t.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},e.prototype.next=function(){if(!this.sliding)return this.slide("next")},e.prototype.prev=function(){if(!this.sliding)return this.slide("prev")},e.prototype.slide=function(e,i){var o=this.$element.find(".item.active"),n=i||o[e](),a=this.interval,s="next"==e?"left":"right",r="next"==e?"first":"last",l=this;if(!n.length){if(!this.options.wrap)return;n=this.$element.find(".item")[r]()}this.sliding=!0,a&&this.pause();var d=t.Event("slide.zui.carousel",{relatedTarget:n[0],direction:s});if(!n.hasClass("active")){if(this.$indicators.length&&(this.$indicators.find(".active").removeClass("active"),this.$element.one("slid",function(){var e=t(l.$indicators.children()[l.getActiveIndex()]);e&&e.addClass("active")})),t.support.transition&&this.$element.hasClass("slide")){if(this.$element.trigger(d),d.isDefaultPrevented())return;n.addClass(e),n[0].offsetWidth,o.addClass(s),n.addClass(s),o.one(t.support.transition.end,function(){n.removeClass([e,s].join(" ")).addClass("active"),o.removeClass(["active",s].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger("slid")},0)}).emulateTransitionEnd(600)}else{if(this.$element.trigger(d),d.isDefaultPrevented())return;o.removeClass("active"),n.addClass("active"),this.sliding=!1,this.$element.trigger("slid")}return a&&this.cycle(),this}};var i=t.fn.carousel;t.fn.carousel=function(i){return this.each(function(){var o=t(this),n=o.data("zui.carousel"),a=t.extend({},e.DEFAULTS,o.data(),"object"==typeof i&&i),s="string"==typeof i?i:a.slide;n||o.data("zui.carousel",n=new e(this,a)),"number"==typeof i?n.to(i):s?n[s]():a.interval&&n.pause().cycle(),a.touchable&&n.touchable()})},t.fn.carousel.Constructor=e,t.fn.carousel.noConflict=function(){return t.fn.carousel=i,this},t(document).on("click.zui.carousel.data-api","[data-slide], [data-slide-to]",function(e){var i,o=t(this),n=t(o.attr("data-target")||(i=o.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,"")),a=t.extend({},n.data(),o.data()),s=o.attr("data-slide-to");s&&(a.interval=!1),n.carousel(a),(s=o.attr("data-slide-to"))&&n.data("zui.carousel").to(s),e.preventDefault();
}),t(window).on("load",function(){t('[data-ride="carousel"]').each(function(){var e=t(this);e.carousel(e.data())})})}(window.jQuery);