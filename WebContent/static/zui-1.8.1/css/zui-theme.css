/*!
 * ZUI: Theme - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */

a {
  color: #3949ab;
  }
a:hover,
a:focus {
  color: #263172;
  }
.btn {
  color: #353535;
  background-color: #fff;
  border-color: #ccc;
  border-radius: 0;
  }
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.open .dropdown-toggle.btn {
  color: #353535;
  background-color: #ebebeb;
  border-color: #adadad;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn:active,
.btn.active,
.open .dropdown-toggle.btn {
  background-color: #d9d9d9;
  background-image: none;
  border-color: #b3b3b3;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn,
.btn.disabled:hover,
.btn[disabled]:hover,
fieldset[disabled] .btn:hover,
.btn.disabled:focus,
.btn[disabled]:focus,
fieldset[disabled] .btn:focus,
.btn.disabled:active,
.btn[disabled]:active,
fieldset[disabled] .btn:active,
.btn.disabled.active,
.btn[disabled].active,
fieldset[disabled] .btn.active {
  background-color: #fff;
  border-color: #ccc;
  }
.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.open .dropdown-toggle.btn {
  color: #353535;
  background-color: #ebebeb;
  border-color: #adadad;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn:active,
.btn.active,
.open .dropdown-toggle.btn {
  background-color: #d9d9d9;
  background-image: none;
  border-color: #b3b3b3;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn,
.btn.disabled:hover,
.btn[disabled]:hover,
fieldset[disabled] .btn:hover,
.btn.disabled:focus,
.btn[disabled]:focus,
fieldset[disabled] .btn:focus,
.btn.disabled:active,
.btn[disabled]:active,
fieldset[disabled] .btn:active,
.btn.disabled.active,
.btn[disabled].active,
fieldset[disabled] .btn.active {
  background-color: #fff;
  border-color: #ccc;
  }
.btn-primary {
  color: #fff;
  background-color: #3f51b5;
  border-color: #3e4fb1;
  }
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
  color: #fff;
  background-color: #344397;
  border-color: #2e3b84;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
  background-color: #2b387c;
  background-image: none;
  border-color: #313e8b;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #3f51b5;
  border-color: #3e4fb1;
  }
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
  color: #fff;
  background-color: #344397;
  border-color: #2e3b84;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
  background-color: #2b387c;
  background-image: none;
  border-color: #313e8b;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #3f51b5;
  border-color: #3e4fb1;
  }
.btn-warning {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #f0ab49;
  }
.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.active,
.open .dropdown-toggle.btn-warning {
  color: #fff;
  background-color: #ed9c28;
  border-color: #e89014;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn-warning:active,
.btn-warning.active,
.open .dropdown-toggle.btn-warning {
  background-color: #df8a13;
  background-image: none;
  border-color: #ec951a;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
  background-color: #f0ad4e;
  border-color: #f0ab49;
  }
.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.active,
.open .dropdown-toggle.btn-warning {
  color: #fff;
  background-color: #ed9c28;
  border-color: #e89014;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn-warning:active,
.btn-warning.active,
.open .dropdown-toggle.btn-warning {
  background-color: #df8a13;
  background-image: none;
  border-color: #ec951a;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
  background-color: #f0ad4e;
  border-color: #f0ab49;
  }
.btn-danger {
  color: #fff;
  background-color: #d9534f;
  border-color: #d84f4b;
  }
.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active,
.btn-danger.active,
.open .dropdown-toggle.btn-danger {
  color: #fff;
  background-color: #d2322d;
  border-color: #bd2d29;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn-danger:active,
.btn-danger.active,
.open .dropdown-toggle.btn-danger {
  background-color: #b52b27;
  background-image: none;
  border-color: #c52f2b;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active {
  background-color: #d9534f;
  border-color: #d84f4b;
  }
.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active,
.btn-danger.active,
.open .dropdown-toggle.btn-danger {
  color: #fff;
  background-color: #d2322d;
  border-color: #bd2d29;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn-danger:active,
.btn-danger.active,
.open .dropdown-toggle.btn-danger {
  background-color: #b52b27;
  background-image: none;
  border-color: #c52f2b;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active {
  background-color: #d9534f;
  border-color: #d84f4b;
  }
.btn-success {
  color: #fff;
  background-color: #4caf50;
  border-color: #4aab4e;
  }
.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success {
  color: #fff;
  background-color: #409343;
  border-color: #38813b;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success {
  background-color: #357a38;
  background-image: none;
  border-color: #3b883e;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled:active,
.btn-success[disabled]:active,
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success.active {
  background-color: #4caf50;
  border-color: #4aab4e;
  }
.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success {
  color: #fff;
  background-color: #409343;
  border-color: #38813b;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success {
  background-color: #357a38;
  background-image: none;
  border-color: #3b883e;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled:active,
.btn-success[disabled]:active,
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success.active {
  background-color: #4caf50;
  border-color: #4aab4e;
  }
.btn-info {
  color: #fff;
  background-color: #039be5;
  border-color: #0398e0;
  }
.btn-info:hover,
.btn-info:focus,
.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info {
  color: #fff;
  background-color: #0280bd;
  border-color: #026fa4;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info {
  background-color: #026899;
  background-image: none;
  border-color: #0276ae;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
  background-color: #039be5;
  border-color: #0398e0;
  }
.btn-info:hover,
.btn-info:focus,
.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info {
  color: #fff;
  background-color: #0280bd;
  border-color: #026fa4;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info {
  background-color: #026899;
  background-image: none;
  border-color: #0276ae;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
  background-color: #039be5;
  border-color: #0398e0;
  }
.btn-link {
  color: #3949ab;
  background-color: transparent;
  }
.btn-link,
.btn-link:active,
.btn-link[disabled],
fieldset[disabled] .btn-link,
.btn-link:hover,
.btn-link:focus {
  border-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
  }
.btn-link:hover,
.btn-link:focus {
  color: #263172;
  }
.btn-link[disabled]:hover,
fieldset[disabled] .btn-link:hover,
.btn-link[disabled]:focus,
fieldset[disabled] .btn-link:focus {
  color: #ddd;
  }
.popover,
.tooltip-inner {
  border-radius: 0;
  }
.label {
  border-radius: 0;
  }
.label-badge {
  border-radius: 12px;
  }
.label-dot {
  border-radius: 5px;
  }
.label-primary {
  background-color: #3f51b5;
  }
.label-primary[href]:hover,
.label-primary[href]:focus {
  color: #fff;
  background-color: #32408f;
  }
.label-primary.label-circle {
  color: #3f51b5;
  background: none;
  border: 1px solid #3f51b5;
  }
.label-primary[href]:hover,
.label-primary[href]:focus {
  background-color: #32408f;
  }
.label-success {
  background-color: #4caf50;
  }
.label-success[href]:hover,
.label-success[href]:focus {
  color: #fff;
  background-color: #3d8b40;
  }
.label-success.label-circle {
  color: #4caf50;
  background: none;
  border: 1px solid #4caf50;
  }
.label-success[href]:hover,
.label-success[href]:focus {
  background-color: #3d8b40;
  }
.label-info {
  background-color: #039be5;
  }
.label-info[href]:hover,
.label-info[href]:focus {
  color: #fff;
  background-color: #0279b3;
  }
.label-info.label-circle {
  color: #039be5;
  background: none;
  border: 1px solid #039be5;
  }
.label-info[href]:hover,
.label-info[href]:focus {
  background-color: #0279b3;
  }
.label-warning {
  background-color: #f0ad4e;
  }
.label-warning[href]:hover,
.label-warning[href]:focus {
  color: #fff;
  background-color: #ec971f;
  }
.label-warning.label-circle {
  color: #f0ad4e;
  background: none;
  border: 1px solid #f0ad4e;
  }
.label-warning[href]:hover,
.label-warning[href]:focus {
  background-color: #ec971f;
  }
.label-danger {
  background-color: #d9534f;
  }
.label-danger[href]:hover,
.label-danger[href]:focus {
  color: #fff;
  background-color: #c9302c;
  }
.label-danger.label-circle {
  color: #d9534f;
  background: none;
  border: 1px solid #d9534f;
  }
.label-danger[href]:hover,
.label-danger[href]:focus {
  background-color: #c9302c;
  }
.btn-primary .label-badge,
.btn-primary .label-dot {
  background-color: #8591d5;
  }
.text-primary {
  color: #3f51b5;
  }
.text-primary:hover {
  color: #32408f;
  }
.text-warning {
  color: #f0ad4e;
  }
.text-warning:hover {
  color: #ec971f;
  }
.text-danger {
  color: #d9534f;
  }
.text-danger:hover {
  color: #c9302c;
  }
.text-success {
  color: #4caf50;
  }
.text-success:hover {
  color: #3d8b40;
  }
.text-info {
  color: #039be5;
  }
.text-info:hover {
  color: #0279b3;
  }
.text-important {
  color: #bd7b46;
  }
.text-important:hover {
  color: #996337;
  }
.text-special {
  color: #8666b8;
  }
.text-special:hover {
  color: #6c4aa1;
  }
.text-muted a:hover,
.text-muted a:active {
  color: #3949ab;
  }
.text-link:hover,
.text-link:active {
  color: #3949ab;
  }
.bg-primary {
  background-color: #3f51b5;
  }
a.bg-primary:hover {
  background-color: #32408f;
  }
.hl-primary {
  background-color: #eceff1;
  }
a.hl-primary:hover {
  background-color: #cfd6db;
  }
.form-control {
  border-radius: 0;
  }
.form-control:focus,
.form-control.focus {
  border-color: #3949ab;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(57, 73, 171, .6);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(57, 73, 171, .6);
  }
.form-control:focus {
  border-color: #3949ab;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(57, 73, 171, .6);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(57, 73, 171, .6);
  }
.form-condensed .form-control,
.form-condensed .btn {
  border-radius: 0;
  }
.switch > input:checked + label:before {
  background-color: #3f51b5;
  border-color: #3f51b5;
  }
.switch input:focus + label {
  color: #3f51b5;
  }
.switch input:focus + label:before {
  border-color: #3f51b5;
  }
.form-control:focus + .input-control-label-left {
  color: #3949ab;
  }
.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
  }
.btn-group-vertical > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  }
.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 0;
  }
.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
  }
.btn-group-vertical > .btn-group:first-child > .btn:last-child,
.btn-group-vertical > .btn-group:first-child > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  }
.btn-group-vertical > .btn-group:last-child > .btn:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  }
.input-group-addon {
  background-color: #f1f1f1;
  }
.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
  }
.input-group-addon {
  border-radius: 0;
  }
.input-group-btn.fix-border > .btn {
  border-radius: 0;
  }
.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group > .btn,
.input-group-btn:first-child > .dropdown-toggle,
.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  }
.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group > .btn,
.input-group-btn:last-child > .dropdown-toggle,
.input-group-btn:first-child > .btn:not(:first-child),
.input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  }
.list-group-item {
  background-color: #fff;
  border: 1px solid #ddd;
  }
.list-group-item:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  }
.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  }
a.list-group-item {
  color: #555;
  }
a.list-group-item .list-group-item-heading {
  color: #333;
  }
a.list-group-item:hover,
a.list-group-item:focus {
  color: #353535;
  text-decoration: none;
  background-color: #eceff1;
  }
.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
  color: #fff;
  background-color: #3f51b5;
  border-color: #3f51b5;
  }
.list-group-item.active .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading {
  color: inherit;
  }
.list-group-item.active .list-group-item-text,
.list-group-item.active:hover .list-group-item-text,
.list-group-item.active:focus .list-group-item-text {
  color: #e6e6e6;
  }
.list-group-item-heading {
  color: #333;
  }
.navbar-toggle {
  border-radius: 0;
  }
@media (min-width: 768px) {
  .navbar {
    border-radius: 0;
    }
  }
.navbar-inverse {
  background-color: #3949ab;
  border-color: #2c3985;
  }
.navbar-inverse .navbar-nav > li.nav-heading {
  background-color: #334198;
  }
.navbar-inverse .navbar-nav > li > a:hover,
.navbar-inverse .navbar-nav > li > a:focus {
  background-color: #4254c0;
  }
.navbar-inverse .navbar-nav > .active > a,
.navbar-inverse .navbar-nav > .active > a:hover,
.navbar-inverse .navbar-nav > .active > a:focus {
  background-color: #5262c5;
  }
.navbar-inverse .navbar-toggle {
  border-color: #5262c5;
  }
.navbar-inverse .navbar-toggle:hover,
.navbar-inverse .navbar-toggle:focus {
  background-color: #334198;
  }
.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
  border-color: #303e90;
  }
.navbar-inverse .navbar-nav > .open > a,
.navbar-inverse .navbar-nav > .open > a:hover,
.navbar-inverse .navbar-nav > .open > a:focus {
  background-color: #5262c5;
  }
@media (max-width: 767px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
    border-color: #2c3985;
    }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
    background-color: #4254c0;
    }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
    background-color: #5262c5;
    }
  }
.navbar-fixed-left.navbar-inverse .navbar-header,
.navbar-fixed-left.navbar-inverse .navbar-nav {
  border-bottom: 1px solid #5262c5;
  -webkit-box-shadow: inset 0 -1px 0 #2c3985;
          box-shadow: inset 0 -1px 0 #2c3985;
  }
.navbar-fixed-left.navbar-inverse.navbar-collapsed .navbar-nav > li.nav-heading:hover {
  color: #eaeaea;
  background-color: #2f3c8c;
  }
.nav > li > a:hover,
.nav > li > a:focus {
  color: #263172;
  }
.nav .open > a,
.nav .open > a:hover,
.nav .open > a:focus {
  border-color: #3949ab;
  }
.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus {
  background-color: #3f51b5;
  }
.nav-primary > li:first-child > a {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  }
.nav-primary > li:last-child > a {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  }
.nav-primary > li > a {
  border: 1px solid #ddd;
  }
.nav-primary > li.active > a,
.nav-primary > li.active > a:hover,
.nav-primary > li.active > a:focus {
  color: #fff;
  background-color: #3f51b5;
  border-color: #3f51b5;
  }
.nav-secondary > li.active > a,
.nav-secondary > li.active > a:hover,
.nav-secondary > li.active > a:focus {
  color: #3f51b5;
  border-bottom-color: #3f51b5;
  }
.nav-stacked.nav-primary > li,
.nav-stacked.nav-primary > li > a {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  }
.nav-stacked.nav-primary > li:first-child > a,
.nav-stacked.nav-primary > li:first-child.nav-heading {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  }
.nav-stacked.nav-primary > li:last-child > a {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  }
.nav-stacked.nav-primary > li.nav-heading {
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  }
.nav-stacked.nav-secondary > li > a {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  -webkit-box-shadow: inset 2px 0 0 #f5f5f5;
          box-shadow: inset 2px 0 0 #f5f5f5;
  }
.nav-stacked.nav-secondary > li > a:hover {
  -webkit-box-shadow: inset 2px 0 0 #e5e5e5;
          box-shadow: inset 2px 0 0 #e5e5e5;
  }
.nav-stacked.nav-secondary > li.active > a,
.nav-stacked.nav-secondary > li.active > a:hover,
.nav-stacked.nav-secondary > li.active > a:focus {
  background-color: #f5f5f5;
  -webkit-box-shadow: inset 2px 0 0 #3f51b5;
          box-shadow: inset 2px 0 0 #3f51b5;
  }
.nav-stacked.nav-secondary > li.nav-heading {
  border-bottom: 1px solid #ddd;
  }
.nav-tabs > li > a {
  border-radius: 0 0 0 0;
  }
.pager {
  border-radius: 0;
  }
.pager > li > a,
.pager > li > span {
  background-color: #fff;
  border: 1px solid #ddd;
  }
.pager > li:first-child > a,
.pager > li:first-child > span {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  }
.pager > li:last-child > a,
.pager > li:last-child > span {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  }
.pager > li > a:hover,
.pager > li > span:hover,
.pager > li > a:focus,
.pager > li > span:focus {
  background-color: #e5e5e5;
  }
.pager > li.active > a,
.pager > li.active > span,
.pager > li.active > a:hover,
.pager > li.active > span:hover {
  color: #fff;
  background-color: #3f51b5;
  border-color: #3f51b5;
  }
.pager > li.disabled > span,
.pager > li.disabled > span:hover,
.pager > li.disabled > span:focus,
.pager > li.disabled > a,
.pager > li.disabled > a:hover,
.pager > li.disabled > a:focus {
  color: #ddd;
  background-color: #fff;
  border-color: #ddd;
  }
.panel {
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  }
.panel > .panel-heading {
  background-image: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  }
.panel-group .panel {
  border-radius: 0;
  }
.dashboard .panel {
  -webkit-box-shadow: none;
          box-shadow: none;
  }
.panel-primary {
  border-color: #3f51b5;
  }
.panel-primary > .panel-heading {
  color: #fff;
  background-color: #3f51b5;
  border-color: #3f51b5;
  }
.panel-primary > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #3f51b5;
  }
.panel-primary > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #3f51b5;
  }
.panel-success > .panel-body,
.panel-warning > .panel-body,
.panel-primary > .panel-body,
.panel-info > .panel-body,
.panel-danger > .panel-body {
  -webkit-box-shadow: none;
          box-shadow: none;
  }
.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th {
  background-color: #eceff1;
  }
.table tr > td.active,
.table tr > th.active,
.table tr.active > td,
.table tr.active > th {
  background-color: #ffe7bc;
  }
table.tablesorter thead tr .headerSortUp:after,
table.tablesorter thead tr .headerSortDown:after {
  color: #3949ab !important;
  }
.chosen-container-single .chosen-search:focus,
.chosen-container-single .chosen-search input[type="text"]:focus {
  border-color: #3949ab !important;
  }
.chosen-container-single .chosen-single {
  border-radius: 0 !important;
  }
.chosen-container-single.chosen-with-drop .chosen-single {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  }
.chosen-container-single.chosen-with-drop.chosen-up .chosen-single {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: #3949ab !important;
  border-bottom-left-radius: #3949ab !important;
  }
.chosen-container .chosen-results li.highlighted {
  background-color: #3f51b5 !important;
  }
.chosen-container-active .chosen-single {
  border-color: #3949ab !important;
  }
.chosen-container-active.chosen-with-drop .chosen-single {
  border-color: #cbcbcb !important;
  border-color: rgba(0, 0, 0, .15) !important;
  }
.chosen-container-active .chosen-choices {
  border-color: #3949ab !important;
  }
.chosen-container-multi .chosen-choices {
  border-radius: 0 !important;
  }
.chosen-container-multi.chosen-with-drop .chosen-choices {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  }
.chosen-container-multi.chosen-with-drop.chosen-up .chosen-choices {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: #3949ab !important;
  border-bottom-left-radius: #3949ab !important;
  }
.menu > .nav > li > .nav {
  background-color: #f6f6f6;
  }
.menu > .nav > li > .nav > li > a {
  border: 1px solid #ddd;
  }
.menu > .nav > li > .nav > li > a:hover,
.menu > .nav > li > .nav > li > a:focus {
  background-color: #e4e4e4;
  }
.menu > .nav > li > .nav > li.active > a,
.menu > .nav > li > .nav > li.active > a:hover,
.menu > .nav > li > .nav > li.active > a:focus {
  color: #fff;
  background-color: #3f51b5;
  border-color: #3f51b5;
  }
.menu > .nav > li:first-child > a,
.menu > .nav > li:first-child.nav-heading {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  }
.menu > .nav > li:last-child > a {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  }
.menu > .nav > li:first-child > a,
.menu > .nav > li:first-child.nav-heading {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  }
.menu > .nav > li.show > a,
.menu > .nav > li.show > a:hover,
.menu > .nav > li.show > a:focus {
  color: #353535;
  background-color: #fff;
  border-color: #ddd;
  }
.menu > .nav > li.show > a > [class*='icon-'],
.menu > .nav > li.show > a:hover > [class*='icon-'],
.menu > .nav > li.show > a:focus > [class*='icon-'] {
  color: #353535;
  }
.menu > .nav > li.show > a:hover {
  background-color: #f1f1f1;
  }
.menu > .nav > li.show:last-child > a {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  }
.menu > .nav > li.show:last-child > .nav > li:last-child > a {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  }
.menu > .nav > li.nav-heading {
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  }
.dropdown-menu {
  border-radius: 0;
  }
.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus {
  background-color: #3f51b5;
  }
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
  background-color: #3f51b5;
  }
.dropdown-submenu:hover > a,
.dropdown-submenu:focus > a {
  background-color: #3f51b5;
  }
.dropdown-submenu:hover > a:after {
  border-left-color: #fff;
  }
.datetimepicker td.day.today {
  background-color: #f0ad4e !important;
  border-color: #f0ad4e !important;
  }
.datetimepicker td.day.active {
  background-color: #3f51b5 !important;
  border-color: #32408f !important;
  }
.datetimepicker td.day.active:hover {
  background-color: #32408f !important;
  }
.datetimepicker td.day.today:hover,
.datetimepicker td.day.today.active:hover {
  background-color: #ec971f !important;
  }
.ke-container {
  border-radius: 0 !important;
  }
.ke-container.focus {
  border-color: #3949ab !important;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(57, 73, 171, .6) !important;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(57, 73, 171, .6) !important;
  }
.ke-menu-item-on {
  background-color: #3f51b5 !important;
  }
.ke-colorpicker-cell-on {
  background-color: #3f51b5 !important;
  }
.ke-input-text:focus {
  border-color: #3949ab !important;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(57, 73, 171, .6) !important;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(57, 73, 171, .6) !important;
  }
.messager {
  border-radius: 0;
  }
.messager-primary {
  background-color: #3f51b5;
  }
.messager-success {
  background-color: #4caf50;
  }
.messager-info {
  background-color: #039be5;
  }
.messager-warning {
  background-color: #f0ad4e;
  }
.messager-danger {
  background-color: #d9534f;
  }
.messager-important {
  background-color: #bd7b46;
  }
.messager-special {
  background-color: #8666b8;
  }
.comment:hover > .content > .actions > a {
  color: #3949ab;
  }
.alert:not(.alert-block) {
  border-radius: 0;
  }
.alert-primary {
  color: #282828;
  background-color: #eceff1;
  }
.alert-primary hr {
  border-top-color: #eceff1;
  }
.alert-primary .alert-link {
  color: #161616;
  }
.alert-success {
  color: #449d48;
  background-color: #ddf4df;
  }
.alert-success hr {
  border-top-color: #ddf4df;
  }
.alert-success .alert-link {
  color: #39843c;
  }
.alert-info {
  color: #038acc;
  background-color: #ddf3f5;
  }
.alert-info hr {
  border-top-color: #ddf3f5;
  }
.alert-info .alert-link {
  color: #0272a9;
  }
.alert-warning {
  color: #eea236;
  background-color: #fff0d5;
  }
.alert-warning hr {
  border-top-color: #fff0d5;
  }
.alert-warning .alert-link {
  color: #eb9316;
  }
.alert-danger {
  color: #d43f3a;
  background-color: #ffe5e0;
  }
.alert-danger hr {
  border-top-color: #ffe5e0;
  }
.alert-danger .alert-link {
  color: #c12e2a;
  }
.alert-primary-inverse {
  color: #fff;
  background-color: #3f51b5;
  border-color: #3f51b5;
  }
.alert-primary-inverse hr {
  border-top-color: #ddd;
  border-top-color: rgba(221, 221, 221, .25);
  }
.alert-primary-inverse .alert-link {
  color: rgba(255, 255, 255, .8);
  }
.modal-dialog {
  border-radius: 1px;
  }
.card {
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  }
pre {
  border-radius: 0;
  }
.datagrid .datagrid-hover-row .datagrid-row-cell:hover .datagrid-cell,
.datagrid .datagrid-hover-col .datagrid-cell.hover,
.datagrid .datagrid-hover-col .datagrid-row-cell.active .datagrid-cell.hover {
  background-color: #eceff1;
  }
.datagrid .datagrid-loading > .content > .icon,
.datagrid .datagrid-sort-up > .icon-sort:before,
.datagrid .datagrid-sort-down > .icon-sort:before {
  color: #3f51b5;
  }
.tree-drag-holder {
  background-color: #eceff1;
  }
.tree-menu li.active > a {
  color: #fff;
  background-color: #3f51b5;
  border-color: #3f51b5;
  }
.tree-menu li.active > .list-toggle {
  color: #fff;
  }
