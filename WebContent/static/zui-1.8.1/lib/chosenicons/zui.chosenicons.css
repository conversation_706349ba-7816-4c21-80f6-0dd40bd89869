/*!
 * ZUI: 图标选择器 - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */

.chosen-container.chosen-icons .chosen-results {
  padding: 5px;
  }
.chosen-container.chosen-icons .chosen-results li {
  border-radius: 4px;
  }
.chosen-container.chosen-icons .chosen-results li.group-result {
  padding: 5px 0;
  font-size: 12px;
  color: #666;
  border-radius: 0;
  }
.chosen-container.chosen-icons .chosen-results li.group-option {
  display: inline-block;
  width: 30px;
  padding: 8px;
  font-size: 14px;
  line-height: 14px;
  text-align: center;
  }
