/*!
 * ZUI: 树形图 - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */

.treemap-data {
  text-align: left;
  }
.treemap-nodes {
  position: relative;
  display: table;
  padding: 10px;
  margin-right: auto;
  margin-left: auto;
  text-align: center;
  -webkit-user-select: none;
     -moz-user-select: none;
  }
.treemap-node {
  display: table-cell;
  vertical-align: top;
  }
.treemap-node-wrapper {
  position: relative;
  z-index: 5;
  display: inline-block;
  padding: 5px 10px;
  border: 1px solid #808080;
  border-radius: 1px;
  }
a.treemap-node-wrapper {
  color: #353535;
  cursor: pointer;
  }
a.treemap-node-wrapper:active,
a.treemap-node-wrapper:focus,
a.treemap-node-wrapper:hover {
  color: #3280fc;
  text-decoration: none;
  background-color: #ebf2f9;
  border-color: #3280fc;
  }
.treemap-node-root > .treemap-node-wrapper {
  background-color: rgba(0, 0, 0, .1);
  }
.treemap-node-children {
  display: table;
  margin-top: 20px auto 0;
  }
.treemap-line-top,
.treemap-line-bottom {
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -1px;
  border-right: none!important;
  border-bottom: none!important;
  }
.treemap-line-top {
  top: 0;
  }
.treemap-node > .treemap-line {
  position: absolute;
  right: 0;
  left: 0;
  z-index: 1;
  border-right: none!important;
  border-bottom: none!important;
  }
.treemap-node-fold-icon {
  position: absolute;
  top: -6px;
  left: -5px;
  z-index: 10;
  display: block!important;
  width: 10px;
  height: 10px;
  line-height: 10px;
  color: #808080;
  background-color: #fff;
  border-radius: 5px;
  opacity: 0;
  -webkit-transition: opacity .2s, -webkit-transform .1s;
       -o-transition: opacity .2s, -o-transform .1s;
          transition: opacity .2s, -webkit-transform .1s;
          transition: opacity .2s, transform .1s;
          transition: opacity .2s, transform .1s, -webkit-transform .1s, -o-transform .1s;
  }
.treemap-node-fold-icon:before {
  min-width: 10px;
  content: '\e6f2';
  }
.treemap-node-wrapper:hover .treemap-node-fold-icon {
  opacity: 1;
  }
.treemap-node.collapsed > .treemap-line,
.treemap-node.collapsed .treemap-line-bottom {
  border-color: transparent!important;
  }
.treemap-node.collapsed > .treemap-node-children {
  display: none;
  }
.treemap-node.collapsed .treemap-node-fold-icon {
  top: -6px;
  color: #808080;
  opacity: 1;
  -webkit-transform: none!important;
      -ms-transform: none!important;
       -o-transform: none!important;
          transform: none!important;
  }
.treemap-node.collapsed .treemap-node-fold-icon:before {
  content: '\e6f1';
  }
.treemap-node.tree-node-collapsing > .treemap-line,
.treemap-node.tree-node-collapsing > .treemap-node-children {
  visibility: hidden;
  }
