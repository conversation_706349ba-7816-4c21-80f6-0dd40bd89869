/*!
 * ZUI: 树形图 - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */
!function(e,t,a,o,r){"use strict";var n="zui.treemap",l={data:[],cableWidth:1,cableColor:"#808080",cableStyle:"solid",rowSpace:30,nodeSpace:20,listenNodeResize:!0,nodeTemplate:'<div class="treemap-node"><a class="treemap-node-wrapper"></a></div>',foldable:!0,clickNodeToFold:!0},i=function(t){return t.children("li,.treemap-data-item").map(function(){var t=e(this),a=t.data(),o=t.children(".text"),r=t.children(".content"),n=t.children("ul,.treemap-data-list");if(o.length&&(a.text=o.text()),r.length&&(a.html=r.html()),n.length&&(a.children=i(n)),!a.text&&!a.html){var l=t.children(":not(ul,.treemap-data-list)"),d=t.clone();d.find("ul,.treemap-data-list").remove(),l.length?a.html=d.html():a.text=d.text()}return a}).get()},d=function(t,a){var o=e(t);e.isArray(a)&&(a={data:a}),a=e.extend({},l,o.data(),a);var r=a.data||[];if(!r.length){var n=o.children(".treemap-data");n.length&&(r=i(n.hide()))}var d=o.children(".treemap-nodes");d.length||(d=e('<div class="treemap-nodes" unselectable="on"/>').appendTo(o));var s=this;s.$=o,s.$nodes=d,s.data=e.isArray(r)?r:[r],s.options=a,s.offsetX=0,s.offsetY=0,s.scale=a.scale||1,s.render(),d.on("resize",".treemap-node-wrapper",function(){s.delayDrawLines()}),a.foldable&&d.on("click",a.clickNodeToFold?".treemap-node-wrapper":".treemap-node-fold-icon",function(){s.toggle(e(this).closest(".treemap-node"))}),d.on("click",".treemap-node-wrapper",function(){var t=e(this).closest(".treemap-node");s.callEvent("onNodeClick",t.data("node"))})};d.prototype.toggle=function(e,t,a){var o=this;if("boolean"==typeof e&&(t=e,e=null),e||(e=o.$nodes.children(".treemap-node").first()),e){if(e.data("node").foldable===!1)return;t===r&&(t=e.hasClass("collapsed")),e.toggleClass("collapsed",!t).find('[data-toggle="tooltip"]').tooltip("hide"),a||e.addClass("tree-node-collapsing"),o.$nodes.find(".tooltip").remove(),o.drawLines(),a?(clearTimeout(o.toggleTimeTask),o.toggleTimeTask=setTimeout(function(){e.removeClass("tree-node-collapsing")},200)):e.removeClass("tree-node-collapsing")}},d.prototype.showLevel=function(t){var a=this;a.$nodes.find(".treemap-node").each(function(){var o=e(this);a.toggle(o,o.data("level")<t,!0)})},d.prototype.render=function(t){var a=this;a.data=t?e.isArray(t)?t:[t]:a.data,a.data&&(a.createNodes(),a.drawLines(),a.delayDrawLines(500)),a.callEvent("afterRender")},d.prototype.createNodes=function(t,a,r){var l=this,i=l.options,d=i.rowSpace,s=l.$nodes;a||(s.find(".treemap-node-wrapper").off("resize."+n),s.empty()),i.sort&&t.sort(e.isFunction(i.sort)?i.sort:function(e,t){return(e.order||0)-t.order});var p=null;t=t||l.data,a||(l.maxLevel=1),e.each(t,function(c,h){"string"==typeof h&&(h={html:h},t[c]=h),h.id||(h.id=e.zui.uuid()),h.level=a?a.level+1:1,l.maxLevel=o.max(l.maxLevel,h.level);var f=e.isFunction(i.nodeTemplate),m=f?i.nodeTemplate(h,l):e(i.nodeTemplate),g=m.find(".treemap-node-wrapper");g.length||(g=e('<div class="treemap-node-wrapper"/>').appendTo(m));var v=h.children,u=v&&v.length;h.isOnlyOneChild=1===u,h.idx=c;var w=a?a.row+1:0;m.toggleClass("treemap-node-has-child",!!u).toggleClass("treemap-node-has-parent",!!a).toggleClass("treemap-node-one-child",1===u).toggleClass("collapsed",!!h.collapsed&&"false"!==h.collapsed).toggleClass("treemap-node-root",!w).attr({"data-id":h.id,"data-level":h.level}).data("node",h),h.className&&m.addClass(h.className),h.row=w;var y=e.extend({},i.nodeStyle,h.style);h.textColor&&(y.color=h.textColor),h.color&&(y.backgroundColor=h.color),h.border&&(y.border=h.border);var b=e.extend({},h.attrs,{title:h.caption});if(h.tooltip&&(b["data-toggle"]="tooltip",b.title=h.tooltip),g.attr(b).css(y),p&&m.css("padding-left",i.nodeSpace),f||(h.html?g.append(h.html):h.text&&g.text(h.text)),m.appendTo(a?a.$children:s),p&&(p.next=h),h.prev=p,h.parent=a,h.$=m,h.$wrapper=g,u){var x=m.find(".treemap-node-children");x.length||(x=e('<div class="treemap-node-children"/>').appendTo(m)),x.css("margin-top",d),h.$children=x,l.createNodes(v,h)}i.listenNodeResize&&g.on("resize."+n,function(){l.delayDrawLines()}),p=h,r&&r(m,h)}),a||s.find('[data-toggle="tooltip"]').tooltip(i.tooltip)},d.prototype.delayDrawLines=function(e){var t=this;clearTimeout(t.delayDrawLinesTask),t.delayDrawLinesTask=setTimeout(function(){t.drawLines()},e||10)},d.prototype.drawLines=function(t,a){var r=this,n=r.options,l=n.rowSpace,i={};n.cableWidth&&(i.borderWidth=n.cableWidth),n.cableStyle&&(i.borderStyle=n.cableStyle),n.cableColor&&(i.borderColor=n.cableColor);var d=o.round(l/2),s=r.$nodes.offset().left;e.each(t||r.data,function(t,p){var c=p.$wrapper,h=p.children,f=e.extend({height:d,top:-d-1,left:o.round((c.outerWidth()-i.borderWidth)/2),color:i.borderColor},i);if(a&&!a.isOnlyOneChild){var m=c.find(".treemap-line-top");m.length||(m=e('<div class="treemap-line-top"/>').appendTo(c)),m.css(f)}if(h&&h.length){f.top=c.outerHeight()-1,p.isOnlyOneChild&&(f.height=l);var g=c.find(".treemap-line-bottom");if(g.length||(g=e('<div class="treemap-line-bottom"/>').appendTo(c),n.foldable&&g.append('<i class="treemap-node-fold-icon icon" style="transform: translate(-'+o.floor(f.borderWidth/2)+"px, "+d+'px)"/>')),g.css(f),r.drawLines(h,p),h.length>1){var v=h[0],u=h[h.length-1],w=p.$.children(".treemap-line");w.length||(w=e('<div class="treemap-line"/>').insertAfter(c));var y=o.round(v.$wrapper.offset().left-s+v.$wrapper.outerWidth()/2);w.css(e.extend({marginTop:d,left:y,width:u.$wrapper.offset().left-s-y+u.$wrapper.outerWidth()/2},i))}}}),a||r.callEvent("afterDrawLines")},d.prototype.callEvent=function(t,a){var o=this;if(e.isArray(a)||(a=[a]),o.$.trigger(t,a),e.isFunction(o.options[t]))return o.options[t].apply(o,a)},d.DEFAULTS=l,d.NAME=n,e.fn.treemap=function(t,a,o){return this.each(function(){var r=e(this),l=r.data(n),i="object"==typeof t&&t;l||r.data(n,l=new d(this,i)),"string"==typeof t&&l[t](a,o)})},e.fn.treemap.Constructor=d}(jQuery,window,document,Math,void 0);