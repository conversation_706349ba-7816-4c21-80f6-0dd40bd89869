/*! bootbox.js v4.4.0 http://bootboxjs.com/license.txt */
!function(t,o){"use strict";"function"==typeof define&&define.amd?define(["jquery"],o):"object"==typeof exports?module.exports=o(require("jquery")):t.bootbox=o(t.jQuery)}(this,function t(o,e){"use strict";function a(t){var o=h[f.locale];return o?o[t]:h.en[t]}function n(t,e,a){t.stopPropagation(),t.preventDefault();var n=o.isFunction(a)&&a.call(e,t)===!1;n||e.modal("hide")}function r(t){var o,e=0;for(o in t)e++;return e}function i(t,e){var a=0;o.each(t,function(t,o){e(t,o,a++)})}function c(t){var e,a;if("object"!=typeof t)throw new Error("Please supply an object of options");if(!t.message)throw new Error("Please specify a message");return t=o.extend({},f,t),t.buttons||(t.buttons={}),e=t.buttons,a=r(e),i(e,function(t,n,r){if(o.isFunction(n)&&(n=e[t]={callback:n}),"object"!==o.type(n))throw new Error("button with key "+t+" must be an object");n.label||(n.label=t),n.className||(2===a&&("ok"===t||"confirm"===t)||1===a?n.className="btn-primary":n.className="btn-default")}),t}function l(t,o){var e=t.length,a={};if(e<1||e>2)throw new Error("Invalid argument length");return 2===e||"string"==typeof t[0]?(a[o[0]]=t[0],a[o[1]]=t[1]):a=t[0],a}function s(t,e,a){return o.extend(!0,{},t,l(e,a))}function u(t,o,e,a){var n={className:"bootbox-"+t,buttons:p.apply(null,o)};return b(s(n,a,e),o)}function p(){for(var t={},o=0,e=arguments.length;o<e;o++){var n=arguments[o],r=n.toLowerCase(),i=n.toUpperCase();t[r]={label:a(i)}}return t}function b(t,o){var a={};return i(o,function(t,o){a[o]=!0}),i(t.buttons,function(t){if(a[t]===e)throw new Error("button key "+t+" is not allowed (options are "+o.join("\n")+")")}),t}var d={dialog:"<div class='bootbox modal' tabindex='-1' role='dialog'><div class='modal-dialog'><div class='modal-content'><div class='modal-body'><div class='bootbox-body'></div></div></div></div></div>",header:"<div class='modal-header'><h4 class='modal-title'></h4></div>",footer:"<div class='modal-footer'></div>",closeButton:"<button type='button' class='bootbox-close-button close' data-dismiss='modal' aria-hidden='true'>&times;</button>",form:"<form class='bootbox-form'></form>",inputs:{text:"<input class='bootbox-input bootbox-input-text form-control' autocomplete=off type=text />",textarea:"<textarea class='bootbox-input bootbox-input-textarea form-control'></textarea>",email:"<input class='bootbox-input bootbox-input-email form-control' autocomplete='off' type='email' />",select:"<select class='bootbox-input bootbox-input-select form-control'></select>",checkbox:"<div class='checkbox'><label><input class='bootbox-input bootbox-input-checkbox' type='checkbox' /></label></div>",date:"<input class='bootbox-input bootbox-input-date form-control' autocomplete=off type='date' />",time:"<input class='bootbox-input bootbox-input-time form-control' autocomplete=off type='time' />",number:"<input class='bootbox-input bootbox-input-number form-control' autocomplete=off type='number' />",password:"<input class='bootbox-input bootbox-input-password form-control' autocomplete='off' type='password' />"}},f={locale:o.zui&&o.zui.clientLang?o.zui.clientLang():"zh_cn",backdrop:"static",animate:!0,className:null,closeButton:!0,show:!0,container:"body"},m={};m.alert=function(){var t;if(t=u("alert",["ok"],["message","callback"],arguments),t.callback&&!o.isFunction(t.callback))throw new Error("alert requires callback property to be a function when provided");return t.buttons.ok.callback=t.onEscape=function(){return!o.isFunction(t.callback)||t.callback.call(this)},m.dialog(t)},m.confirm=function(){var t;if(t=u("confirm",["confirm","cancel"],["message","callback"],arguments),t.buttons.cancel.callback=t.onEscape=function(){return t.callback.call(this,!1)},t.buttons.confirm.callback=function(){return t.callback.call(this,!0)},!o.isFunction(t.callback))throw new Error("confirm requires a callback");return m.dialog(t)},m.prompt=function(){var t,a,n,r,c,l,u;if(r=o(d.form),a={className:"bootbox-prompt",buttons:p("cancel","confirm"),value:"",inputType:"text"},t=b(s(a,arguments,["title","callback"]),["confirm","cancel"]),l=t.show===e||t.show,t.message=r,t.buttons.cancel.callback=t.onEscape=function(){return t.callback.call(this,null)},t.buttons.confirm.callback=function(){var e;switch(t.inputType){case"text":case"textarea":case"email":case"select":case"date":case"time":case"number":case"password":e=c.val();break;case"checkbox":var a=c.find("input:checked");e=[],i(a,function(t,a){e.push(o(a).val())})}return t.callback.call(this,e)},t.show=!1,!t.title)throw new Error("prompt requires a title");if(!o.isFunction(t.callback))throw new Error("prompt requires a callback");if(!d.inputs[t.inputType])throw new Error("invalid prompt type");switch(c=o(d.inputs[t.inputType]),t.inputType){case"text":case"textarea":case"email":case"date":case"time":case"number":case"password":c.val(t.value);break;case"select":var f={};if(u=t.inputOptions||[],!o.isArray(u))throw new Error("Please pass an array of input options");if(!u.length)throw new Error("prompt with select requires options");i(u,function(t,a){var n=c;if(a.value===e||a.text===e)throw new Error("given options in wrong format");a.group&&(f[a.group]||(f[a.group]=o("<optgroup/>").attr("label",a.group)),n=f[a.group]),n.append("<option value='"+a.value+"'>"+a.text+"</option>")}),i(f,function(t,o){c.append(o)}),c.val(t.value);break;case"checkbox":var h=o.isArray(t.value)?t.value:[t.value];if(u=t.inputOptions||[],!u.length)throw new Error("prompt with checkbox requires options");if(!u[0].value||!u[0].text)throw new Error("given options in wrong format");c=o("<div/>"),i(u,function(e,a){var n=o(d.inputs[t.inputType]);n.find("input").attr("value",a.value),n.find("label").append(a.text),i(h,function(t,o){o===a.value&&n.find("input").prop("checked",!0)}),c.append(n)})}return t.placeholder&&c.attr("placeholder",t.placeholder),t.pattern&&c.attr("pattern",t.pattern),t.maxlength&&c.attr("maxlength",t.maxlength),r.append(c),r.on("submit",function(t){t.preventDefault(),t.stopPropagation(),n.find(".btn-primary").click()}),n=m.dialog(t),n.off("shown.zui.modal"),n.on("shown.zui.modal",function(){c.focus()}),l===!0&&n.modal("show"),n},m.dialog=function(t){t=c(t);var a=o(d.dialog),r=a.find(".modal-dialog"),l=a.find(".modal-body"),s=t.buttons,u="",p={onEscape:t.onEscape};if(o.fn.modal===e)throw new Error("$.fn.modal is not defined; please double check you have included the Bootstrap JavaScript library. See http://getbootstrap.com/javascript/ for more details.");if(i(s,function(t,o){u+="<button data-bb-handler='"+t+"' type='button' class='btn "+o.className+"'>"+o.label+"</button>",p[t]=o.callback}),l.find(".bootbox-body").html(t.message),t.animate===!0&&a.addClass("fade"),t.className&&a.addClass(t.className),"large"===t.size?r.addClass("modal-lg"):"small"===t.size&&r.addClass("modal-sm"),t.title&&l.before(d.header),t.closeButton){var b=o(d.closeButton);t.title?a.find(".modal-header").prepend(b):b.css("margin-top","-10px").prependTo(l)}return t.title&&a.find(".modal-title").html(t.title),u.length&&(l.after(d.footer),a.find(".modal-footer").html(u)),a.on("hidden.zui.modal",function(t){t.target===this&&a.remove()}),a.on("shown.zui.modal",function(){a.find(".btn-primary:first").focus()}),"static"!==t.backdrop&&a.on("click.dismiss.zui.modal",function(t){a.children(".modal-backdrop").length&&(t.currentTarget=a.children(".modal-backdrop").get(0)),t.target===t.currentTarget&&a.trigger("escape.close.bb")}),a.on("escape.close.bb",function(t){p.onEscape&&n(t,a,p.onEscape)}),a.on("click",".modal-footer button",function(t){var e=o(this).data("bb-handler");n(t,a,p[e])}),a.on("click",".bootbox-close-button",function(t){n(t,a,p.onEscape)}),a.on("keyup",function(t){27===t.which&&a.trigger("escape.close.bb")}),o(t.container).append(a),a.modal({backdrop:!!t.backdrop&&"static",keyboard:!1,show:!1}),t.show&&a.modal("show"),a},m.setDefaults=function(){var t={};2===arguments.length?t[arguments[0]]=arguments[1]:t=arguments[0],o.extend(f,t)},m.hideAll=function(){return o(".bootbox").modal("hide"),m};var h={en:{OK:"OK",CANCEL:"Cancel",CONFIRM:"OK"},zh_cn:{OK:"确认",CANCEL:"取消",CONFIRM:"确认"},zh_tw:{OK:"確認",CANCEL:"取消",CONFIRM:"確認"}};return m.addLocale=function(t,e){return o.each(["OK","CANCEL","CONFIRM"],function(t,o){if(!e[o])throw new Error("Please supply a translation for '"+o+"'")}),h[t]={OK:e.OK,CANCEL:e.CANCEL,CONFIRM:e.CONFIRM},m},m.removeLocale=function(t){return delete h[t],m},m.setLocale=function(t){return m.setDefaults("locale",t)},m.init=function(e){return t(e||o)},m});