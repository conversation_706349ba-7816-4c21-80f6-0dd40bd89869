/*!
 * ZUI: 日历 - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */
!function(t,e){"use strict";var a="zui.calendar",n="number",s="string",i="undefined",o={primary:1,green:2,red:3,blue:4,yellow:5,brown:6,purple:7},d=function(t,e){e=e||1;for(var a=t.clone();a.getDay()!=e;)a.addDays(-1);return a.clearTime(),a},r=function(t){var e=t.clone();return e.setDate(1),e},l=function(t,e){var a=t.clone().clearTime(),n=e.clone().clearTime();return Math.round((n.getTime()-a.getTime())/Date.ONEDAY_TICKS)+1},c=function(t,e,a){for(var n=t.clone(),s=0;n<=e;)a(n.clone(),s++),n.addDays(1)},h=function(e,n){if(this.name=a,this.$=t(e),this.id=this.$.attr("id")||a+t.zui.uuid(),this.$.attr("id",this.id),this.storeName=a+"."+this.id,this.getOptions(n),this.getLang(),this.data=this.options.data,this.addCalendars(this.data.calendars),this.addEvents(this.data.events),this.sortEvents(),this.storeData=t.zui.store.pageGet(this.storeName,{date:"today",view:"month"}),this.date=this.options.startDate||"today",this.view=this.options.startView||"month",this.$.toggleClass("limit-event-title",n.limitEventTitle),this.options.withHeader){var s=this.$.children(".calender-header");s.length||(s=t('<header><div class="btn-toolbar"><div class="btn-group"><button type="button" class="btn btn-today">{today}</button></div><div class="btn-group"><button type="button" class="btn btn-prev"><i class="icon-chevron-left"></i></button><button type="button" class="btn btn-next"><i class="icon-chevron-right"></i></button></div><div class="btn-group"><span class="calendar-caption"></span></div></div></header>'.format(this.lang)),this.$.append(s)),this.$caption=s.find(".calendar-caption"),this.$todayBtn=s.find(".btn-today"),this.$header=s}var i=this.$.children(".calendar-views");i.length||(i=t('<div class="calendar-views"></div>'),this.$.append(i)),this.$views=i,this.$monthView=i.children(".calendar-view.month"),this.display(),this.bindEvents()};h.DEFAULTS={langs:{zh_cn:{weekNames:["周一","周二","周三","周四","周五","周六","周日"],monthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],today:"今天",year:"{0}年",month:"{0}月",yearMonth:"{0}年{1}月"},zh_tw:{weekNames:["週一","週二","週三","週四","週五","週六","週日"],monthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],today:"今天",year:"{0}年",month:"{0}月",yearMonth:"{0}年{1}月"},en:{weekNames:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],monthNames:["Jan","Feb","Mar","Apr","May","June","July","Aug","Sep","Oct","Nov","Dec"],today:"Today",year:"{0}",month:"{0}",yearMonth:"{2}, {0}"}},data:{calendars:{defaultCal:{color:"#229F24"}},events:[]},limitEventTitle:!0,storage:!0,withHeader:!0,dragThenDrop:!0},h.prototype.sortEvents=function(){var e=this.events;t.isArray(e)||(e=[]),e.sort(function(t,e){return t.start<e.start?1:t.start>e.start?-1:0}),this.events=e},h.prototype.bindEvents=function(){var e=this.$,a=this;e.on("click",".btn-today",function(){a.date=new Date,a.display(),a.callEvent("clickTodayBtn")}).on("click",".btn-next",function(){"month"===a.view&&a.date.addMonths(1),a.display(),a.callEvent("clickNextBtn")}).on("click",".btn-prev",function(){"month"===a.view&&a.date.addMonths(-1),a.display(),a.callEvent("clickPrevBtn")}).on("click",".event",function(e){a.callEvent("clickEvent",{element:this,event:t(this).data("event"),events:a.events}),e.stopPropagation()}).on("click",".cell-day",function(){a.callEvent("clickCell",{element:this,view:a.view,date:new Date(t(this).children(".day").attr("data-date")),events:a.events})})},h.prototype.addCalendars=function(e,a){var n=this;n.calendars||(n.calendars={}),t.isPlainObject(e)&&(e=[e]),t.each(e,function(e,s){if(a||n.callEvent("beforeAddCalendars",{newCalendar:s,data:n.data})){if(s.color||(s.color="primary"),o[s.color.toLowerCase()])s.presetColor=!0;else{var i=new t.zui.Color(s.color);s.textColor=i.contrast().hexStr()}n.calendars[s.name]=s}}),a||(n.display(),n.callEvent("addCalendars",{newCalendars:e,data:n.data}))},h.prototype.addEvents=function(e,a){var o=this;o.events||(o.events=[]),t.isPlainObject(e)&&(e=[e]),t.each(e,function(e,d){if(a||o.callEvent("beforeAddEvent",{newEvent:d,data:o.data})){var r=typeof d.start,c=typeof d.end;r!==n&&r!==s||(d.start=new Date(d.start)),c!==n&&c!==s||(d.end=new Date(d.end)),typeof d.id===i&&(d.id=t.zui.uuid()),d.allDay&&(d.start.clearTime(),d.end.clearTime().addDays(1).addMilliseconds(-1)),d.days=l(d.start,d.end),o.events.push(d)}}),a||(o.sortEvents(),o.display(),o.callEvent("addEvents",{newEvents:e,data:o.data}))},h.prototype.getEvent=function(t){for(var e=this.events,a=0;a<e.length;a++)if(e[a].id==t)return e[a];return null},h.prototype.updateEvents=function(e){var a={data:this.data,changes:[]},n=this;t.isPlainObject(e)&&(e=[e]);var i,o,d;t.each(e,function(e,r){i=r.event,o=r.changes,d={event:i,changes:[]},typeof i===s&&(i=n.getEvent(i)),i&&(t.isPlainObject(o)&&(o=[o]),t.each(r,function(e,a){n.callEvent("beforeChange",{event:i,change:a.change,to:a.to,from:i[a.change]})&&(d.changes.push(t.extend(!0,{},a,{from:i[a.change]})),i[a.change]=a.to)})),a.changes.push(d)}),n.sortEvents(),n.display(),n.callEvent("change",a)},h.prototype.removeEvents=function(e){t.isArray(e)||(e=[e]);var a,n,s,i=this.events,o=this,d=[];t.each(e,function(e,r){a=t.isPlainObject(r)?r.id:r,s=-1;for(var l=0;l<i.length;l++)if(i[l].id==a){s=l,n=i[l];break}s>=0&&o.callEvent("beforeRemoveEvent",{event:n,eventId:a,data:o.data})&&(i.splice(s,1),d.push(n))}),o.sortEvents(),o.display(),o.callEvent("removeEvents",{removedEvents:d,data:o.data})},h.prototype.getOptions=function(e){this.options=t.extend({},h.DEFAULTS,this.$.data(),e)},h.prototype.getLang=function(){this.lang=this.options.langs[this.options.lang||(t.zui&&t.zui.clientLang?t.zui.clientLang():"zh-cn")]},h.prototype.display=function(e,a){var n=this,o=typeof e,d=typeof a;o===i?e=n.view:n.view=e,d===i?a=n.date:n.date=a,"today"===a&&(a=new Date,n.date=a),typeof a===s&&(a=new Date(a),n.date=a),n.options.storage&&t.zui.store.pageSet(n.storeName,{date:a,view:e});var r={view:e,date:a};if(n.callEvent("beforeDisplay",r)){switch(e){case"month":n.displayMonth(a)}n.callEvent("display",r)}},h.prototype.displayMonth=function(e){var a=this;e=e||a.date;var n,s=a.options,i=a.lang,o=a.$views,l=(a.$,a.$monthView);if(!l.length){l=t('<div class="calendar-view month"><table class="table table-bordered"><thead><tr class="week-head"></tr></thead><tbody class="month-days"></tbody></table></div>');var c,h=l.find(".week-head"),v=l.find(".month-days");for(n=0;n<7;n++)t("<th>"+i.weekNames[n]+"</th>").toggleClass("weekend-head",n>=5).appendTo(h);for(n=0;n<6;n++){c=t('<tr class="week-days"></tr>');for(var p=0;p<7;p++)t('<td class="cell-day"><div class="day"><div class="heading"><span class="month"></span> <span class="number"></span></div><div class="content"><div class="events"></div></div></div></td>').toggleClass("weekend-day",p>=5).appendTo(c);v.append(c)}o.append(l),a.$monthView=l}var u,g,f,y,m,b,w,E,D,C,k,T=l.find(".week-days"),M=(l.find(".day"),r(e)),$=new Date,x=d(M),N=e.getFullYear(),P=e.getMonth(),z=$.getMonth(),S=$.getFullYear(),O=$.getDate(),j=x.clone().addDays(42).addMilliseconds(-1),A=x.clone().addDays(1).addMilliseconds(-1),F=a.getEvents(x,j),L=a.calendars,B=!0;if(T.each(function(e){u=t(this),u.find(".day").each(function(a){E=0===a,g=t(this),f=g.closest(".cell-day"),y=A.getFullYear(),m=A.getDate(),b=A.getMonth(),w=A.toDateString(),g.attr("data-date",w),g.find(".heading > .number").text(m),g.find(".heading > .month").toggle(0===e&&0===a||1===m).text((0===b&&1===m?i.year.format(y)+" ":"")+i.monthNames[b]),f.toggleClass("current-month",b===P),f.toggleClass("current",m===O&&b===z&&y===S),f.toggleClass("past",A<$),f.toggleClass("future",A>$),k=g.find(".events").empty();var s=F[w];if(s){var o,d=s.events,r=0;for(n=0;n<=s.maxPos;++n)o=d[n],!o||o.placeholder&&!E?r++:(B&&a>=5&&(B=!1),D=t('<div data-id="'+o.id+'" class="event" title="'+o.desc+'"><span class="time">'+o.start.format("hh:mm")+'</span> <span class="title">'+o.title+"</span></div>"),D.find(".time").toggle(!o.allDay),D.data("event",o),D.attr("data-days",o.days),o.calendar&&(C=L[o.calendar],C&&(C.presetColor?D.addClass("color-"+C.color):D.css({"background-color":C.color,color:C.textColor}))),o.days&&(o.placeholder?E&&D.css("width",Math.min(7,o.days-o.holderPos)+"00%"):D.css("width",Math.min(7-a,o.days)+"00%")),r>0&&(D.css("margin-top",22*r),r=0),k.append(D))}A.addDays(1)})}),s.hideEmptyWeekends&&l.toggleClass("weekends-empty",B),s.withHeader&&(a.$caption.text(i.yearMonth.format(N,P+1,i.monthNames[P])),a.$todayBtn.toggleClass("disabled",P===z&&N===S)),s.dragThenDrop){if(!t.fn.droppable)return console.error("Calendar dragThenDrop option requires droppable.js");l.data("zui.droppable")||l.droppable(t.extend({target:".cell-day",selector:".event",flex:!0,start:function(){a.$.addClass("event-dragging")},drop:function(t){var e=t.element.data("event"),n=t.target.children(".day").attr("data-date");if(e&&n){var s=e.start.clone();if(s.toDateString()!=n&&(n=new Date(n),n.setHours(s.getHours()),n.setMinutes(s.getMinutes()),n.setSeconds(s.getSeconds()),a.callEvent("beforeChange",{event:e,change:"start",to:n}))){var i=e.end.clone();e.end.addMilliseconds(e.end.getTime()-s.getTime()),e.start=n,a.display(),a.callEvent("change",{data:a.data,changes:[{event:e,changes:[{change:"start",from:s,to:e.start},{change:"end",from:i,to:e.end}]}]})}}},finish:function(){a.$.removeClass("event-dragging")}},t.isPlainObject(s.dragThenDrop)?s.dragThenDrop:null))}},h.prototype.getEvents=function(e,a){var n={},s=(this.calendars,function(t,e,a){var s=t.toDateString(),i=n[s];if(i||(i={maxPos:-1,events:{}}),"undefined"==typeof a)for(var o=0;o<100;++o)if(!i.events[o]){a=o;break}return i.maxPos=Math.max(a,i.maxPos),i.events[a]=e,n[s]=i,a});return t.each(this.events,function(n,i){if(i.start>=e&&i.start<=a){var o=s(i.start,i);if(i.days>1){var d=t.extend({placeholder:!0},i);c(i.start.clone().addDays(1),i.end,function(e,a){s(e.clone(),t.extend({holderPos:a},d),o)})}}}),n},h.prototype.callEvent=function(t,e){var n=this.$.callEvent(t+"."+a,e,this);return!(void 0!==n.result&&!n.result)},t.fn.calendar=function(e){return this.each(function(){var n=t(this),i=n.data(a),o="object"==typeof e&&e;i||n.data(a,i=new h(this,o)),typeof e==s&&i[e]()})},t.fn.calendar.Constructor=h}(jQuery,window);