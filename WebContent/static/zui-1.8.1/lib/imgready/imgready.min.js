/*! TangBin: image.ready.js http://www.planeart.cn/?p=1121 */
!function(n){"use strict";n.zui.imgReady=function(){var n=[],l=null,e=function(){for(var l=0;l<n.length;l++)n[l].end?n.splice(l--,1):n[l]();!n.length&&o()},o=function(){clearInterval(l),l=null};return function(o,r,t,u){var c,i,a,d,f,h=new Image;return h.src=o,h.complete?(r.call(h),void(t&&t.call(h))):(i=h.width,a=h.height,h.onerror=function(){u&&u.call(h),c.end=!0,h=h.onload=h.onerror=null},c=function(){d=h.width,f=h.height,(d!==i||f!==a||d*f>1024)&&(r.call(h),c.end=!0)},c(),h.onload=function(){!c.end&&c(),t&&t.call(h),h=h.onload=h.onerror=null},void(c.end||(n.push(c),null===l&&(l=setInterval(e,40)))))}}()}(jQuery);