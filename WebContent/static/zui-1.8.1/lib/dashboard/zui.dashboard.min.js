/*!
 * ZUI: 仪表盘 - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */
!function(e,a,t){"use strict";function i(e,t,i,n){return a.abs((i-e)*(n-t))}function n(e,a,t,i,n,o){return e>=t&&e<=n&&a>=i&&a<=o}function o(e,t,o,s,r,d,l,h){var g=a.max(e,r),p=a.max(t,d),c=a.min(o,l),f=a.min(s,h);return n(g,p,e,t,o,s)&&n(c,f,e,t,o,s)&&n(g,p,r,d,l,h)&&n(c,f,r,d,l,h)?i(g,p,c,f):0}var s=e.zui.Messager?new e.zui.Messager({placement:"top",time:1500,close:0,scale:!1,fade:!1}):0,r=function(a,t){this.$=e(a),this.options=this.getOptions(t),this.draggable=this.$.hasClass("dashboard-draggable")||this.options.draggable,this.init()};r.DEFAULTS={minHeight:100,height:360,shadowType:"normal",sensitive:!1,circleShadowSize:100,onlyRefreshBody:!0,resizable:!0,resizeMessage:!1},r.prototype.getOptions=function(a){return a=e.extend({},r.DEFAULTS,this.$.data(),a)},r.prototype.handleRemoveEvent=function(){var a=this.options.afterPanelRemoved,i=this.options.panelRemovingTip;this.$.on("click",".remove-panel",function(){var n=e(this).closest(".panel"),o=n.data("name")||n.find(".panel-heading").text().replace("\n","").replace(/(^\s*)|(\s*$)/g,""),s=n.attr("data-id");(i===t||i===!1||confirm(i.format(o)))&&(n.parent().remove(),a&&e.isFunction(a)&&a(s))})},r.prototype.handleRefreshEvent=function(){var a=this,t=this.options.onlyRefreshBody;this.$.on("click",".refresh-panel",function(){var i=e(this).closest(".panel");a.refresh(i,t)})},r.prototype.handleDraggable=function(){var t=this.$,n=this.options,s="circle"===n.shadowType,r=n.circleShadowSize,d=r/2,l=n.afterOrdered;this.$.addClass("dashboard-draggable"),this.$.on("mousedown",".panel-actions, .drag-disabled",function(e){e.stopPropagation()});var h;this.$.on("mousedown",".panel-heading, .panel-drag-handler",function(g){function p(t){var s=x.data("mouseOffset");u=t.pageX-s.x,v=t.pageY-s.y,m=u+F,z=v+A,x.css({left:u,top:v}),R.find(".dragging-in").removeClass("dragging-in"),w=!1,C=null;var r,d=0;R.children(":not(.dragging-col)").each(function(){var s=e(this);if(s.hasClass("dragging-col-holder"))return w=!n.sensitive||d<100,!0;var l=s.children(".panel"),h=l.offset(),g=l.width(),p=l.height(),c=h.left,f=h.top;if(n.sensitive)c-=H.left,f-=H.top,r=o(u,v,m,z,c,f,c+g,f+p),r>100&&r>d&&r>a.min(i(u,v,m,z),i(c,f,c+g,f+p))/3&&(d=r,C=s);else{var y=t.pageX,b=t.pageY;if(y>c&&b>f&&y<c+g&&b<f+p)return C=s,!1}}),C&&(y&&clearTimeout(y),b=C,y=setTimeout(c,50)),t.preventDefault()}function c(){b&&(b.addClass("dragging-in"),w?D.insertAfter(b):D.insertBefore(b),t.addClass("dashboard-holding"),y=null,b=null)}function f(a){y&&clearTimeout(y);var i=$.data("order");$.parent().insertAfter(D);var n=0,o={};R.children(":not(.dragging-col-holder)").each(function(){var a=e(this).children(".panel");a.data("order",++n),o[a.data("id")||a.attr("id")]=n,a.parent().attr("data-order",n)}),i!=o[$.data("id")||$.attr("id")]&&(R.data("orders",o),l&&e.isFunction(l)&&l(o)),x.remove(),t.removeClass("dashboard-holding"),t.find(".dragging-col").removeClass("dragging-col"),t.find(".panel-dragging").removeClass("panel-dragging"),R.find(".dragging-in").removeClass("dragging-in"),t.removeClass("dashboard-dragging"),e(document).off("mousemove",p).off("mouseup",f),a.preventDefault()}var u,v,m,z,y,C,w,b,$=e(this).closest(".panel"),P=$.parent(),R=$.closest(".row"),x=$.clone().addClass("panel-dragging-shadow"),T=$.offset(),H=t.offset(),D=R.find(".dragging-col-holder"),F=$.width(),A=$.height();D.length||(D=e('<div class="dragging-col-holder"><div class="panel"></div></div>').removeClass("dragging-col").appendTo(R)),h&&D.removeClass(h),D.addClass(h=P.attr("class")),D.insertBefore(P).find(".panel").replaceWith($.clone().addClass("panel-dragging panel-dragging-holder")),t.addClass("dashboard-dragging"),$.addClass("panel-dragging").parent().addClass("dragging-col"),x.css({left:T.left-H.left,top:T.top-H.top,width:F,height:A}).appendTo(t).data("mouseOffset",{x:g.pageX-T.left+H.left,y:g.pageY-T.top+H.top}),s&&(x.addClass("circle"),setTimeout(function(){x.css({left:g.pageX-H.left-d,top:g.pageY-H.top-d,width:r,height:r}).data("mouseOffset",{x:H.left+d,y:H.top+d})},100)),e(document).on("mousemove",p).on("mouseup",f),g.preventDefault()})},r.prototype.handlePanelPadding=function(){this.$.find(".panel-body > table, .panel-body > .list-group").parent().addClass("no-padding")},r.prototype.updatePanelHeight=function(){var t=this,i=t.options.height,n=t.options.minHeight,o={};return t.id&&e.zui.store&&(o=e.zui.store.pageGet("zui.dashboard."+t.id+".sizeConfig",o)),this.$.children(".row").each(function(){var t=e(this),s=t.width(),r=[],d=[],l=0;t.children(":not(.dragging-col-holder)").each(function(){var a=e(this),t=a.width();l+t>s?(d.length&&r.push(d),d=[a],l=t):(l+=t,d.push(a))}),d.length&&r.push(d),r.length&&e.each(r,function(t){d=r[t];var s=0,l=[],h=!1;e.each(d,function(e){var i=d[e].data("row-id",t),r=i.children(".panel:first");if(l.push(r),!h){var g=r.data("newHeight");if(g)r.data("newHeight",null).data("height",g),s=a.max(n,g),h=!0;else{var p=r.data("height")||o[r.data("id")];p&&(s=a.max(s,p))}}}),s||(s=i),e.each(l,function(e){var a=l[e].css("height",s);o[a.data("id")]=a.data("height")})})}),t.id&&e.zui.store&&e.zui.store.pageSet("zui.dashboard."+t.id+".sizeConfig",o),o},r.prototype.handleResizeEvent=function(){var t=this,i=t.options,n=i.resizable,o=i.onResize,r=i.minHeight,d=i.resizeMessage,l=d&&s;t.$.on("mousedown",".resize-handle",function(i){var n=e(this),d=n.hasClass("resize-vertical"),h=n.parent().addClass("resizing").toggleClass("resizing-v",d).toggleClass("resizing-h",!d),g=h.closest(".row"),p=h.children(".panel"),c=i.pageX,f=i.pageY,u=h.width(),v=p.height(),m=g.width(),z=a.round(12*u/m),y=z;d||h.attr("data-grid",z);var C=function(e){if(d)p.css("height",a.max(r,v+(e.pageY-f)));else{var t=e.pageX,i=a.max(1,a.min(12,a.round(12*(u+(t-c))/m)));y!=i&&(h.attr("data-grid",i).css("width",100*i/12+"%"),l&&s[s.isShow?"update":"show"](a.round(100*i/12)+"% ("+i+"/12)"),y=i)}e.preventDefault(),e.stopPropagation()},w=function(i){if(h.removeClass("resizing resizing-v resizing-h"),d){var n=a.max(r,v+(i.pageY-f));if(n!==v){if(e.isFunction(o)){var g=function(){p.css("height",v).data("height",v),t.updatePanelHeight()},c=o({type:"vertical",id:p.data("id"),element:h,old:v,height:n,revert:g});c===!1&&g()}p.css("height",n).data("newHeight",n)}}else{var u=h.attr("data-grid");if(z!=u&&e.isFunction(o)){var g=function(){h.attr("data-grid",z).css("width",null),t.updatePanelHeight()},c=o({type:"horizontal",id:p.data("id"),element:h,old:z,grid:u,revert:g});c===!1?g():c!==!0&&l&&s.show(a.round(100*u/12)+"% ("+u+"/12)")}}t.updatePanelHeight(),e("body").off("mousemove.resize",C).off("mouseup.resize",w),i.preventDefault(),i.stopPropagation()};e("body").on("mousemove.resize",C).on("mouseup.resize",w),i.preventDefault(),i.stopPropagation()});var h=t.$.children(".row").children(":not(.dragging-col-holder)");n!==!0&&"horizontal"!==n||h.append('<div class="resize-handle resize-horizontal"><i class="icon icon-resize-h"></i></div>'),n!==!0&&"vertical"!==n||h.append('<div class="resize-handle resize-vertical"><i class="icon icon-resize-v"></i></div>')},r.prototype.refresh=function(a,i){i===t&&(i=this.options.onlyRefreshBody);var n=this.options.afterRefresh;a=e(a);var o=a.data("url");o&&(a.addClass("panel-loading").find(".panel-heading .icon-refresh,.panel-heading .icon-repeat").addClass("icon-spin"),e.ajax({url:o,dataType:"html"}).done(function(t){var o=e(t);o.hasClass("panel")?a.empty().append(o.children()):i?a.find(".panel-body").empty().html(t):a.html(t),e.isFunction(n)&&n.call(this,{result:!0,data:t,$panel:a})}).fail(function(){a.addClass("panel-error"),e.isFunction(n)&&n.call(this,{result:!1,$panel:a})}).always(function(){a.removeClass("panel-loading"),a.find(".panel-heading .icon-refresh,.panel-heading .icon-repeat").removeClass("icon-spin")}))},r.prototype.init=function(){var a=this.options,i=this;if(i.id=a.id?a.id:i.$.attr("id"),a.data){var n=e('<div class="row"/>');e.each(a.data,function(a,i){var o=e('<div class="col-sm-'+(i.colWidth||4)+'"/>');i.colAttrs&&o.attr(i.colAttrs);var s=e('<div class="panel" data-id="'+(i.id||e.zui.uuid())+'"/>');if(i.panelAttrs&&s.attr(i.panelAttrs),i.height!==t&&s.data("height",i.height),i.content!==t)if(e.isFunction(i.content)){var r=i.content(s);r!==!0&&s.html(r)}else s.html(i.content);n.append(o.append(s.data("url",i.url)))}),i.$.append(n)}i.updatePanelHeight(),i.handlePanelPadding(),i.handleRemoveEvent(),i.handleRefreshEvent(),a.resizable&&i.handleResizeEvent(),i.draggable&&i.handleDraggable();var o=0;i.$.find(".panel").each(function(){var t=e(this);t.data("order",++o),t.attr("id")||t.attr("id","panel"+o),t.attr("data-id")||t.attr("data-id",o),i.refresh(t,a.onlyRefreshBody)}),i.$.find('[data-toggle="tooltip"]').tooltip({container:"body"})},e.fn.dashboard=function(a){return this.each(function(){var t=e(this),i=t.data("zui.dashboard"),n="object"==typeof a&&a;i||t.data("zui.dashboard",i=new r(this,n)),"string"==typeof a&&i[a]()})},e.fn.dashboard.Constructor=r}(jQuery,Math,void 0);