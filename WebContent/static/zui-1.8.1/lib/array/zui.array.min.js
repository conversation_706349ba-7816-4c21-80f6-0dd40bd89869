/*!
 * ZUI: 数组辅助方法 - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */
!function(){"use strict";var r="function";Array.prototype.forEach||(Array.prototype.forEach=function(t){var o=this.length;if(typeof t!=r)throw new TypeError;for(var e=arguments[1],i=0;i<o;i++)i in this&&t.call(e,this[i],i,this)}),Array.isArray||(Array.isArray=function(r){return"[object Array]"===Object.toString.call(r)}),Array.prototype.lastIndexOf||(Array.prototype.lastIndexOf=function(r){var t=this.length,o=Number(arguments[1]);for(isNaN(o)?o=t-1:(o=o<0?Math.ceil(o):Math.floor(o),o<0?o+=t:o>=t&&(o=t-1));o>-1;o--)if(o in this&&this[o]===r)return o;return-1}),Array.prototype.every||(Array.prototype.every=function(t){var o=this.length;if(typeof t!=r)throw new TypeError;for(var e=arguments[1],i=0;i<o;i++)if(i in this&&!t.call(e,this[i],i,this))return!1;return!0}),Array.prototype.filter||(Array.prototype.filter=function(t){var o=this.length;if(typeof t!=r)throw new TypeError;for(var e=[],i=arguments[1],n=0;n<o;n++)if(n in this){var a=this[n];t.call(i,a,n,this)&&e.push(a)}return e}),Array.prototype.indexOf||(Array.prototype.indexOf=function(r){var t=this.length,o=Number(arguments[1])||0;for(o=o<0?Math.ceil(o):Math.floor(o),o<0&&(o+=t);o<t;o++)if(o in this&&this[o]===r)return o;return-1}),Array.prototype.map||(Array.prototype.map=function(t){var o=this.length;if(typeof t!=r)throw new TypeError;for(var e=new Array(o),i=arguments[1],n=0;n<o;n++)n in this&&(e[n]=t.call(i,this[n],n,this));return e}),Array.prototype.where||(Array.prototype.where=function(t,o){o=o||[];var e,i,n;return this.forEach(function(a){i=!0;for(var y in t)if(e=t[y],typeof e===r?i=e(a):(n=a[y],i=n&&n===e),!i)break;i&&o.push(a)}),o}),Array.prototype.groupBy||(Array.prototype.groupBy=function(r){var t={};return this.forEach(function(o){var e=o[r];e||(e="unkown"),t[e]||(t[e]=[]),t[e].push(o)}),t}),Array.prototype.has||(Array.prototype.has=function(t){var o,e,i,n=!1;return this.forEach(function(a){e=!0;for(var y in t)if(o=t[y],typeof o===r?e=o(a):(i=a[y],e=i&&i===o),!e)break;if(e)return n=!0,!1}),n})}();