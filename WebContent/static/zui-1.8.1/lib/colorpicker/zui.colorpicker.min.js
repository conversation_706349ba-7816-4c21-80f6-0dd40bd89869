/*!
 * ZUI: 颜色选择器 - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */
!function(t){"use strict";var o="zui.colorPicker",i='<div class="colorpicker"><button type="button" class="btn dropdown-toggle" data-toggle="dropdown"><span class="cp-title"></span><i class="ic"></i></button><ul class="dropdown-menu clearfix"></ul></div>',e={zh_cn:{errorTip:"不是有效的颜色值"},zh_tw:{errorTip:"不是有效的顏色值"},en:{errorTip:"Not a valid color value"}},r=function(i,e){this.name=o,this.$=t(i),this.getOptions(e),this.init()};r.DEFAULTS={colors:["#00BCD4","#388E3C","#3280fc","#3F51B5","#9C27B0","#795548","#F57C00","#F44336","#E91E63"],pullMenuRight:!0,wrapper:"btn-wrapper",tileSize:30,lineCount:5,optional:!0,tooltip:"top",icon:"caret-down"},r.prototype.init=function(){var o=this.options,e=this;this.$picker=t(i).addClass(o.wrapper),this.$picker.find(".cp-title").toggle(void 0!==o.title).text(o.title),this.$menu=this.$picker.find(".dropdown-menu").toggleClass("pull-right",o.pullMenuRight),this.$btn=this.$picker.find(".btn.dropdown-toggle"),this.$btn.find(".ic").addClass("icon-"+o.icon),o.btnTip&&this.$picker.attr("data-toggle","tooltip").tooltip({title:o.btnTip,placement:o.tooltip,container:"body"}),this.$.attr("data-provide",null).after(this.$picker),this.colors={},t.each(this.options.colors,function(o,i){if(t.zui.Color.isColor(i)){var r=new t.zui.Color(i);e.colors[r.toCssStr()]=r}}),this.updateColors();var e=this;this.$picker.on("click",".cp-tile",function(){e.setValue(t(this).data("color"))});var r=this.$,s=function(){var i=r.val(),s=t.zui.Color.isColor(i);r.parent().toggleClass("has-error",!(s||o.optional&&""===i)),s?e.setValue(i,!0):o.optional&&""===i?r.tooltip("hide"):r.is(":focus")||r.tooltip("show",o.errorTip)};r.is("input:not([type=hidden])")?(o.tooltip&&r.attr("data-toggle","tooltip").tooltip({trigger:"manual",placement:o.tooltip,tipClass:"tooltip-danger",container:"body"}),r.on("keyup paste input change",s)):r.appendTo(this.$picker),s()},r.prototype.addColor=function(o){o instanceof t.zui.Color||(o=new t.zui.Color(o));var i=o.toCssStr(),e=this.options;this.colors[i]||(this.colors[i]=o);var r=t('<a href="###" class="cp-tile"></a>',{titile:o}).data("color",o).css({color:o.contrast().toCssStr(),background:i,"border-color":o.luma()>.43?"#ccc":"transparent"}).attr("data-color",i);this.$menu.append(t("<li/>").css({width:e.tileSize,height:e.tileSize}).append(r)),e.optional&&this.$menu.find(".cp-tile.empty").parent().detach().appendTo(this.$menu)},r.prototype.updateColors=function(o){var i=(this.$picker,this.$menu.empty()),e=this.options,o=o||this.colors,r=this,s=0;if(t.each(o,function(t,o){r.addColor(o),s++}),e.optional){var a=t('<li><a class="cp-tile empty" href="###"></a></li>').css({width:e.tileSize,height:e.tileSize});this.$menu.append(a),s++}i.css("width",Math.min(s,e.lineCount)*e.tileSize+6)},r.prototype.setValue=function(o,i){var e=this.options;this.$menu.find(".cp-tile.active").removeClass("active");var r="";if(o){var s=new t.zui.Color(o);r=s.toCssStr().toLowerCase(),this.$btn.css({background:r,color:s.contrast().toCssStr(),borderColor:s.luma()>.43?"#ccc":r}),this.colors[r]||this.addColor(s),i||this.$.val().toLowerCase()===r||this.$.val(r).trigger("change"),this.$menu.find('.cp-tile[data-color="'+r+'"]').addClass("active"),this.$.tooltip("hide"),this.$.trigger("colorchange",s)}else this.$btn.attr("style",null),i||""===this.$.val()||this.$.val(r).trigger("change"),e.optional&&this.$.tooltip("hide"),this.$menu.find(".cp-tile.empty").addClass("active"),this.$.trigger("colorchange",null);e.updateBorder&&t(e.updateBorder).css("border-color",r),e.updateBackground&&t(e.updateBackground).css("background-color",r),e.updateColor&&t(e.updateText).css("color",r),e.updateText&&t(e.updateText).text(r)},r.prototype.getOptions=function(o){var i=t.extend({},r.DEFAULTS,this.$.data(),o);"string"==typeof i.colors&&(i.colors=i.colors.split(","));var s=(i.lang||t.zui.clientLang()).toLowerCase();i.errorTip||(i.errorTip=e[s].errorTip),t.fn.tooltip||(i.btnTip=!1),this.options=i},t.fn.colorPicker=function(i){return this.each(function(){var e=t(this),s=e.data(o),a="object"==typeof i&&i;s||e.data(o,s=new r(this,a)),"string"==typeof i&&s[i]()})},t.fn.colorPicker.Constructor=r,t(function(){t('[data-provide="colorpicker"]').colorPicker()})}(jQuery);