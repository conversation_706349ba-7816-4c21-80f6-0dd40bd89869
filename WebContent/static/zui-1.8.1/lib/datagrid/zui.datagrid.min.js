/*!
 * ZUI: 数据表格② - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */
/*!
 * jQuery Mousewheel 3.1.13
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?module.exports=e:e(jQuery)}(function(e){function t(t){var n=t||window.event,l=s.call(arguments,1),d=0,h=0,g=0,f=0,u=0,p=0;if(t=e.event.fix(n),t.type="mousewheel","detail"in n&&(g=n.detail*-1),"wheelDelta"in n&&(g=n.wheelDelta),"wheelDeltaY"in n&&(g=n.wheelDeltaY),"wheelDeltaX"in n&&(h=n.wheelDeltaX*-1),"axis"in n&&n.axis===n.HORIZONTAL_AXIS&&(h=g*-1,g=0),d=0===g?h:g,"deltaY"in n&&(g=n.deltaY*-1,d=g),"deltaX"in n&&(h=n.deltaX,0===g&&(d=h*-1)),0!==g||0!==h){if(1===n.deltaMode){var v=e.data(this,"mousewheel-line-height");d*=v,g*=v,h*=v}else if(2===n.deltaMode){var m=e.data(this,"mousewheel-page-height");d*=m,g*=m,h*=m}if(f=Math.max(Math.abs(g),Math.abs(h)),(!i||f<i)&&(i=f,r(n,f)&&(i/=40)),r(n,f)&&(d/=40,h/=40,g/=40),d=Math[d>=1?"floor":"ceil"](d/i),h=Math[h>=1?"floor":"ceil"](h/i),g=Math[g>=1?"floor":"ceil"](g/i),c.settings.normalizeOffset&&this.getBoundingClientRect){var y=this.getBoundingClientRect();u=t.clientX-y.left,p=t.clientY-y.top}return t.deltaX=h,t.deltaY=g,t.deltaFactor=i,t.offsetX=u,t.offsetY=p,t.deltaMode=0,l.unshift(t,d,h,g),o&&clearTimeout(o),o=setTimeout(a,200),(e.event.dispatch||e.event.handle).apply(this,l)}}function a(){i=null}function r(e,t){return c.settings.adjustOldDeltas&&"mousewheel"===e.type&&t%120===0}var o,i,n=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],l="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],s=Array.prototype.slice;if(e.event.fixHooks)for(var d=n.length;d;)e.event.fixHooks[n[--d]]=e.event.mouseHooks;var c=e.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var a=l.length;a;)this.addEventListener(l[--a],t,!1);else this.onmousewheel=t;e.data(this,"mousewheel-line-height",c.getLineHeight(this)),e.data(this,"mousewheel-page-height",c.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var a=l.length;a;)this.removeEventListener(l[--a],t,!1);else this.onmousewheel=null;e.removeData(this,"mousewheel-line-height"),e.removeData(this,"mousewheel-page-height")},getLineHeight:function(t){var a=e(t),r=a["offsetParent"in e.fn?"offsetParent":"parent"]();return r.length||(r=e("body")),parseInt(r.css("fontSize"),10)||parseInt(a.css("fontSize"),10)||16},getPageHeight:function(t){return e(t).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};e.fn.extend({mousewheel:function(e){return e?this.bind("mousewheel",e):this.trigger("mousewheel")},unmousewheel:function(e){return this.unbind("mousewheel",e)}})}),function(e,t){"use strict";var a=function(t){var a=[];t.find("thead>tr:first>th").each(function(t){var r=e(this);if(a.push(e.extend({name:t,label:r.html(),html:!0,width:r.outerWidth()},r.data())),r.attr("colspan")&&"1"!==r.attr("colspan"))throw new Erorr("Table th element with colspan attribute is not support.")});var r=[];return t.find("tbody>tr").each(function(){var t=e(this),a={};t.children("td").each(function(t){a[t]=e(this).html()}),r.push(e.extend(a,t.data()))}),{cols:a,array:r,length:r.length}},r="zui.datagrid",o={date:{getter:function(e,t,a){var r=a.options.defaultDateFormater;return Date.create(e).format(r)},setter:function(e,t,a){if("string"==typeof e){var r=parseInt(e,10);isNaN(r)||(e=r)}return Date.timestamp(e)}}},i={},n={page:0,recTotal:0,recPerPage:10},l={fixedLeftUntil:0,fixedTopUntil:0,order:"asc",sortBy:null,pager:n,selections:{}},s=function(t,a){var r=0,o=a.length,i=0,n={};return e.each(t,function(e,t){var l=typeof t;"number"===l||"number"===l?t+="":"string"!==l&&(t=JSON.stringify(l));for(var s=0,d=0;d<o;++d){var c=a[d];t.includes(c)&&(s=t.startsWith(c)?10:20,n[c]||(n[c]=1,i++))}r+=s}),r=i===o?r:0},d=function(e,t){return e==t?0:e<t?-1:1},c={zh_cn:{errorCannotGetDataFromRemote:"无法从远程服务器（{0}）获取数据。",errorCannotHandleRemoteData:"无法处理远程服务器返回的数据。"},zh_tw:{errorCannotGetDataFromRemote:"無法從遠程服務器（{0}）獲取數據。",errorCannotHandleRemoteData:"無法處理遠程服務器返回的數據。"},en:{errorCannotGetDataFromRemote:"Cannot fetch data from remote server {0}.",errorCannotHandleRemoteData:"Cannot handle the remote data."}},h=function(t,n){var s=this,d=s.$=e(t);s.name=r,s.uuid=e.zui.uuid(),s.id="zui-datagrid-"+s.uuid,n=e.extend({},h.DEFAULTS,s.$.data(),n);var g=n.lang||"zh_cn";s.lang=e.isPlainObject(g)?e.extend(!0,{},c[g.lang||e.zui.clientLang()],g):c[g],n.valueOperator=e.extend({},o,n.valueOperator),n.rowDefaultHeight=n.rowDefaultHeight||30,n.headerHeight=n.headerHeight||n.rowDefaultHeight||30,s.options=n,"number"!=typeof n.borderWidth&&(n.borderWidth=1),d.is("table")&&(n.dataSource=e.extend(a(s.$),n.dataSource),d.hide(),d=e('<div class="datagrid" id="datagrid-'+s.uuid+'" />').insertAfter(s.$));var f=d.find(".datagrid-container:first");f.length||(f=e('<div class="datagrid-container" />').appendTo(d)),f.css({width:n.width,borderWidth:n.borderWidth});var u=e(document),p=function(t){var a=f.find(".datagrid-scrollbar-"+t);a.length||(a=e('<div class="datagrid-scrollbar datagrid-scrollbar-'+t+'"><div class="bar"></div></div>').appendTo(f));var o,i,n,l,d=!1,c=null,h=".scrollbar"+t+"."+r+"."+s.uuid,g=function(e){if(d){var a=e["h"===t?"pageX":"pageY"];if(c!==a){c=a,a=a-o+i;var r,h=s.layout[t+"Scroll"];r=n?c-o+l:Math.max(0,Math.min(h.space,a-Math.round(h.barSize/2))),"h"===t?s.setScrollbarOffset(r):s.setScrollbarOffset(null,r)}}};a.on("mousedown",function(r){r.preventDefault(),d=!0;var c=s.layout[t+"Scroll"],f="h"===t?"X":"Y";i=r["offset"+f],o=r["page"+f],n=e(r.target).is(".bar"),l=c.offset,n&&(i+=l),g(r),a.addClass("scrolling"),u.on("mouseup"+h,function(e){d=!1,g(e),u.off(h),a.removeClass("scrolling")}).on("mousemove"+h,g)}),s["$"+t+"Scroll"]=a,s["$"+t+"Scrollbar"]=a.find(".bar")};p("h"),p("v");var v=n.mouseWheelFactor,m=window.navigator.userAgent.match(/Win/i);m&&(v*=20),f.on("mousewheel",function(e){s.scroll(s.layout.scrollLeft-Math.round(e.deltaX*v),s.layout.scrollTop-Math.round(e.deltaY*v)),e.preventDefault()}),s.$container=f;var y=d.find(".datagrid-cells:first");if(y.length||(y=e('<div class="datagrid-cells" />').appendTo(f)),y.toggleClass("datagrid-hover-cell",!!n.hoverCell).toggleClass("datagrid-hover-row",!!n.hoverRow).toggleClass("datagrid-hover-col",!!n.hoverCol).toggleClass("datagrid-hover-shadow",!!n.hoverCol),s.$cells=y,s.isFuncConfigs=e.isFunction(n.configs),s.configs=s.isFuncConfigs?n.configs:e.extend({},i,n.configs),s.layout={scrollLeft:0,scrollTop:0},s.configsCache={},s.userConfigs={},s.states=e.extend(!0,{},l,n.states),s.cells=[],s.setPager(s.states.pager),s.setDataSource(n.dataSource),s.render(!0),n.responsive){f.width();f.on("resize",function(){s.layout.cols=null,s.render()})}if(n.hoverCol&&y.on("mouseenter",".datagrid-cell-head",function(){var t=e(this),a=t.data("col");s.$cells.find(".datagrid-cell.hover").removeClass("hover"),s.$cells.find('.datagrid-cell[data-col="'+a+'"]').addClass("hover")}).on("mouseleave",".datagrid-cell-head.hover",function(){s.$cells.find(".datagrid-cell.hover").removeClass("hover")}),n.sortable&&y.on("click",".datagrid-col-sortable",function(){var t=e(this).data("col"),a=s.getColConfig(t),r=s.states.sortBy,o=s.states.order;r!==a.name?(r=a.name,o="desc"):"desc"===o?o="asc":"asc"===o&&(r=""),s.sortBy(r,o)}),n.checkable&&(n.selectable&&e.fn.selectable?(s.selectable=y.selectable(e.extend({selector:".datagrid-row-cell",trigger:n.checkByClickRow?null:".datagrid-row-cell .datagrid-has-checkbox",clickBehavior:"multi",select:function(e){s.checkRow(e.id,!0)},unselect:function(e){s.checkRow(e.id,!1)}},e.isPlainObject(n.selectable)?n.selectable:null)).data("zui.selectable"),y.on("click",".datagrid-cell-head.datagrid-has-checkbox",function(){s.checkRow(e(this).data("row")),s.selectable.syncSelectionsFromClass()})):y.on("click",n.checkByClickRow?".datagrid-row":".datagrid-has-checkbox",function(t){var a=e(this).data("row");(a||e(t.target).closest(".datagrid-has-checkbox").length)&&s.checkRow(a)})),e.fn.pager){var w=s.$.find(".pager");w.length&&(s.pagerObj=w.pager(e.extend({},s.pager,{onPageChange:function(e){s.setPager(e).render()}})).data("zui.pager"))}if(e.fn.searchBox){var C=s.$.find(".search-box");C&&(s.searchbox=C.searchBox({onSearchChange:function(e){s.search(e)}}))}};h.prototype.setPager=function(t,a,r){var o=this;"object"==typeof t&&(r=t.recPerPage,a=t.recTotal,t=t.page);var i=o.pager,l=e.extend({},i);return i||(i=e.extend({},n)),"number"==typeof r&&r>0&&(i.recPerPage=r),"number"==typeof a&&a>=0&&(i.recTotal=a),"number"==typeof t&&t>=0&&(i.page=t),i.totalPage=i.recTotal&&i.recPerPage?Math.ceil(i.recTotal/i.recPerPage):1,i.page=Math.max(0,Math.min(i.page,i.totalPage)),i.pageRecCount=i.recTotal,i.page&&i.recTotal&&(i.page<i.totalPage?i.pageRecCount=i.recPerPage:i.page>1&&(i.pageRecCount=i.recTotal-i.recPerPage*(i.page-1))),i.skip=i.page>1?(i.page-1)*i.recPerPage:0,i.end=i.skip+i.pageRecCount,o.pager=i,l.page===i.page&&l.recTotal===i.recTotal&&l.recPerPage===i.recPerPage||(o.layout.cols=null,o.scroll(0,0)),o},h.prototype.goToPage=function(e){return this.setPager(e).render()},h.prototype.setSearch=function(a){return a!==t&&null!==a||(a=""),this.states.search=e.trim(a),this},h.prototype.search=function(e){var t=this;return e!==t.states.search&&t.pager.page&&t.setPager(1),t.setSearch(e).render()},h.prototype.setSorter=function(e,a){var r=this;return a===t&&(a="desc"===r.states.order?"asc":"desc"),r.states.order=a.toLowerCase(),r.states.sortBy=e,r},h.prototype.sortBy=function(e,t){return this.setSorter(e,t).render()},h.prototype.setDataSource=function(a,r){var o=this,i={},n=o.dataSource&&o.dataSource.cols;if(e.isArray(a)?(i.array=a,i.length=a.length,o.setPager("",a.length)):e.isPlainObject(a)?i=e.extend(i,a):"string"==typeof a&&(i.remote=a),i.cache===!0||i.cache===t?(i.cache=[],i.cacheSize=1):"number"==typeof i.cache&&(i.cacheSize=i.cache,i.cache=[]),e.isArray(i.data)?(i.array=i.data,i.length=i.array.length,o.setPager("",i.length),delete i.data):!i.data&&e.isFunction(i.getByIndex)&&o.setPager("",i.length),o.dataSource=i,r=r||i.cols||n||[],r.length)for(var l=0;l<r.length;++l){var s=r[l];"string"==typeof s&&(r[l]={name:s})}r!==n&&(o.layout.cols=null),i.cols=r},h.prototype.filterData=function(e,t){var a=this,r=e,o=null;if(t.search){var i=t.search.replace(/\s{2,}/g," ").split(" ");r=[];for(var n=a.options.searchFunc||s,l=0;l<e.length;++l){var c=e[l],h=n(c,i,l,t,a);h&&(null===o&&(o="number"==typeof h),o&&(c._SCORE=h),r.push(c))}}if(a.setPager(-1,r.length),r.length){var g=t.sortBy||!!o&&"_SCORE";if(g){var f="_SCORE"===g?"DESC":t.order,u=a.getColConfigByName(g),p="desc"===f,v=u&&u.sortFunc||a.options.sortFunc||d;r.sort(function(e,t){var r=v(e[g],t[g],e,t,g,a);return p?-1*r:r})}var m=a.pager;if(m.page){m.page>1?m.page*m.recPerPage:0;r=r.slice(m.skip,m.end)}}return r},h.prototype.getFilterParams=function(){var e=this,t=e.states;return{page:e.pager.page,recPerPage:e.pager.recPerPage,search:t.search,sortBy:t.sortBy,order:t.order}},h.prototype.loadData=function(t){var a=this;a.loadingId=e.zui.uuid();var r=function(e){return a.$.callComEvent(a,"onLoad",e),t&&t(e)},o=a.getFilterParams(),i=[o.page,o.recPerPage,o.search,o.sortBy,o.order].join("&"),n=a.getData(i);if(n)return r(n);var l=a.dataSource;if(l.array)return n=a.filterData(l.array,o),a.resetData(i,n,a.pager),r(n);if(l.getByIndex)return n=l.getByIndex,a.resetData(i,n),r(n);var s=l.loader,d=l.remote;if(!s&&d&&(s=function(t,r){var o=e.isFunction(d)?d(t,a):{url:d};e.ajax(e.extend({type:"GET",data:t,dataType:"json",success:function(t,o,i){if(l.remoteConverter&&(t=l.remoteConverter(t,o,i,a)),"string"==typeof t&&(t=e.parseJSON(t)),e.isPlainObject(t)&&t.data){var n=t.result||t.status;"success"===n||"ok"===n||200===n?r(t):r(!1,t.message||t.reason||a.lang.errorCannotHandleRemoteData,t)}else r(!1,a.lang.errorCannotHandleRemoteData,t)},error:function(){r(!1,a.lang.errorCannotGetDataFromRemote.format(l.remote))}},o))}),!s)return r(!1);a.renderLoading(!0);var c=a.loadingId;s(o,function(e,t){if(c===a.loadingId){if(a.renderLoading(!1),t)return a.showMessage(t,"danger"),void r(!1);a.resetData(i,e.data,e.pager),r(e.data)}})},h.prototype.getDataItem=function(e,t,a){var r=this;return t=t||r.getData(),"function"==typeof t?(a=a||r.getFilterParams(),t(e,a)):t[e]},h.prototype.showMessage=function(a,r,o){var i=this;i.msgerAutoCloseTimer&&(clearTimeout(i.msgerAutoCloseTimer),i.msgerAutoCloseTimer=null);var n=i.$container.find(".datagrid-messager");return a?(r=r||"info",o===t&&(o=5e3),n.length||(n=e('<div class="datagrid-messager" style="display: none"><div class="content"></div><button type="button" class="close">×</button></div>').appendTo(i.$container).on("click",".close",function(){n.slideUp(),i.msgerAutoCloseTimer&&(clearTimeout(i.msgerAutoCloseTimer),i.msgerAutoCloseTimer=null)})),n.attr("class","datagrid-messager bg-"+r).find(".content").text(a),n.slideDown(),void(o&&(i.msgerAutoCloseTimer=setTimeout(function(){n.slideUp(),i.msgerAutoCloseTimer=null},o)))):void n.slideUp()},h.prototype.renderLoading=function(a){var r=this;a!==t&&(r.states.loading=a);var o=r.$container.find(".datagrid-loading");a?(o.length||(o=e('<div class="datagrid-loading" style="display: none"><div class="content"><i class="icon icon-spin icon-spinner icon-2x"></i><div className="datagrid-loading-message"></div></div></div>').appendTo(r.$container)),o.find(".datagrid-loading-message").text("string"==typeof a?a:""),o.fadeIn()):o.fadeOut()},h.prototype.getData=function(e){var t=this.dataSource,a=null;if(e&&e!==t.dataId){if(t.cache&&t.cache.length)for(var r=t.cache.length-1;r>=0;--r){var o=t.cache[r];if(o.id===e){t.dataId=e,t.data=o.data,this.setPager(o.pager),a=o.data;break}}}else a=t.data;return a},h.prototype.resetData=function(t,a,r){var o=this.dataSource;if(o.dataId=t,o.data=a,o.cache){for(var i=o.cache.length-1;i>0;--i){var n=o.cache[i];if(n.id===t){o.cache.splice(i,1);break}}for(o.cache.push({id:t,data:a,pager:e.extend({},r)});o.cache.length>o.cacheSize;)o.cache.shift()}r&&this.setPager(r)},h.prototype.getRowLayout=function(e){var t=this.layout;if(0===e)return{top:0,height:t.headerHeight};var a=t.rowHeight;return{height:a,top:t.headerHeight+(e>1?(e-1)*a:0)+e*t.borderWidth}},h.prototype.updateLayout=function(){var a=this,r=a.options,o=a.layout,i=(a.data,a.pager),n=i.pageRecCount,l=a.$container,s=l.width(),d=a.dataSource;if(!d.cols.length&&n&&e.each(a.getDataItem(0),function(e){d.cols.push({name:e})}),!o.cols){for(var c,h,g=d.cols,f=r.colAutoMinWidth,u=r.colAutoDefaultWidth,p=0,v=0,m=r.rowIndexWidth,y=[{left:0,width:r.showRowIndex?"auto"===m?8*(n+a.pager.skip+"").length+18:m:0}],w=0,C=y[0].width,b=!1,x=0,S=0,T=0;T<g.length;++T){var D=g[T];D&&(h=D.width,h&&"auto"!==h||(h=.1),c={left:0},h>=1?(D.minWidth!==t&&(h=Math.max(h,D.minWidth)),c.width=h,C+=h):(D.minWidth===t&&(D.minWidth=f),c.grow=h,p+=h,v+=D.minWidth,x<=c.grow&&(x=c.grow,b=T+1)),c.minWidth=D.minWidth,!S&&D.checkbox&&(S=T+1,c.checkbox=!0),y.push(c))}r.checkable&&!S&&(y[0].checkbox=!0,"auto"===m&&(y[0].width+=30,C+=30));for(var M=s-C,R=M<v,P=y.length,L=0;L<P;++L){if(c=y[L],h=c.width,h||0===h||(h=R?u*c.grow*10:M*c.grow/p,h=Math.floor(Math.max(c.minWidth,h)),c.width=h),L>0){var k=y[L-1];c.left=k.left+k.width}w+=h}var H=s-w;b&&H>0&&(y[b].width+=H,w+=H),o.width=w,o.cols=y}o.containerWidth=s,o.rowHeight=r.rowDefaultHeight,o.borderWidth=r.borderWidth,o.headerHeight=r.showHeader?r.headerHeight:0,o.rowsLength=n+1,o.colsLength=o.cols.length,o.height=o.headerHeight+n*(o.rowHeight+o.borderWidth),o.spanMap={};var W=r.height;"page"===W&&(W=o.headerHeight+a.pager.recPerPage*(o.rowHeight+o.borderWidth)),l.css("height",W),o.containerHeight=W,o.vScrollSpare=o.height-o.containerHeight,o.hScrollSpare=o.width-o.containerWidth,a.layout=o;var F=!!i.page||r.partialRendering;return"auto"===F&&(F=o.height>2*o.containerHeight),o.partialRendering=F,o},h.prototype.getCell=function(e,a){var r,o,i=this,n=i.getCellConfig(e,a),l=a>0?i.dataSource.cols[a-1]:null,s={rowIndex:e,colIndex:a,config:n,checked:i.isRowChecked(n.rowId)};if(0===a){r="index";var d=e>0?i.pager.skip+e:"";o=n.label!==t?n.label:d}else 0===e?(r="head",o=n.label!==t?n.label:n.name!==t?n.name:a):(r="cell",o=n.data&&n.data[i.options.dataItemIsArray?a:l.name]);if(e>0){var c=i.options.valueOperator,h=n.valueType,g=n.valueOperator||(c&&h?c[h]:null);g&&g.getter&&(o=g.getter(o,s,i))}s.value=o,s.type=r;var f=i.layout.spanMap;if(f[n.id]||n.hidden)s.hidden=!0;else if(n.colspan&&n.colspan>1||n.rowspan&&n.rowspan>1){for(var u=e+(n.rowspan||1),p=a+(n.colspan||1),v=e;v<u;++v)for(var m=a;m<p;++m)v===e&&m===a||(f["R"+v+"C"+m]=n.id);n.span=!0}return s},h.prototype.getRowConfig=function(a){var r=this,o="R"+a,i=r.configsCache[o];i||(i=e.extend({},r.isFuncConfigs?r.configs(o):r.configs[o],r.userConfigs[o]),r.configsCache[o]=i);var n=a>0?r.getDataItem(a-1):null;i.data=n;var o=n&&(n.rowId||n.id);return i.rowId=o!==t?o:0===a?"#header":a,i},h.prototype.getColConfigByName=function(e){for(var t=this.dataSource.cols,a=0;a<t.length;++a)if(t[a].name===e)return this.getColConfig(a+1);return null},h.prototype.getColConfig=function(t){var a=this,r="C"+t,o=null;return o||(o=e.extend({valueType:"string"},t>0?a.dataSource.cols[t-1]:null,a.layout.cols?a.layout.cols[t]:null,a.isFuncConfigs?a.configs(r):a.configs[r],a.userConfigs[r])),o},h.prototype.getCellConfig=function(t,a){var r=this,o="R"+t+"C"+a,i=null;return i||(i=e.extend({id:o},r.getColConfig(a),r.getRowConfig(t),r.isFuncConfigs?r.configs(o):r.configs[o],r.userConfigs[o])),i},h.prototype.isRowChecked=function(e){return!!this.states.selections[e]},h.prototype.checkRow=function(e,a){var r=this,o=r.states.selections,i=r.getRowConfig(e),n=i.rowId;if(a===t&&(a=!o[n]),o[n]!==a){if(a?o[n]=i:(delete o[n],e>0&&o["#header"]&&(delete o["#header"],r.renderRow(0))),r.renderRow(e),0===e&&r.layout.rowsLength<500)for(var l=1;l<r.layout.rowsLength;++l)r.checkRow(l,a);return r.renderFixeds(),a}},h.prototype.getCheckItems=function(){var t=this.states.selections,a=[];return t&&e.each(t,function(e){a.push(t[e].data)}),a},h.prototype.renderCell=function(t,a,r){var o=this,i=o.options,n=o.getCell(t,a),l=n.config;if(!n.hidden){var s=l.checkbox,d=[o.id,"cell",t,a].join("-"),c=e("#"+d);if(!c.length&&(r=r||e("#"+o.id+"-row-"+t),c=(i.cellCreator?i.cellCreator(n,o):e('<div class="datagrid-cell" />')).appendTo(r),c.attr({id:d,"data-type":n.type,"data-col":n.colIndex,"data-row":n.rowIndex}).toggleClass("datagrid-cell-head",0===t).toggleClass("datagrid-cell-cell","cell"===n.type).toggleClass("datagrid-cell-index",0===a),s)){var h=c.find(".datagrid-checkbox");h.length||(h=e('<div class="checkbox-primary datagrid-checkbox"><label></label></div>').prependTo(c.addClass("datagrid-has-checkbox"))),c.append('<span class="content"></span>')}var g=i.borderWidth,f=o.layout,u=f.colsLength,p={top:g?-g:0,bottom:g?-g:0,left:g?l.left-g:l.left,width:g?l.width+(u-1===a?2:1)*g:l.width,borderWidth:g};if(l.span&&(l.rowspan&&l.rowspan>1&&(p.bottom-=(l.rowspan-1)*(f.rowHeight+g)),l.colspan&&l.colspan>1))for(var v=a+l.colspan,m=a+1;m<v;++m){var y=o.getCell(t,m);p.width+=y.config.width}var w=l.style;e.isFunction(w)&&(w=w(n,p,o));var C=e.extend({},w,p);if(c.css(C).toggleClass("datagrid-cell-span",!!l.span),i.cellFormator)i.cellFormator(c,n,o);else{var b=s?c.find(".content"):c;b[n.config.html?"html":"text"](n.value),l.className&&c.addClass(l.className)}if(a>0&&0===t&&i.sortable&&l.sort!==!1){var x=!1;l.name===o.states.sortBy&&(x="desc"===o.states.order?"down":"up");var S=c.find(".datagrid-sorter");S.length||(S=e('<div class="datagrid-sorter"><i class="icon icon-sort"></i></div>').appendTo(c),c.addClass("datagrid-col-sortable")),S.toggleClass("datagrid-sort-up","up"===x).toggleClass("datagrid-sort-down","down"===x)}return s&&(c.find(".datagrid-checkbox").toggleClass("checked",n.checked),r.toggleClass("active",n.checked)),c}},h.prototype.renderRow=function(t){var a=this,r=a.layout,o=a.options,i=a.getRowLayout(t),n=r.colsLength,l=a.id+"-row-"+t,s=e("#"+l);s.length?r.partialRendering&&s.css("top",i.top-r.scrollTop):(s=(o.rowCreator?o.rowCreator(t,a):e('<div class="datagrid-row" />')).appendTo(a.$cells),s.attr({id:l,"data-row":t,"data-id":t}).css({top:r.partialRendering?i.top-r.scrollTop:i.top,height:i.height}).toggleClass("datagrid-row-head",0===t).toggleClass("datagrid-row-cell",0!==t));for(var d=0;d<n;++d)a.renderCell(t,d,s);return s},h.prototype.renderData=function(){var t=this,a=t.layout;a.cols||t.updateLayout();var r=1,o=a.rowsLength-1;if(a.partialRendering){var i=a.rowHeight+a.borderWidth;r=Math.min(o,Math.max(1,Math.floor((a.scrollTop-a.headerHeight)/i))),o=Math.min(o,Math.max(1,Math.ceil((a.scrollTop+a.containerHeight-a.headerHeight)/i))),t.$cells.find(".datagrid-row").each(function(){var t=e(this),a=t.data("row");a>0&&!t.hasClass("datagrid-fixed")&&(a<r||a>o)&&t.remove()})}t.options.showHeader&&t.renderRow(0);for(var n=r;n<=o;++n)t.renderRow(n);if(a.vScrollSpare){var l=t.states,s=l.fixedTopUntil,d=l.fixedBottomFrom;if("number"==typeof s&&s>0&&s<r)for(var n=1;n<=s;++n)t.renderRow(n);if("number"==typeof d&&d>0&&d>o)for(var n=d;n<=a.rowsLength-1;++n)t.renderRow(n)}t.pagerObj&&t.pagerObj.set(t.pager)},h.prototype.render=function(e){var t=this,a=t.options;return!e&&a.renderDelay?(t.renderDelayTimer&&clearTimeout(t.renderDelayTimer),t.renderDelayTimer=setTimeout(function(){t.render(!0)},a.renderDelay),t):(t.renderDelayTimer&&(clearTimeout(t.renderDelayTimer),t.renderDelayTimer=null),t.loadData(function(e){var a=t.updateLayout();t.$cells.css({width:a.width,height:a.partialRendering?a.containerHeight:t.layout.height}),t.renderData(),t.renderScrolls(),t.renderFixeds(),t.$.callComEvent(t,"onRender")}),t)},h.prototype.setScrollbarOffset=function(e,t){var a=this,r=a.layout,o=r.scrollLeft,i=r.scrollTop;if("number"==typeof e){var n=r.hScroll;n.offset!==e&&(o=Math.round(e*r.hScrollSpare/n.space))}if("number"==typeof t){var l=r.vScroll;l.offset!==t&&(i=Math.round(t*r.vScrollSpare/l.space))}a.scroll(o,i)},h.prototype.renderScrolls=function(){var e=this,t=e.layout,a=t.vScrollSpare,r=t.hScrollSpare,o=a>0,i=r>0;if(e.$vScroll.toggle(o),e.$hScroll.toggle(i),t.scrollLeft=i?Math.max(0,Math.min(r,t.scrollLeft)):0,t.scrollTop=o?Math.max(0,Math.min(a,t.scrollTop)):0,o){var n=e.$vScrollbar,l=t.containerHeight/t.height,s=Math.max(20,Math.floor(l*t.containerHeight)),d=t.containerHeight-s,c=d/a,h=Math.round(t.scrollTop*c);t.vScroll={space:d,size:a,scale:c,barSize:s,offset:h};var g={height:s,top:h};n.css(g)}if(i){var n=e.$hScrollbar,l=t.containerWidth/t.width,s=Math.max(20,Math.floor(l*t.containerWidth)),d=t.containerWidth-s,h=Math.round(t.scrollLeft*d/r),g={width:s,left:h};t.hScroll={offset:h,space:d,size:r,barSize:s},n.css(g)}e.$cells.css({top:t.partialRendering?0:-t.scrollTop,left:-t.scrollLeft})},h.prototype.scroll=function(e,t,a){var r=this,o=new Date,i=r.options.scrollDelay;if(i){if(!a&&r.lastScrollTime&&o-r.lastScrollTime<i)return r.scrollDelayTimer&&clearTimeout(r.scrollDelayTimer),void(r.scrollDelayTimer=setTimeout(function(){r.scroll(e,t)},i-(o-r.lastScrollTime)));r.scrollDelayTimer&&(clearTimeout(r.scrollDelayTimer),r.scrollDelayTimer=null),r.lastScrollTime=o}var n=r.layout,l=!1,s=!1;"number"==typeof e&&(e=Math.max(0,Math.min(e,n.width-n.containerWidth)),e!==n.scrollLeft&&(l=!0,n.scrollLeft=e)),"number"==typeof t&&(t=Math.max(0,Math.min(t,n.height-n.containerHeight)),t!==n.scrollTop&&(s=!0,n.scrollTop=t)),s&&n.partialRendering&&r.renderData(),(l||s)&&(r.renderScrolls(),r.renderFixeds()),r.$.callComEvent(r,"onScroll",[e,t,{vScrolled:s,hScrolled:l}])},h.prototype.renderFixeds=function(){var t=this,a=t.states,r=t.layout;if(t.$cells.find(".datagrid-fixed").removeClass("datagrid-fixed"),t.$cells.find(".datagrid-fixed-edge-top").removeClass("datagrid-fixed-edge-top"),t.$cells.find(".datagrid-fixed-edge-bottom").removeClass("datagrid-fixed-edge-bottom"),t.$cells.find(".datagrid-fixed-edge-left").removeClass("datagrid-fixed-edge-left"),t.$cells.find(".datagrid-fixed-edge-right").removeClass("datagrid-fixed-edge-right"),r.vScrollSpare){var o=a.fixedTopUntil;if("number"==typeof o&&o>-1){o=Math.min(o,r.rowsLength);for(var i=0;i<=o;++i){var n=t.getRowLayout(i),l=e("#"+t.id+"-row-"+i),s=r.partialRendering?n.top:n.top+r.scrollTop;l.addClass("datagrid-fixed").css("top",s),i===o&&r.scrollTop&&l.addClass("datagrid-fixed-edge-top")}}else o=-1;var d=a.fixedBottomFrom;if("number"==typeof d&&d>-1){d=Math.max(o>-1?o+1:1,Math.min(d,r.rowsLength));for(var i=d;i<r.rowsLength;++i){var n=t.getRowLayout(i),l=e("#"+t.id+"-row-"+i),s=r.partialRendering?n.top-r.vScrollSpare:n.top-r.vScrollSpare+r.scrollTop;l.addClass("datagrid-fixed").css("top",s),i===d&&r.scrollTop<r.vScrollSpare&&l.addClass("datagrid-fixed-edge-bottom")}}}if(r.hScrollSpare){var c=a.fixedLeftUntil;if("number"==typeof c&&c>-1){c=Math.min(c,r.colsLength);for(var i=0;i<=c;++i){var h=r.cols[i],g=t.$cells.find('.datagrid-cell[data-col="'+i+'"]'),f=h.left+r.scrollLeft-r.borderWidth;g.addClass("datagrid-fixed").css("left",f),i===c&&r.scrollLeft&&g.addClass("datagrid-fixed-edge-left")}}else c=-1;var u=a.fixedRightFrom;if("number"==typeof u&&u>-1){u=Math.max(c>-1?c+1:1,Math.min(u,r.colsLength));for(var i=u;i<r.colsLength;++i){var h=r.cols[i],g=t.$cells.find('.datagrid-cell[data-col="'+i+'"]'),f=h.left-r.hScrollSpare+r.scrollLeft;g.addClass("datagrid-fixed").css("left",f),i===u&&r.scrollLeft<r.hScrollSpare&&g.addClass("datagrid-fixed-edge-right")}}}},h.DEFAULTS={width:"auto",height:400,rowDefaultHeight:36,colAutoDefaultWidth:80,colAutoMinWidth:50,showHeader:!0,headerHeight:36,showRowIndex:!0,rowIndexWidth:"auto",borderWidth:1,hoverRow:!0,hoverCol:!0,hoverCell:!1,responsive:!0,defaultDateFormater:"yyyy-MM-dd hh:mm",partialRendering:"auto",scrollDelay:0,renderDelay:100,checkByClickRow:!0,selectable:!0,mouseWheelFactor:1},e.fn.datagrid=function(t){return this.each(function(){var a=e(this),o=a.data(r),i="object"==typeof t&&t;o||a.data(r,o=new h(this,i)),"string"==typeof t&&o[t]()})},h.NAME=r,e.fn.datagrid.Constructor=h,e(function(){e('[data-ride="datagrid"]').datagrid()})}(jQuery,void 0);