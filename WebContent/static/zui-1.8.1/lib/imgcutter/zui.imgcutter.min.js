/*!
 * ZUI: 图片裁剪工具 - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */
!function(t,i,e){"use strict";t.fn.draggable||console.error("img-cutter requires draggable.js"),t.zui.imgReady||console.error("img-cutter requires image.ready.js");var h="zui.imgCutter",o=function(i,e){this.$=t(i),this.initOptions(e),this.init()};o.DEFAULTS={coverColor:"#000",coverOpacity:.6,defaultWidth:128,defaultHeight:128,minWidth:48,minHeight:48},o.prototype.callEvent=function(t,i){var o=this.$.callEvent(t+"."+h,i,this);return!(o.result!==e&&!o.result)},o.prototype.initOptions=function(i){this.options=t.extend({},o.DEFAULTS,this.$.data(),i),this.options.coverOpacityIE=100*this.options.coverOpacity,this.clipWidth=this.options.defaultWidth,this.clipHeight=this.options.defaultHeight},o.prototype.init=function(){this.initDom(),this.initSize(),this.bindEvents()},o.prototype.initDom=function(){this.$canvas=this.$.children(".canvas"),this.$img=this.$canvas.children("img"),this.$actions=this.$.children(".actions"),this.$btn=this.$.find(".img-cutter-submit"),this.$preview=this.$.find(".img-cutter-preview"),this.options.img=this.$img.attr("src"),this.$canvas.append('<div class="cover" style="background: {coverColor}; opacity: {coverOpacity}; filter:alpha(opacity={coverOpacityIE});"></div><div class="controller" style="width: {defaultWidth}px; height: {defaultHeight}px"><div class="control" data-direction="top"></div><div class="control" data-direction="right"></div><div class="control" data-direction="bottom"></div><div class="control" data-direction="left"></div><div class="control" data-direction="top-left"></div><div class="control" data-direction="top-right"></div><div class="control" data-direction="bottom-left"></div><div class="control" data-direction="bottom-right"></div></div><div class="cliper"><img src="{img}"/></div>'.format(this.options)),this.$cover=this.$canvas.children(".cover"),this.$controller=this.$canvas.children(".controller"),this.$cliper=this.$canvas.children(".cliper"),this.$chipImg=this.$cliper.children("img"),this.options.fixedRatio&&this.$.addClass("fixed-ratio")},o.prototype.resetImage=function(t){var i=this;i.options.img=t,i.$img.attr("src",t),i.$chipImg.attr("src",t),i.imgWidth=e,i.left=e,i.initSize()},o.prototype.initSize=function(){var h=this;h.imgWidth||t.zui.imgReady(h.options.img,function(){h.imgWidth=this.width,h.imgHeight=this.height,h.callEvent("ready")});var o=setInterval(function(){h.imgWidth&&(clearInterval(o),h.width=i.min(h.imgWidth,h.$.width()),h.$canvas.css("width",this.width),h.$cliper.css("width",this.width),h.height=h.$canvas.height(),h.left===e&&(h.left=i.floor((h.width-h.$controller.width())/2),h.top=i.floor((h.height-h.$controller.height())/2)),h.refreshSize())},0)},o.prototype.refreshSize=function(t){var e=this.options;this.clipWidth=i.max(e.minWidth,i.min(this.width,this.clipWidth)),this.clipHeight=i.max(e.minHeight,i.min(this.height,this.clipHeight)),e.fixedRatio&&(t&&"height"===t?(this.clipWidth=i.max(e.minWidth,i.min(this.width,this.clipHeight*e.defaultWidth/e.defaultHeight)),this.clipHeight=this.clipWidth*e.defaultHeight/e.defaultWidth):(this.clipHeight=i.max(e.minHeight,i.min(this.height,this.clipWidth*e.defaultHeight/e.defaultWidth)),this.clipWidth=this.clipHeight*e.defaultWidth/e.defaultHeight)),this.left=i.min(this.width-this.clipWidth,i.max(0,this.left)),this.top=i.min(this.height-this.clipHeight,i.max(0,this.top)),this.right=this.left+this.clipWidth,this.bottom=this.top+this.clipHeight,this.$controller.css({left:this.left,top:this.top,width:this.clipWidth,height:this.clipHeight}),this.$cliper.css("clip","rect({0}px {1}px {2}px {3}px".format(this.top,this.left+this.clipWidth,this.top+this.clipHeight,this.left)),this.callEvent("change",{top:this.top,left:this.left,bottom:this.bottom,right:this.right,width:this.clipWidth,height:this.clipHeight})},o.prototype.getData=function(){var t=this;return t.data={originWidth:t.imgWidth,originHeight:t.imgHeight,scaleWidth:t.width,scaleHeight:t.height,width:t.right-t.left,height:t.bottom-t.top,left:t.left,top:t.top,right:t.right,bottom:t.bottom,scaled:t.imgWidth!=t.width||t.imgHeight!=t.height},t.data},o.prototype.bindEvents=function(){var e=this,h=this.options;this.$.resize(t.proxy(this.initSize,this)),this.$btn.hover(function(){e.$.toggleClass("hover")}).click(function(){var i=e.getData();if(e.callEvent("before",i)){var o=h.post||h.get||h.url||null;null!==o&&t.ajax({type:h.post?"POST":"GET",url:o,data:i}).done(function(t){e.callEvent("done",t)}).fail(function(t){e.callEvent("fail",t)}).always(function(t){e.callEvent("always",t)})}}),this.$controller.draggable({move:!1,container:this.$canvas,drag:function(t){e.left+=t.smallOffset.x,e.top+=t.smallOffset.y,e.refreshSize()}}),this.$controller.children(".control").draggable({move:!1,container:this.$canvas,stopPropagation:!0,drag:function(t){var o=t.element.data("direction"),s=t.smallOffset,a=!1;switch(o){case"left":case"top-left":case"bottom-left":e.left+=s.x,e.left=i.min(e.right-h.minWidth,i.max(0,e.left)),e.clipWidth=e.right-e.left;break;case"right":case"top-right":case"bottom-right":e.clipWidth+=s.x,e.clipWidth=i.min(e.width-e.left,i.max(h.minWidth,e.clipWidth))}switch(o){case"top":case"top-left":case"top-right":e.top+=s.y,e.top=i.min(e.bottom-h.minHeight,i.max(0,e.top)),e.clipHeight=e.bottom-e.top,a=!0;break;case"bottom":case"bottom-left":case"bottom-right":e.clipHeight+=s.y,e.clipHeight=i.min(e.height-e.top,i.max(h.minHeight,e.clipHeight)),a=!0}e.refreshSize(a)}})},t.fn.imgCutter=function(i){return this.each(function(){var e=t(this),s=e.data(h),a="object"==typeof i&&i;s||e.data(h,s=new o(this,a)),"string"==typeof i&&s[i]()})},t.fn.imgCutter.Constructor=o,t(function(){t('[data-toggle="imgCutter"]').imgCutter()})}(jQuery,Math,void 0);