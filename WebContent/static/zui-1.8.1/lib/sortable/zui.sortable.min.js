/*!
 * ZUI: 排序 - v1.8.1 - 2018-01-18
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2018 cnezsoft.com; Licensed MIT
 */
+function(t,e,r){"use strict";if(!t.fn.droppable)return void console.error("Sortable requires droppable.js");var a="zui.sortable",n={selector:"li,div",dragCssClass:"invisible",sortingClass:"sortable-sorting"},o="order",s=function(e,r){var a=this;a.$=t(e),a.options=t.extend({},n,a.$.data(),r),a.init()};s.DEFAULTS=n,s.NAME=a,s.prototype.init=function(){var e=this,r=e.$,a=e.options,n=a.selector,s=a.sortingClass,i=a.dragCssClass,l=a.reverse,d=function(r){r=r||e.getItems(1);var a=[];r.each(function(){var e=t(this).data(o);"number"==typeof e&&a.push(e)}),a.sort(function(t,e){return t-e});for(var n=r.length;a.length<n;)a.push(a.length?a[a.length-1]+1:0);l&&a.reverse(),e.maxOrder=0,r.each(function(r){e.maxOrder=Math.max(e.maxOrder,a[r]),t(this).data(o,a[r]).attr("data-"+o,a[r])})};d(),r.droppable({handle:a.trigger,target:n,selector:n,container:r,always:a.always,flex:!0,lazy:a.lazy,canMoveHere:a.canMoveHere,nested:a.nested,before:a.before,mouseButton:a.mouseButton,start:function(t){i&&t.element.addClass(i),e.trigger("start",t)},drag:function(t){if(r.addClass(s),t.isIn){var a=t.element,n=t.target,i=a.data(o),u=n.data(o);if(i||0===i||(e.maxOrder++,i=e.maxOrder,a.attr("data-"+o,i)),u||0===u||(e.maxOrder++,u=e.maxOrder,n.attr("data-"+o,u)),i==u)return;i>u?n[l?"after":"before"](a):n[l?"before":"after"](a);var f=e.getItems(1);d(f),e.trigger(o,{list:f,element:a})}},finish:function(t){i&&t.element&&t.element.removeClass(i),r.removeClass(s),e.trigger("finish",{list:e.getItems(1),element:t.element})}})},s.prototype.destroy=function(){this.$.droppable("destroy"),this.$.data(a,null)},s.prototype.reset=function(){this.destroy(),this.init()},s.prototype.getItems=function(e){var r=this.$.children(this.options.selector).not(".drag-shadow");return e?r:r.map(function(){var e=t(this);return{item:e,order:e.data("order")}})},s.prototype.trigger=function(e,r){return t.zui.callEvent(this.options[e],r,this)},t.fn.sortable=function(e){return this.each(function(){var r=t(this),n=r.data(a),o="object"==typeof e&&e;n?"object"==typeof e&&n.reset():r.data(a,n=new s(this,o)),"string"==typeof e&&n[e]()})},t.fn.sortable.Constructor=s}(jQuery,window,document);