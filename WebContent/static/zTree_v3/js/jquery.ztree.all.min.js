!function($){var settings={},roots={},caches={},_consts={className:{BUTTON:"button",LEVEL:"level",ICO_LOADING:"ico_loading",SWITCH:"switch",NAME:"node_name"},event:{NODECREATED:"ztree_nodeCreated",CLICK:"ztree_click",EXPAND:"ztree_expand",COLLAPSE:"ztree_collapse",ASYNC_SUCCESS:"ztree_async_success",ASYNC_ERROR:"ztree_async_error",REMOVE:"ztree_remove",SELECTED:"ztree_selected",UNSELECTED:"ztree_unselected"},id:{A:"_a",ICON:"_ico",SPAN:"_span",SWITCH:"_switch",UL:"_ul"},line:{ROOT:"root",ROOTS:"roots",CENTER:"center",BOTTOM:"bottom",NOLINE:"noline",LINE:"line"},folder:{OPEN:"open",CLOSE:"close",DOCU:"docu"},node:{CURSELECTED:"curSelectedNode"}},_setting={treeId:"",treeObj:null,view:{addDiyDom:null,autoCancelSelected:!0,dblClickExpand:!0,expandSpeed:"fast",fontCss:{},nameIsHTML:!1,selectedMulti:!0,showIcon:!0,showLine:!0,showTitle:!0,txtSelectedEnable:!1},data:{key:{children:"children",name:"name",title:"",url:"url",icon:"icon"},simpleData:{enable:!1,idKey:"id",pIdKey:"pId",rootPId:null},keep:{parent:!1,leaf:!1}},async:{enable:!1,contentType:"application/x-www-form-urlencoded",type:"post",dataType:"text",url:"",autoParam:[],otherParam:[],dataFilter:null},callback:{beforeAsync:null,beforeClick:null,beforeDblClick:null,beforeRightClick:null,beforeMouseDown:null,beforeMouseUp:null,beforeExpand:null,beforeCollapse:null,beforeRemove:null,onAsyncError:null,onAsyncSuccess:null,onNodeCreated:null,onClick:null,onDblClick:null,onRightClick:null,onMouseDown:null,onMouseUp:null,onExpand:null,onCollapse:null,onRemove:null}},_initRoot=function(e){var t=data.getRoot(e);t||(t={},data.setRoot(e,t)),t[e.data.key.children]=[],t.expandTriggerFlag=!1,t.curSelectedList=[],t.noSelection=!0,t.createdNodes=[],t.zId=0,t._ver=(new Date).getTime()},_initCache=function(e){var t=data.getCache(e);t||(t={},data.setCache(e,t)),t.nodes=[],t.doms=[]},_bindEvent=function(e){var t=e.treeObj,n=consts.event;t.bind(n.NODECREATED,function(t,n,o){tools.apply(e.callback.onNodeCreated,[t,n,o])}),t.bind(n.CLICK,function(t,n,o,a,r){tools.apply(e.callback.onClick,[n,o,a,r])}),t.bind(n.EXPAND,function(t,n,o){tools.apply(e.callback.onExpand,[t,n,o])}),t.bind(n.COLLAPSE,function(t,n,o){tools.apply(e.callback.onCollapse,[t,n,o])}),t.bind(n.ASYNC_SUCCESS,function(t,n,o,a){tools.apply(e.callback.onAsyncSuccess,[t,n,o,a])}),t.bind(n.ASYNC_ERROR,function(t,n,o,a,r,i){tools.apply(e.callback.onAsyncError,[t,n,o,a,r,i])}),t.bind(n.REMOVE,function(t,n,o){tools.apply(e.callback.onRemove,[t,n,o])}),t.bind(n.SELECTED,function(t,n,o){tools.apply(e.callback.onSelected,[n,o])}),t.bind(n.UNSELECTED,function(t,n,o){tools.apply(e.callback.onUnSelected,[n,o])})},_unbindEvent=function(e){var t=e.treeObj,n=consts.event;t.unbind(n.NODECREATED).unbind(n.CLICK).unbind(n.EXPAND).unbind(n.COLLAPSE).unbind(n.ASYNC_SUCCESS).unbind(n.ASYNC_ERROR).unbind(n.REMOVE).unbind(n.SELECTED).unbind(n.UNSELECTED)},_eventProxy=function(e){var t=e.target,n=data.getSetting(e.data.treeId),o="",a=null,r="",i="",d=null,s=null,l=null;if(tools.eqs(e.type,"mousedown")?i="mousedown":tools.eqs(e.type,"mouseup")?i="mouseup":tools.eqs(e.type,"contextmenu")?i="contextmenu":tools.eqs(e.type,"click")?tools.eqs(t.tagName,"span")&&null!==t.getAttribute("treeNode"+consts.id.SWITCH)?(o=tools.getNodeMainDom(t).id,r="switchNode"):(l=tools.getMDom(n,t,[{tagName:"a",attrName:"treeNode"+consts.id.A}]))&&(o=tools.getNodeMainDom(l).id,r="clickNode"):tools.eqs(e.type,"dblclick")&&(i="dblclick",(l=tools.getMDom(n,t,[{tagName:"a",attrName:"treeNode"+consts.id.A}]))&&(o=tools.getNodeMainDom(l).id,r="switchNode")),i.length>0&&0==o.length&&(l=tools.getMDom(n,t,[{tagName:"a",attrName:"treeNode"+consts.id.A}]))&&(o=tools.getNodeMainDom(l).id),o.length>0)switch(a=data.getNodeCache(n,o),r){case"switchNode":a.isParent&&(tools.eqs(e.type,"click")||tools.eqs(e.type,"dblclick")&&tools.apply(n.view.dblClickExpand,[n.treeId,a],n.view.dblClickExpand))?d=handler.onSwitchNode:r="";break;case"clickNode":d=handler.onClickNode}switch(i){case"mousedown":s=handler.onZTreeMousedown;break;case"mouseup":s=handler.onZTreeMouseup;break;case"dblclick":s=handler.onZTreeDblclick;break;case"contextmenu":s=handler.onZTreeContextmenu}return{stop:!1,node:a,nodeEventType:r,nodeEventCallback:d,treeEventType:i,treeEventCallback:s}},_initNode=function(e,t,n,o,a,r,i){if(n){var d=data.getRoot(e),s=e.data.key.children;n.level=t,n.tId=e.treeId+"_"+ ++d.zId,n.parentTId=o?o.tId:null,n.open="string"==typeof n.open?tools.eqs(n.open,"true"):!!n.open,n[s]&&n[s].length>0?(n.isParent=!0,n.zAsync=!0):(n.isParent="string"==typeof n.isParent?tools.eqs(n.isParent,"true"):!!n.isParent,n.open=!(!n.isParent||e.async.enable)&&n.open,n.zAsync=!n.isParent),n.isFirstNode=a,n.isLastNode=r,n.getParentNode=function(){return data.getNodeCache(e,n.parentTId)},n.getPreNode=function(){return data.getPreNode(e,n)},n.getNextNode=function(){return data.getNextNode(e,n)},n.getIndex=function(){return data.getNodeIndex(e,n)},n.getPath=function(){return data.getNodePath(e,n)},n.isAjaxing=!1,data.fixPIdKeyValue(e,n)}},_init={bind:[_bindEvent],unbind:[_unbindEvent],caches:[_initCache],nodes:[_initNode],proxys:[_eventProxy],roots:[_initRoot],beforeA:[],afterA:[],innerBeforeA:[],innerAfterA:[],zTreeTools:[]},data={addNodeCache:function(e,t){data.getCache(e).nodes[data.getNodeCacheId(t.tId)]=t},getNodeCacheId:function(e){return e.substring(e.lastIndexOf("_")+1)},addAfterA:function(e){_init.afterA.push(e)},addBeforeA:function(e){_init.beforeA.push(e)},addInnerAfterA:function(e){_init.innerAfterA.push(e)},addInnerBeforeA:function(e){_init.innerBeforeA.push(e)},addInitBind:function(e){_init.bind.push(e)},addInitUnBind:function(e){_init.unbind.push(e)},addInitCache:function(e){_init.caches.push(e)},addInitNode:function(e){_init.nodes.push(e)},addInitProxy:function(e,t){t?_init.proxys.splice(0,0,e):_init.proxys.push(e)},addInitRoot:function(e){_init.roots.push(e)},addNodesData:function(e,t,n,o){var a,r=e.data.key.children;t[r]?n>=t[r].length&&(n=-1):(t[r]=[],n=-1),t[r].length>0&&0===n?(t[r][0].isFirstNode=!1,view.setNodeLineIcos(e,t[r][0])):t[r].length>0&&n<0&&(t[r][t[r].length-1].isLastNode=!1,view.setNodeLineIcos(e,t[r][t[r].length-1])),t.isParent=!0,n<0?t[r]=t[r].concat(o):(a=[n,0].concat(o),t[r].splice.apply(t[r],a))},addSelectedNode:function(e,t){var n=data.getRoot(e);data.isSelectedNode(e,t)||n.curSelectedList.push(t)},addCreatedNode:function(e,t){(e.callback.onNodeCreated||e.view.addDiyDom)&&data.getRoot(e).createdNodes.push(t)},addZTreeTools:function(e){_init.zTreeTools.push(e)},exSetting:function(e){$.extend(!0,_setting,e)},fixPIdKeyValue:function(e,t){e.data.simpleData.enable&&(t[e.data.simpleData.pIdKey]=t.parentTId?t.getParentNode()[e.data.simpleData.idKey]:e.data.simpleData.rootPId)},getAfterA:function(e,t,n){for(var o=0,a=_init.afterA.length;o<a;o++)_init.afterA[o].apply(this,arguments)},getBeforeA:function(e,t,n){for(var o=0,a=_init.beforeA.length;o<a;o++)_init.beforeA[o].apply(this,arguments)},getInnerAfterA:function(e,t,n){for(var o=0,a=_init.innerAfterA.length;o<a;o++)_init.innerAfterA[o].apply(this,arguments)},getInnerBeforeA:function(e,t,n){for(var o=0,a=_init.innerBeforeA.length;o<a;o++)_init.innerBeforeA[o].apply(this,arguments)},getCache:function(e){return caches[e.treeId]},getNodeIndex:function(e,t){if(!t)return null;for(var n=e.data.key.children,o=t.parentTId?t.getParentNode():data.getRoot(e),a=0,r=o[n].length-1;a<=r;a++)if(o[n][a]===t)return a;return-1},getNextNode:function(e,t){if(!t)return null;for(var n=e.data.key.children,o=t.parentTId?t.getParentNode():data.getRoot(e),a=0,r=o[n].length-1;a<=r;a++)if(o[n][a]===t)return a==r?null:o[n][a+1];return null},getNodeByParam:function(e,t,n,o){if(!t||!n)return null;for(var a=e.data.key.children,r=0,i=t.length;r<i;r++){if(t[r][n]==o)return t[r];var d=data.getNodeByParam(e,t[r][a],n,o);if(d)return d}return null},getNodeCache:function(e,t){if(!t)return null;var n=caches[e.treeId].nodes[data.getNodeCacheId(t)];return n||null},getNodeName:function(e,t){return""+t[e.data.key.name]},getNodePath:function(e,t){return t?((n=t.parentTId?t.getParentNode().getPath():[])&&n.push(t),n):null;var n},getNodeTitle:function(e,t){return""+t[""===e.data.key.title?e.data.key.name:e.data.key.title]},getNodes:function(e){return data.getRoot(e)[e.data.key.children]},getNodesByParam:function(e,t,n,o){if(!t||!n)return[];for(var a=e.data.key.children,r=[],i=0,d=t.length;i<d;i++)t[i][n]==o&&r.push(t[i]),r=r.concat(data.getNodesByParam(e,t[i][a],n,o));return r},getNodesByParamFuzzy:function(e,t,n,o){if(!t||!n)return[];var a=e.data.key.children,r=[];o=o.toLowerCase();for(var i=0,d=t.length;i<d;i++)"string"==typeof t[i][n]&&t[i][n].toLowerCase().indexOf(o)>-1&&r.push(t[i]),r=r.concat(data.getNodesByParamFuzzy(e,t[i][a],n,o));return r},getNodesByFilter:function(e,t,n,o,a){if(!t)return o?null:[];for(var r=e.data.key.children,i=o?null:[],d=0,s=t.length;d<s;d++){if(tools.apply(n,[t[d],a],!1)){if(o)return t[d];i.push(t[d])}var l=data.getNodesByFilter(e,t[d][r],n,o,a);if(o&&l)return l;i=o?l:i.concat(l)}return i},getPreNode:function(e,t){if(!t)return null;for(var n=e.data.key.children,o=t.parentTId?t.getParentNode():data.getRoot(e),a=0,r=o[n].length;a<r;a++)if(o[n][a]===t)return 0==a?null:o[n][a-1];return null},getRoot:function(e){return e?roots[e.treeId]:null},getRoots:function(){return roots},getSetting:function(e){return settings[e]},getSettings:function(){return settings},getZTreeTools:function(e){var t=this.getRoot(this.getSetting(e));return t?t.treeTools:null},initCache:function(e){for(var t=0,n=_init.caches.length;t<n;t++)_init.caches[t].apply(this,arguments)},initNode:function(e,t,n,o,a,r){for(var i=0,d=_init.nodes.length;i<d;i++)_init.nodes[i].apply(this,arguments)},initRoot:function(e){for(var t=0,n=_init.roots.length;t<n;t++)_init.roots[t].apply(this,arguments)},isSelectedNode:function(e,t){for(var n=data.getRoot(e),o=0,a=n.curSelectedList.length;o<a;o++)if(t===n.curSelectedList[o])return!0;return!1},removeNodeCache:function(e,t){var n=e.data.key.children;if(t[n])for(var o=0,a=t[n].length;o<a;o++)data.removeNodeCache(e,t[n][o]);data.getCache(e).nodes[data.getNodeCacheId(t.tId)]=null},removeSelectedNode:function(e,t){for(var n=data.getRoot(e),o=0,a=n.curSelectedList.length;o<a;o++)t!==n.curSelectedList[o]&&data.getNodeCache(e,n.curSelectedList[o].tId)||(n.curSelectedList.splice(o,1),e.treeObj.trigger(consts.event.UNSELECTED,[e.treeId,t]),o--,a--)},setCache:function(e,t){caches[e.treeId]=t},setRoot:function(e,t){roots[e.treeId]=t},setZTreeTools:function(e,t){for(var n=0,o=_init.zTreeTools.length;n<o;n++)_init.zTreeTools[n].apply(this,arguments)},transformToArrayFormat:function(e,t){if(!t)return[];var n=e.data.key.children,o=[];if(tools.isArray(t))for(var a=0,r=t.length;a<r;a++)o.push(t[a]),t[a][n]&&(o=o.concat(data.transformToArrayFormat(e,t[a][n])));else o.push(t),t[n]&&(o=o.concat(data.transformToArrayFormat(e,t[n])));return o},transformTozTreeFormat:function(e,t){var n,o,a=e.data.simpleData.idKey,r=e.data.simpleData.pIdKey,i=e.data.key.children;if(!a||""==a||!t)return[];if(tools.isArray(t)){var d=[],s={};for(n=0,o=t.length;n<o;n++)s[t[n][a]]=t[n];for(n=0,o=t.length;n<o;n++)s[t[n][r]]&&t[n][a]!=t[n][r]?(s[t[n][r]][i]||(s[t[n][r]][i]=[]),s[t[n][r]][i].push(t[n])):d.push(t[n]);return d}return[t]}},event={bindEvent:function(e){for(var t=0,n=_init.bind.length;t<n;t++)_init.bind[t].apply(this,arguments)},unbindEvent:function(e){for(var t=0,n=_init.unbind.length;t<n;t++)_init.unbind[t].apply(this,arguments)},bindTree:function(e){var t={treeId:e.treeId},n=e.treeObj;e.view.txtSelectedEnable||n.bind("selectstart",handler.onSelectStart).css({"-moz-user-select":"-moz-none"}),n.bind("click",t,event.proxy),n.bind("dblclick",t,event.proxy),n.bind("mouseover",t,event.proxy),n.bind("mouseout",t,event.proxy),n.bind("mousedown",t,event.proxy),n.bind("mouseup",t,event.proxy),n.bind("contextmenu",t,event.proxy)},unbindTree:function(e){e.treeObj.unbind("selectstart",handler.onSelectStart).unbind("click",event.proxy).unbind("dblclick",event.proxy).unbind("mouseover",event.proxy).unbind("mouseout",event.proxy).unbind("mousedown",event.proxy).unbind("mouseup",event.proxy).unbind("contextmenu",event.proxy)},doProxy:function(e){for(var t=[],n=0,o=_init.proxys.length;n<o;n++){var a=_init.proxys[n].apply(this,arguments);if(t.push(a),a.stop)break}return t},proxy:function(e){var t=data.getSetting(e.data.treeId);if(!tools.uCanDo(t,e))return!0;for(var n=event.doProxy(e),o=!0,a=0,r=n.length;a<r;a++){var i=n[a];i.nodeEventCallback&&(!0,o=i.nodeEventCallback.apply(i,[e,i.node])&&o),i.treeEventCallback&&(!0,o=i.treeEventCallback.apply(i,[e,i.node])&&o)}return o}},handler={onSwitchNode:function(e,t){var n=data.getSetting(e.data.treeId);if(t.open){if(0==tools.apply(n.callback.beforeCollapse,[n.treeId,t],!0))return!0;data.getRoot(n).expandTriggerFlag=!0,view.switchNode(n,t)}else{if(0==tools.apply(n.callback.beforeExpand,[n.treeId,t],!0))return!0;data.getRoot(n).expandTriggerFlag=!0,view.switchNode(n,t)}return!0},onClickNode:function(e,t){var n=data.getSetting(e.data.treeId),o=n.view.autoCancelSelected&&(e.ctrlKey||e.metaKey)&&data.isSelectedNode(n,t)?0:n.view.autoCancelSelected&&(e.ctrlKey||e.metaKey)&&n.view.selectedMulti?2:1;return 0==tools.apply(n.callback.beforeClick,[n.treeId,t,o],!0)||(0===o?view.cancelPreSelectedNode(n,t):view.selectNode(n,t,2===o),n.treeObj.trigger(consts.event.CLICK,[e,n.treeId,t,o]),!0)},onZTreeMousedown:function(e,t){var n=data.getSetting(e.data.treeId);return tools.apply(n.callback.beforeMouseDown,[n.treeId,t],!0)&&tools.apply(n.callback.onMouseDown,[e,n.treeId,t]),!0},onZTreeMouseup:function(e,t){var n=data.getSetting(e.data.treeId);return tools.apply(n.callback.beforeMouseUp,[n.treeId,t],!0)&&tools.apply(n.callback.onMouseUp,[e,n.treeId,t]),!0},onZTreeDblclick:function(e,t){var n=data.getSetting(e.data.treeId);return tools.apply(n.callback.beforeDblClick,[n.treeId,t],!0)&&tools.apply(n.callback.onDblClick,[e,n.treeId,t]),!0},onZTreeContextmenu:function(e,t){var n=data.getSetting(e.data.treeId);return tools.apply(n.callback.beforeRightClick,[n.treeId,t],!0)&&tools.apply(n.callback.onRightClick,[e,n.treeId,t]),"function"!=typeof n.callback.onRightClick},onSelectStart:function(e){var t=e.originalEvent.srcElement.nodeName.toLowerCase();return"input"===t||"textarea"===t}},tools={apply:function(e,t,n){return"function"==typeof e?e.apply(zt,t||[]):n},canAsync:function(e,t){var n=e.data.key.children;return e.async.enable&&t&&t.isParent&&!(t.zAsync||t[n]&&t[n].length>0)},clone:function(e){if(null===e)return null;var t=tools.isArray(e)?[]:{};for(var n in e)t[n]=e[n]instanceof Date?new Date(e[n].getTime()):"object"==typeof e[n]?tools.clone(e[n]):e[n];return t},eqs:function(e,t){return e.toLowerCase()===t.toLowerCase()},isArray:function(e){return"[object Array]"===Object.prototype.toString.apply(e)},isElement:function(e){return"object"==typeof HTMLElement?e instanceof HTMLElement:e&&"object"==typeof e&&null!==e&&1===e.nodeType&&"string"==typeof e.nodeName},$:function(e,t,n){return t&&"string"!=typeof t&&(n=t,t=""),"string"==typeof e?$(e,n?n.treeObj.get(0).ownerDocument:null):$("#"+e.tId+t,n?n.treeObj:null)},getMDom:function(e,t,n){if(!t)return null;for(;t&&t.id!==e.treeId;){for(var o=0,a=n.length;t.tagName&&o<a;o++)if(tools.eqs(t.tagName,n[o].tagName)&&null!==t.getAttribute(n[o].attrName))return t;t=t.parentNode}return null},getNodeMainDom:function(e){return $(e).parent("li").get(0)||$(e).parentsUntil("li").parent().get(0)},isChildOrSelf:function(e,t){return $(e).closest("#"+t).length>0},uCanDo:function(e,t){return!0}},view={addNodes:function(e,t,n,o,a){if(!e.data.keep.leaf||!t||t.isParent)if(tools.isArray(o)||(o=[o]),e.data.simpleData.enable&&(o=data.transformTozTreeFormat(e,o)),t){var r=$$(t,consts.id.SWITCH,e),i=$$(t,consts.id.ICON,e),d=$$(t,consts.id.UL,e);t.open||(view.replaceSwitchClass(t,r,consts.folder.CLOSE),view.replaceIcoClass(t,i,consts.folder.CLOSE),t.open=!1,d.css({display:"none"})),data.addNodesData(e,t,n,o),view.createNodes(e,t.level+1,o,t,n),a||view.expandCollapseParentNode(e,t,!0)}else data.addNodesData(e,data.getRoot(e),n,o),view.createNodes(e,0,o,null,n)},appendNodes:function(e,t,n,o,a,r,i){if(!n)return[];var d,s,l=[],c=e.data.key.children,u=(o||data.getRoot(e))[c];(!u||a>=u.length-n.length)&&(a=-1);for(var h=0,f=n.length;h<f;h++){var p=n[h];r&&(d=(0===a||u.length==n.length)&&0==h,s=a<0&&h==n.length-1,data.initNode(e,t,p,o,d,s,i),data.addNodeCache(e,p));var g=[];p[c]&&p[c].length>0&&(g=view.appendNodes(e,t+1,p[c],p,-1,r,i&&p.open)),i&&(view.makeDOMNodeMainBefore(l,e,p),view.makeDOMNodeLine(l,e,p),data.getBeforeA(e,p,l),view.makeDOMNodeNameBefore(l,e,p),data.getInnerBeforeA(e,p,l),view.makeDOMNodeIcon(l,e,p),data.getInnerAfterA(e,p,l),view.makeDOMNodeNameAfter(l,e,p),data.getAfterA(e,p,l),p.isParent&&p.open&&view.makeUlHtml(e,p,l,g.join("")),view.makeDOMNodeMainAfter(l,e,p),data.addCreatedNode(e,p))}return l},appendParentULDom:function(e,t){var n=[],o=$$(t,e);!o.get(0)&&t.parentTId&&(view.appendParentULDom(e,t.getParentNode()),o=$$(t,e));var a=$$(t,consts.id.UL,e);a.get(0)&&a.remove();var r=e.data.key.children,i=view.appendNodes(e,t.level+1,t[r],t,-1,!1,!0);view.makeUlHtml(e,t,n,i.join("")),o.append(n.join(""))},asyncNode:function(setting,node,isSilent,callback){var i,l;if(node&&!node.isParent)return tools.apply(callback),!1;if(node&&node.isAjaxing)return!1;if(0==tools.apply(setting.callback.beforeAsync,[setting.treeId,node],!0))return tools.apply(callback),!1;if(node){node.isAjaxing=!0;var icoObj=$$(node,consts.id.ICON,setting);icoObj.attr({style:"",class:consts.className.BUTTON+" "+consts.className.ICO_LOADING})}var tmpParam={};for(i=0,l=setting.async.autoParam.length;node&&i<l;i++){var pKey=setting.async.autoParam[i].split("="),spKey=pKey;pKey.length>1&&(spKey=pKey[1],pKey=pKey[0]),tmpParam[spKey]=node[pKey]}if(tools.isArray(setting.async.otherParam))for(i=0,l=setting.async.otherParam.length;i<l;i+=2)tmpParam[setting.async.otherParam[i]]=setting.async.otherParam[i+1];else for(var p in setting.async.otherParam)tmpParam[p]=setting.async.otherParam[p];var _tmpV=data.getRoot(setting)._ver;return $.ajax({contentType:setting.async.contentType,cache:!1,type:setting.async.type,url:tools.apply(setting.async.url,[setting.treeId,node],setting.async.url),data:setting.async.contentType.indexOf("application/json")>-1?JSON.stringify(tmpParam):tmpParam,dataType:setting.async.dataType,success:function(msg){if(_tmpV==data.getRoot(setting)._ver){var newNodes=[];try{newNodes=msg&&0!=msg.length?"string"==typeof msg?eval("("+msg+")"):msg:[]}catch(e){newNodes=msg}node&&(node.isAjaxing=null,node.zAsync=!0),view.setNodeLineIcos(setting,node),newNodes&&""!==newNodes?(newNodes=tools.apply(setting.async.dataFilter,[setting.treeId,node,newNodes],newNodes),view.addNodes(setting,node,-1,newNodes?tools.clone(newNodes):[],!!isSilent)):view.addNodes(setting,node,-1,[],!!isSilent),setting.treeObj.trigger(consts.event.ASYNC_SUCCESS,[setting.treeId,node,msg]),tools.apply(callback)}},error:function(e,t,n){_tmpV==data.getRoot(setting)._ver&&(node&&(node.isAjaxing=null),view.setNodeLineIcos(setting,node),setting.treeObj.trigger(consts.event.ASYNC_ERROR,[setting.treeId,node,e,t,n]))}}),!0},cancelPreSelectedNode:function(e,t,n){var o,a,r=data.getRoot(e).curSelectedList;for(o=r.length-1;o>=0;o--)if(t===(a=r[o])||!t&&(!n||n!==a)){if($$(a,consts.id.A,e).removeClass(consts.node.CURSELECTED),t){data.removeSelectedNode(e,t);break}r.splice(o,1),e.treeObj.trigger(consts.event.UNSELECTED,[e.treeId,a])}},createNodeCallback:function(e){if(e.callback.onNodeCreated||e.view.addDiyDom)for(var t=data.getRoot(e);t.createdNodes.length>0;){var n=t.createdNodes.shift();tools.apply(e.view.addDiyDom,[e.treeId,n]),e.callback.onNodeCreated&&e.treeObj.trigger(consts.event.NODECREATED,[e.treeId,n])}},createNodes:function(e,t,n,o,a){if(n&&0!=n.length){var r=data.getRoot(e),i=e.data.key.children,d=!o||o.open||!!$$(o[i][0],e).get(0);r.createdNodes=[];var s,l,c=view.appendNodes(e,t,n,o,a,!0,d);if(o){var u=$$(o,consts.id.UL,e);u.get(0)&&(s=u)}else s=e.treeObj;s&&(a>=0&&(l=s.children()[a]),a>=0&&l?$(l).before(c.join("")):s.append(c.join(""))),view.createNodeCallback(e)}},destroy:function(e){e&&(data.initCache(e),data.initRoot(e),event.unbindTree(e),event.unbindEvent(e),e.treeObj.empty(),delete settings[e.treeId])},expandCollapseNode:function(e,t,n,o,a){var r,i=data.getRoot(e),d=e.data.key.children;if(t)if(i.expandTriggerFlag&&(r=a,a=function(){r&&r(),t.open?e.treeObj.trigger(consts.event.EXPAND,[e.treeId,t]):e.treeObj.trigger(consts.event.COLLAPSE,[e.treeId,t])},i.expandTriggerFlag=!1),!t.open&&t.isParent&&(!$$(t,consts.id.UL,e).get(0)||t[d]&&t[d].length>0&&!$$(t[d][0],e).get(0))&&(view.appendParentULDom(e,t),view.createNodeCallback(e)),t.open!=n){var s=$$(t,consts.id.UL,e),l=$$(t,consts.id.SWITCH,e),c=$$(t,consts.id.ICON,e);t.isParent?(t.open=!t.open,t.iconOpen&&t.iconClose&&c.attr("style",view.makeNodeIcoStyle(e,t)),t.open?(view.replaceSwitchClass(t,l,consts.folder.OPEN),view.replaceIcoClass(t,c,consts.folder.OPEN),0==o||""==e.view.expandSpeed?(s.show(),tools.apply(a,[])):t[d]&&t[d].length>0?s.slideDown(e.view.expandSpeed,a):(s.show(),tools.apply(a,[]))):(view.replaceSwitchClass(t,l,consts.folder.CLOSE),view.replaceIcoClass(t,c,consts.folder.CLOSE),0!=o&&""!=e.view.expandSpeed&&t[d]&&t[d].length>0?s.slideUp(e.view.expandSpeed,a):(s.hide(),tools.apply(a,[])))):tools.apply(a,[])}else tools.apply(a,[]);else tools.apply(a,[])},expandCollapseParentNode:function(e,t,n,o,a){t&&(t.parentTId?(view.expandCollapseNode(e,t,n,o),t.parentTId&&view.expandCollapseParentNode(e,t.getParentNode(),n,o,a)):view.expandCollapseNode(e,t,n,o,a))},expandCollapseSonNode:function(e,t,n,o,a){var r=data.getRoot(e),i=e.data.key.children,d=t?t[i]:r[i],s=!t&&o,l=data.getRoot(e).expandTriggerFlag;if(data.getRoot(e).expandTriggerFlag=!1,d)for(var c=0,u=d.length;c<u;c++)d[c]&&view.expandCollapseSonNode(e,d[c],n,s);data.getRoot(e).expandTriggerFlag=l,view.expandCollapseNode(e,t,n,o,a)},isSelectedNode:function(e,t){if(!t)return!1;var n,o=data.getRoot(e).curSelectedList;for(n=o.length-1;n>=0;n--)if(t===o[n])return!0;return!1},makeDOMNodeIcon:function(e,t,n){var o=data.getNodeName(t,n),a=t.view.nameIsHTML?o:o.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;");e.push("<span id='",n.tId,consts.id.ICON,"' title='' treeNode",consts.id.ICON," class='",view.makeNodeIcoClass(t,n),"' style='",view.makeNodeIcoStyle(t,n),"'></span><span id='",n.tId,consts.id.SPAN,"' class='",consts.className.NAME,"'>",a,"</span>")},makeDOMNodeLine:function(e,t,n){e.push("<span id='",n.tId,consts.id.SWITCH,"' title='' class='",view.makeNodeLineClass(t,n),"' treeNode",consts.id.SWITCH,"></span>")},makeDOMNodeMainAfter:function(e,t,n){e.push("</li>")},makeDOMNodeMainBefore:function(e,t,n){e.push("<li id='",n.tId,"' class='",consts.className.LEVEL,n.level,"' tabindex='0' hidefocus='true' treenode>")},makeDOMNodeNameAfter:function(e,t,n){e.push("</a>")},makeDOMNodeNameBefore:function(e,t,n){var o=data.getNodeTitle(t,n),a=view.makeNodeUrl(t,n),r=view.makeNodeFontCss(t,n),i=[];for(var d in r)i.push(d,":",r[d],";");e.push("<a id='",n.tId,consts.id.A,"' class='",consts.className.LEVEL,n.level,"' treeNode",consts.id.A,' onclick="',n.click||"",'" ',null!=a&&a.length>0?"href='"+a+"'":""," target='",view.makeNodeTarget(n),"' style='",i.join(""),"'"),tools.apply(t.view.showTitle,[t.treeId,n],t.view.showTitle)&&o&&e.push("title='",o.replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"'"),e.push(">")},makeNodeFontCss:function(e,t){var n=tools.apply(e.view.fontCss,[e.treeId,t],e.view.fontCss);return n&&"function"!=typeof n?n:{}},makeNodeIcoClass:function(e,t){var n=["ico"];return t.isAjaxing||(n[0]=(t.iconSkin?t.iconSkin+"_":"")+n[0],t.isParent?n.push(t.open?consts.folder.OPEN:consts.folder.CLOSE):n.push(consts.folder.DOCU)),consts.className.BUTTON+" "+n.join("_")},makeNodeIcoStyle:function(e,t){var n=[];if(!t.isAjaxing){var o=t.isParent&&t.iconOpen&&t.iconClose?t.open?t.iconOpen:t.iconClose:t[e.data.key.icon];o&&n.push("background:url(",o,") 0 0 no-repeat;"),0!=e.view.showIcon&&tools.apply(e.view.showIcon,[e.treeId,t],!0)||n.push("width:0px;height:0px;")}return n.join("")},makeNodeLineClass:function(e,t){var n=[];return e.view.showLine?0==t.level&&t.isFirstNode&&t.isLastNode?n.push(consts.line.ROOT):0==t.level&&t.isFirstNode?n.push(consts.line.ROOTS):t.isLastNode?n.push(consts.line.BOTTOM):n.push(consts.line.CENTER):n.push(consts.line.NOLINE),t.isParent?n.push(t.open?consts.folder.OPEN:consts.folder.CLOSE):n.push(consts.folder.DOCU),view.makeNodeLineClassEx(t)+n.join("_")},makeNodeLineClassEx:function(e){return consts.className.BUTTON+" "+consts.className.LEVEL+e.level+" "+consts.className.SWITCH+" "},makeNodeTarget:function(e){return e.target||"_blank"},makeNodeUrl:function(e,t){var n=e.data.key.url;return t[n]?t[n]:null},makeUlHtml:function(e,t,n,o){n.push("<ul id='",t.tId,consts.id.UL,"' class='",consts.className.LEVEL,t.level," ",view.makeUlLineClass(e,t),"' style='display:",t.open?"block":"none","'>"),n.push(o),n.push("</ul>")},makeUlLineClass:function(e,t){return e.view.showLine&&!t.isLastNode?consts.line.LINE:""},removeChildNodes:function(e,t){if(t){var n=e.data.key.children,o=t[n];if(o){for(var a=0,r=o.length;a<r;a++)data.removeNodeCache(e,o[a]);if(data.removeSelectedNode(e),delete t[n],e.data.keep.parent)$$(t,consts.id.UL,e).empty();else{t.isParent=!1,t.open=!1;var i=$$(t,consts.id.SWITCH,e),d=$$(t,consts.id.ICON,e);view.replaceSwitchClass(t,i,consts.folder.DOCU),view.replaceIcoClass(t,d,consts.folder.DOCU),$$(t,consts.id.UL,e).remove()}}}},scrollIntoView:function(e,t){if(t)if("undefined"!=typeof Element)Element.prototype.scrollIntoViewIfNeeded||(Element.prototype.scrollIntoViewIfNeeded=function(e){function t(t,n,o,a){return!1===e||o<=t+a&&t<=n+a?Math.min(o,Math.max(n,t)):(n+o)/2}for(var n,o=this,a=function e(t,n,o,r){return{left:t,top:n,width:o,height:r,right:t+o,bottom:n+r,translate:function(a,i){return e(a+t,i+n,o,r)},relativeFromTo:function(i,d){var s=t,l=n;if((i=i.offsetParent)===(d=d.offsetParent))return a;for(;i;i=i.offsetParent)s+=i.offsetLeft+i.clientLeft,l+=i.offsetTop+i.clientTop;for(;d;d=d.offsetParent)s-=d.offsetLeft+d.clientLeft,l-=d.offsetTop+d.clientTop;return e(s,l,o,r)}}}(this.offsetLeft,this.offsetTop,this.offsetWidth,this.offsetHeight);tools.isElement(n=o.parentNode);){var r=n.offsetLeft+n.clientLeft,i=n.offsetTop+n.clientTop;a=a.relativeFromTo(o,n).translate(-r,-i),n.scrollLeft=t(n.scrollLeft,a.right-n.clientWidth,a.left,n.clientWidth),n.scrollTop=t(n.scrollTop,a.bottom-n.clientHeight,a.top,n.clientHeight),a=a.translate(r-n.scrollLeft,i-n.scrollTop),o=n}}),t.scrollIntoViewIfNeeded();else{var n=e.treeObj.get(0).getBoundingClientRect(),o=t.getBoundingClientRect();(o.top<n.top||o.bottom>n.bottom||o.right>n.right||o.left<n.left)&&t.scrollIntoView()}},setFirstNode:function(e,t){var n=e.data.key.children;t[n].length>0&&(t[n][0].isFirstNode=!0)},setLastNode:function(e,t){var n=e.data.key.children,o=t[n].length;o>0&&(t[n][o-1].isLastNode=!0)},removeNode:function(e,t){var n=data.getRoot(e),o=e.data.key.children,a=t.parentTId?t.getParentNode():n;if(t.isFirstNode=!1,t.isLastNode=!1,t.getPreNode=function(){return null},t.getNextNode=function(){return null},data.getNodeCache(e,t.tId)){$$(t,e).remove(),data.removeNodeCache(e,t),data.removeSelectedNode(e,t);for(var r=0,i=a[o].length;r<i;r++)if(a[o][r].tId==t.tId){a[o].splice(r,1);break}view.setFirstNode(e,a),view.setLastNode(e,a);var d,s,l,c=a[o].length;if(e.data.keep.parent||0!=c){if(e.view.showLine&&c>0){var u=a[o][c-1];if(d=$$(u,consts.id.UL,e),s=$$(u,consts.id.SWITCH,e),l=$$(u,consts.id.ICON,e),a==n)if(1==a[o].length)view.replaceSwitchClass(u,s,consts.line.ROOT);else{var h=$$(a[o][0],consts.id.SWITCH,e);view.replaceSwitchClass(a[o][0],h,consts.line.ROOTS),view.replaceSwitchClass(u,s,consts.line.BOTTOM)}else view.replaceSwitchClass(u,s,consts.line.BOTTOM);d.removeClass(consts.line.LINE)}}else a.isParent=!1,a.open=!1,d=$$(a,consts.id.UL,e),s=$$(a,consts.id.SWITCH,e),l=$$(a,consts.id.ICON,e),view.replaceSwitchClass(a,s,consts.folder.DOCU),view.replaceIcoClass(a,l,consts.folder.DOCU),d.css("display","none")}},replaceIcoClass:function(e,t,n){if(t&&!e.isAjaxing){var o=t.attr("class");if(void 0!=o){var a=o.split("_");switch(n){case consts.folder.OPEN:case consts.folder.CLOSE:case consts.folder.DOCU:a[a.length-1]=n}t.attr("class",a.join("_"))}}},replaceSwitchClass:function(e,t,n){if(t){var o=t.attr("class");if(void 0!=o){var a=o.split("_");switch(n){case consts.line.ROOT:case consts.line.ROOTS:case consts.line.CENTER:case consts.line.BOTTOM:case consts.line.NOLINE:a[0]=view.makeNodeLineClassEx(e)+n;break;case consts.folder.OPEN:case consts.folder.CLOSE:case consts.folder.DOCU:a[1]=n}t.attr("class",a.join("_")),n!==consts.folder.DOCU?t.removeAttr("disabled"):t.attr("disabled","disabled")}}},selectNode:function(e,t,n){n||view.cancelPreSelectedNode(e,null,t),$$(t,consts.id.A,e).addClass(consts.node.CURSELECTED),data.addSelectedNode(e,t),e.treeObj.trigger(consts.event.SELECTED,[e.treeId,t])},setNodeFontCss:function(e,t){var n=$$(t,consts.id.A,e),o=view.makeNodeFontCss(e,t);o&&n.css(o)},setNodeLineIcos:function(e,t){if(t){var n=$$(t,consts.id.SWITCH,e),o=$$(t,consts.id.UL,e),a=$$(t,consts.id.ICON,e),r=view.makeUlLineClass(e,t);0==r.length?o.removeClass(consts.line.LINE):o.addClass(r),n.attr("class",view.makeNodeLineClass(e,t)),t.isParent?n.removeAttr("disabled"):n.attr("disabled","disabled"),a.removeAttr("style"),a.attr("style",view.makeNodeIcoStyle(e,t)),a.attr("class",view.makeNodeIcoClass(e,t))}},setNodeName:function(e,t){var n=data.getNodeTitle(e,t),o=$$(t,consts.id.SPAN,e);(o.empty(),e.view.nameIsHTML?o.html(data.getNodeName(e,t)):o.text(data.getNodeName(e,t)),tools.apply(e.view.showTitle,[e.treeId,t],e.view.showTitle))&&$$(t,consts.id.A,e).attr("title",n||"")},setNodeTarget:function(e,t){$$(t,consts.id.A,e).attr("target",view.makeNodeTarget(t))},setNodeUrl:function(e,t){var n=$$(t,consts.id.A,e),o=view.makeNodeUrl(e,t);null==o||0==o.length?n.removeAttr("href"):n.attr("href",o)},switchNode:function(e,t){if(t.open||!tools.canAsync(e,t))view.expandCollapseNode(e,t,!t.open);else if(e.async.enable){if(!view.asyncNode(e,t))return void view.expandCollapseNode(e,t,!t.open)}else t&&view.expandCollapseNode(e,t,!t.open)}};$.fn.zTree={consts:_consts,_z:{tools:tools,view:view,event:event,data:data},getZTreeObj:function(e){var t=data.getZTreeTools(e);return t||null},destroy:function(e){if(e&&e.length>0)view.destroy(data.getSetting(e));else for(var t in settings)view.destroy(settings[t])},init:function(e,t,n){var o=tools.clone(_setting);$.extend(!0,o,t),o.treeId=e.attr("id"),o.treeObj=e,o.treeObj.empty(),settings[o.treeId]=o,void 0===document.body.style.maxHeight&&(o.view.expandSpeed=""),data.initRoot(o);var a=data.getRoot(o),r=o.data.key.children;n=n?tools.clone(tools.isArray(n)?n:[n]):[],o.data.simpleData.enable?a[r]=data.transformTozTreeFormat(o,n):a[r]=n,data.initCache(o),event.unbindTree(o),event.bindTree(o),event.unbindEvent(o),event.bindEvent(o);var i={setting:o,addNodes:function(e,t,n,a){if(e||(e=null),e&&!e.isParent&&o.data.keep.leaf)return null;var r=parseInt(t,10);if(isNaN(r)?(a=!!n,n=t,t=-1):t=r,!n)return null;var i=tools.clone(tools.isArray(n)?n:[n]);function d(){view.addNodes(o,e,t,i,1==a)}return tools.canAsync(o,e)?view.asyncNode(o,e,a,d):d(),i},cancelSelectedNode:function(e){view.cancelPreSelectedNode(o,e)},destroy:function(){view.destroy(o)},expandAll:function(e){return e=!!e,view.expandCollapseSonNode(o,null,e,!0),e},expandNode:function(e,t,n,a,r){return e&&e.isParent?(!0!==t&&!1!==t&&(t=!e.open),(r=!!r)&&t&&0==tools.apply(o.callback.beforeExpand,[o.treeId,e],!0)?null:r&&!t&&0==tools.apply(o.callback.beforeCollapse,[o.treeId,e],!0)?null:(t&&e.parentTId&&view.expandCollapseParentNode(o,e.getParentNode(),t,!1),t!==e.open||n?(data.getRoot(o).expandTriggerFlag=r,!tools.canAsync(o,e)&&n?view.expandCollapseSonNode(o,e,t,!0,i):(e.open=!t,view.switchNode(this.setting,e),i()),t):null)):null;function i(){var t=$$(e,o).get(0);t&&!1!==a&&view.scrollIntoView(o,t)}},getNodes:function(){return data.getNodes(o)},getNodeByParam:function(e,t,n){return e?data.getNodeByParam(o,n?n[o.data.key.children]:data.getNodes(o),e,t):null},getNodeByTId:function(e){return data.getNodeCache(o,e)},getNodesByParam:function(e,t,n){return e?data.getNodesByParam(o,n?n[o.data.key.children]:data.getNodes(o),e,t):null},getNodesByParamFuzzy:function(e,t,n){return e?data.getNodesByParamFuzzy(o,n?n[o.data.key.children]:data.getNodes(o),e,t):null},getNodesByFilter:function(e,t,n,a){return t=!!t,e&&"function"==typeof e?data.getNodesByFilter(o,n?n[o.data.key.children]:data.getNodes(o),e,t,a):t?null:[]},getNodeIndex:function(e){if(!e)return null;for(var t=o.data.key.children,n=e.parentTId?e.getParentNode():data.getRoot(o),a=0,r=n[t].length;a<r;a++)if(n[t][a]==e)return a;return-1},getSelectedNodes:function(){for(var e=[],t=data.getRoot(o).curSelectedList,n=0,a=t.length;n<a;n++)e.push(t[n]);return e},isSelectedNode:function(e){return data.isSelectedNode(o,e)},reAsyncChildNodesPromise:function(e,t,n){return new Promise(function(o,a){try{i.reAsyncChildNodes(e,t,n,function(){o(e)})}catch(e){a(e)}})},reAsyncChildNodes:function(e,t,n,a){if(this.setting.async.enable){var r=!e;if(r&&(e=data.getRoot(o)),"refresh"==t){for(var i=this.setting.data.key.children,d=0,s=e[i]?e[i].length:0;d<s;d++)data.removeNodeCache(o,e[i][d]);if(data.removeSelectedNode(o),e[i]=[],r)this.setting.treeObj.empty();else $$(e,consts.id.UL,o).empty()}view.asyncNode(this.setting,r?null:e,!!n,a)}},refresh:function(){this.setting.treeObj.empty();var e=data.getRoot(o),t=e[o.data.key.children];data.initRoot(o),e[o.data.key.children]=t,data.initCache(o),view.createNodes(o,0,e[o.data.key.children],null,-1)},removeChildNodes:function(e){if(!e)return null;var t=e[o.data.key.children];return view.removeChildNodes(o,e),t||null},removeNode:function(e,t){e&&((t=!!t)&&0==tools.apply(o.callback.beforeRemove,[o.treeId,e],!0)||(view.removeNode(o,e),t&&this.setting.treeObj.trigger(consts.event.REMOVE,[o.treeId,e])))},selectNode:function(e,t,n){if(e&&tools.uCanDo(o)){if(t=o.view.selectedMulti&&t,e.parentTId)view.expandCollapseParentNode(o,e.getParentNode(),!0,!1,function(){if(n)return;var t=$$(e,o).get(0);view.scrollIntoView(o,t)});else if(!n)try{$$(e,o).focus().blur()}catch(e){}view.selectNode(o,e,t)}},transformTozTreeNodes:function(e){return data.transformTozTreeFormat(o,e)},transformToArray:function(e){return data.transformToArrayFormat(o,e)},updateNode:function(e,t){e&&($$(e,o).get(0)&&tools.uCanDo(o)&&(view.setNodeName(o,e),view.setNodeTarget(o,e),view.setNodeUrl(o,e),view.setNodeLineIcos(o,e),view.setNodeFontCss(o,e)))}};return a.treeTools=i,data.setZTreeTools(o,i),a[r]&&a[r].length>0?view.createNodes(o,0,a[r],null,-1):o.async.enable&&o.async.url&&""!==o.async.url&&view.asyncNode(o),i}};var zt=$.fn.zTree,$$=tools.$,consts=zt.consts}(jQuery),function(e){var t={event:{CHECK:"ztree_check"},id:{CHECK:"_check"},checkbox:{STYLE:"checkbox",DEFAULT:"chk",DISABLED:"disable",FALSE:"false",TRUE:"true",FULL:"full",PART:"part",FOCUS:"focus"},radio:{STYLE:"radio",TYPE_ALL:"all",TYPE_LEVEL:"level"}},n={check:{enable:!1,autoCheckTrigger:!1,chkStyle:t.checkbox.STYLE,nocheckInherit:!1,chkDisabledInherit:!1,radioType:t.radio.TYPE_LEVEL,chkboxType:{Y:"ps",N:"ps"}},data:{key:{checked:"checked"}},callback:{beforeCheck:null,onCheck:null}},o={onCheckNode:function(e,t){if(!0===t.chkDisabled)return!1;var n=l.getSetting(e.data.treeId),o=n.data.key.checked;if(0==i.apply(n.callback.beforeCheck,[n.treeId,t],!0))return!0;t[o]=!t[o],s.checkNodeRelation(n,t);var a=c(t,d.id.CHECK,n);return s.setChkClass(n,a,t),s.repairParentChkClassWithSelf(n,t),n.treeObj.trigger(d.event.CHECK,[e,n.treeId,t]),!0},onMouseoverCheck:function(e,t){if(!0===t.chkDisabled)return!1;var n=l.getSetting(e.data.treeId),o=c(t,d.id.CHECK,n);return t.check_Focus=!0,s.setChkClass(n,o,t),!0},onMouseoutCheck:function(e,t){if(!0===t.chkDisabled)return!1;var n=l.getSetting(e.data.treeId),o=c(t,d.id.CHECK,n);return t.check_Focus=!1,s.setChkClass(n,o,t),!0}},a={tools:{},view:{checkNodeRelation:function(e,t){var n,o,a,r=e.data.key.children,i=e.data.key.checked,u=d.radio;if(e.check.chkStyle==u.STYLE){var h=l.getRadioCheckedList(e);if(t[i])if(e.check.radioType==u.TYPE_ALL){for(o=h.length-1;o>=0;o--)(n=h[o])[i]&&n!=t&&(n[i]=!1,h.splice(o,1),s.setChkClass(e,c(n,d.id.CHECK,e),n),n.parentTId!=t.parentTId&&s.repairParentChkClassWithSelf(e,n));h.push(t)}else{var f=t.parentTId?t.getParentNode():l.getRoot(e);for(o=0,a=f[r].length;o<a;o++)(n=f[r][o])[i]&&n!=t&&(n[i]=!1,s.setChkClass(e,c(n,d.id.CHECK,e),n))}else if(e.check.radioType==u.TYPE_ALL)for(o=0,a=h.length;o<a;o++)if(t==h[o]){h.splice(o,1);break}}else t[i]&&(!t[r]||0==t[r].length||e.check.chkboxType.Y.indexOf("s")>-1)&&s.setSonNodeCheckBox(e,t,!0),t[i]||t[r]&&0!=t[r].length&&!(e.check.chkboxType.N.indexOf("s")>-1)||s.setSonNodeCheckBox(e,t,!1),t[i]&&e.check.chkboxType.Y.indexOf("p")>-1&&s.setParentNodeCheckBox(e,t,!0),!t[i]&&e.check.chkboxType.N.indexOf("p")>-1&&s.setParentNodeCheckBox(e,t,!1)},makeChkClass:function(e,t){var n=e.data.key.checked,o=d.checkbox,a=d.radio,r="";r=!0===t.chkDisabled?o.DISABLED:t.halfCheck?o.PART:e.check.chkStyle==a.STYLE?t.check_Child_State<1?o.FULL:o.PART:t[n]?2===t.check_Child_State||-1===t.check_Child_State?o.FULL:o.PART:t.check_Child_State<1?o.FULL:o.PART;var i=e.check.chkStyle+"_"+(t[n]?o.TRUE:o.FALSE)+"_"+r;return i=t.check_Focus&&!0!==t.chkDisabled?i+"_"+o.FOCUS:i,d.className.BUTTON+" "+o.DEFAULT+" "+i},repairAllChk:function(e,t){if(e.check.enable&&e.check.chkStyle===d.checkbox.STYLE)for(var n=e.data.key.checked,o=e.data.key.children,a=l.getRoot(e),r=0,i=a[o].length;r<i;r++){var c=a[o][r];!0!==c.nocheck&&!0!==c.chkDisabled&&(c[n]=t),s.setSonNodeCheckBox(e,c,t)}},repairChkClass:function(e,t){if(t&&(l.makeChkFlag(e,t),!0!==t.nocheck)){var n=c(t,d.id.CHECK,e);s.setChkClass(e,n,t)}},repairParentChkClass:function(e,t){if(t&&t.parentTId){var n=t.getParentNode();s.repairChkClass(e,n),s.repairParentChkClass(e,n)}},repairParentChkClassWithSelf:function(e,t){if(t){var n=e.data.key.children;t[n]&&t[n].length>0?s.repairParentChkClass(e,t[n][0]):s.repairParentChkClass(e,t)}},repairSonChkDisabled:function(e,t,n,o){if(t){var a=e.data.key.children;if(t.chkDisabled!=n&&(t.chkDisabled=n),s.repairChkClass(e,t),t[a]&&o)for(var r=0,i=t[a].length;r<i;r++){var d=t[a][r];s.repairSonChkDisabled(e,d,n,o)}}},repairParentChkDisabled:function(e,t,n,o){t&&(t.chkDisabled!=n&&o&&(t.chkDisabled=n),s.repairChkClass(e,t),s.repairParentChkDisabled(e,t.getParentNode(),n,o))},setChkClass:function(e,t,n){t&&(!0===n.nocheck?t.hide():t.show(),t.attr("class",s.makeChkClass(e,n)))},setParentNodeCheckBox:function(e,t,n,o){var a=e.data.key.children,r=e.data.key.checked,i=c(t,d.id.CHECK,e);if(o||(o=t),l.makeChkFlag(e,t),!0!==t.nocheck&&!0!==t.chkDisabled&&(t[r]=n,s.setChkClass(e,i,t),e.check.autoCheckTrigger&&t!=o&&e.treeObj.trigger(d.event.CHECK,[null,e.treeId,t])),t.parentTId){var u=!0;if(!n)for(var h=t.getParentNode()[a],f=0,p=h.length;f<p;f++)if(!0!==h[f].nocheck&&!0!==h[f].chkDisabled&&h[f][r]||(!0===h[f].nocheck||!0===h[f].chkDisabled)&&h[f].check_Child_State>0){u=!1;break}u&&s.setParentNodeCheckBox(e,t.getParentNode(),n,o)}},setSonNodeCheckBox:function(e,t,n,o){if(t){var a=e.data.key.children,r=e.data.key.checked,i=c(t,d.id.CHECK,e);o||(o=t);var u=!1;if(t[a])for(var h=0,f=t[a].length;h<f;h++){var p=t[a][h];s.setSonNodeCheckBox(e,p,n,o),!0===p.chkDisabled&&(u=!0)}t!=l.getRoot(e)&&!0!==t.chkDisabled&&(u&&!0!==t.nocheck&&l.makeChkFlag(e,t),!0!==t.nocheck&&!0!==t.chkDisabled?(t[r]=n,u||(t.check_Child_State=t[a]&&t[a].length>0?n?2:0:-1)):t.check_Child_State=-1,s.setChkClass(e,i,t),e.check.autoCheckTrigger&&t!=o&&!0!==t.nocheck&&!0!==t.chkDisabled&&e.treeObj.trigger(d.event.CHECK,[null,e.treeId,t]))}}},event:{},data:{getRadioCheckedList:function(e){for(var t=l.getRoot(e).radioCheckedList,n=0,o=t.length;n<o;n++)l.getNodeCache(e,t[n].tId)||(t.splice(n,1),n--,o--);return t},getCheckStatus:function(e,t){if(!e.check.enable||t.nocheck||t.chkDisabled)return null;var n=e.data.key.checked;return{checked:t[n],half:t.halfCheck?t.halfCheck:e.check.chkStyle==d.radio.STYLE?2===t.check_Child_State:t[n]?t.check_Child_State>-1&&t.check_Child_State<2:t.check_Child_State>0}},getTreeCheckedNodes:function(e,t,n,o){if(!t)return[];var a=e.data.key.children,r=e.data.key.checked,i=n&&e.check.chkStyle==d.radio.STYLE&&e.check.radioType==d.radio.TYPE_ALL;o=o||[];for(var s=0,c=t.length;s<c&&(!0===t[s].nocheck||!0===t[s].chkDisabled||t[s][r]!=n||(o.push(t[s]),!i))&&(l.getTreeCheckedNodes(e,t[s][a],n,o),!(i&&o.length>0));s++);return o},getTreeChangeCheckedNodes:function(e,t,n){if(!t)return[];var o=e.data.key.children,a=e.data.key.checked;n=n||[];for(var r=0,i=t.length;r<i;r++)!0!==t[r].nocheck&&!0!==t[r].chkDisabled&&t[r][a]!=t[r].checkedOld&&n.push(t[r]),l.getTreeChangeCheckedNodes(e,t[r][o],n);return n},makeChkFlag:function(e,t){if(t){var n=e.data.key.children,o=e.data.key.checked,a=-1;if(t[n])for(var r=0,i=t[n].length;r<i;r++){var s=t[n][r],l=-1;if(e.check.chkStyle==d.radio.STYLE){if(2==(l=!0===s.nocheck||!0===s.chkDisabled?s.check_Child_State:!0===s.halfCheck?2:s[o]?2:s.check_Child_State>0?2:0)){a=2;break}0==l&&(a=0)}else if(e.check.chkStyle==d.checkbox.STYLE){if(1===(l=!0===s.nocheck||!0===s.chkDisabled?s.check_Child_State:!0===s.halfCheck?1:s[o]?-1===s.check_Child_State||2===s.check_Child_State?2:1:s.check_Child_State>0?1:0)){a=1;break}if(2===l&&a>-1&&r>0&&l!==a){a=1;break}if(2===a&&l>-1&&l<2){a=1;break}l>-1&&(a=l)}}t.check_Child_State=a}}}};e.extend(!0,e.fn.zTree.consts,t),e.extend(!0,e.fn.zTree._z,a);var r=e.fn.zTree,i=r._z.tools,d=r.consts,s=r._z.view,l=r._z.data,c=(r._z.event,i.$);l.exSetting(n),l.addInitBind(function(e){var t=e.treeObj,n=d.event;t.bind(n.CHECK,function(t,n,o,a){t.srcEvent=n,i.apply(e.callback.onCheck,[t,o,a])})}),l.addInitUnBind(function(e){var t=e.treeObj,n=d.event;t.unbind(n.CHECK)}),l.addInitCache(function(e){}),l.addInitNode(function(e,t,n,o,a,r,s){if(n){var c=e.data.key.checked;"string"==typeof n[c]&&(n[c]=i.eqs(n[c],"true")),n[c]=!!n[c],n.checkedOld=n[c],"string"==typeof n.nocheck&&(n.nocheck=i.eqs(n.nocheck,"true")),n.nocheck=!!n.nocheck||e.check.nocheckInherit&&o&&!!o.nocheck,"string"==typeof n.chkDisabled&&(n.chkDisabled=i.eqs(n.chkDisabled,"true")),n.chkDisabled=!!n.chkDisabled||e.check.chkDisabledInherit&&o&&!!o.chkDisabled,"string"==typeof n.halfCheck&&(n.halfCheck=i.eqs(n.halfCheck,"true")),n.halfCheck=!!n.halfCheck,n.check_Child_State=-1,n.check_Focus=!1,n.getCheckStatus=function(){return l.getCheckStatus(e,n)},e.check.chkStyle==d.radio.STYLE&&e.check.radioType==d.radio.TYPE_ALL&&n[c]&&l.getRoot(e).radioCheckedList.push(n)}}),l.addInitProxy(function(e){var t=e.target,n=l.getSetting(e.data.treeId),a="",r=null,s="",c=null;if(i.eqs(e.type,"mouseover")?n.check.enable&&i.eqs(t.tagName,"span")&&null!==t.getAttribute("treeNode"+d.id.CHECK)&&(a=i.getNodeMainDom(t).id,s="mouseoverCheck"):i.eqs(e.type,"mouseout")?n.check.enable&&i.eqs(t.tagName,"span")&&null!==t.getAttribute("treeNode"+d.id.CHECK)&&(a=i.getNodeMainDom(t).id,s="mouseoutCheck"):i.eqs(e.type,"click")&&n.check.enable&&i.eqs(t.tagName,"span")&&null!==t.getAttribute("treeNode"+d.id.CHECK)&&(a=i.getNodeMainDom(t).id,s="checkNode"),a.length>0)switch(r=l.getNodeCache(n,a),s){case"checkNode":c=o.onCheckNode;break;case"mouseoverCheck":c=o.onMouseoverCheck;break;case"mouseoutCheck":c=o.onMouseoutCheck}return{stop:"checkNode"===s,node:r,nodeEventType:s,nodeEventCallback:c,treeEventType:"",treeEventCallback:null}},!0),l.addInitRoot(function(e){l.getRoot(e).radioCheckedList=[]}),l.addBeforeA(function(e,t,n){e.data.key.checked;e.check.enable&&(l.makeChkFlag(e,t),n.push("<span ID='",t.tId,d.id.CHECK,"' class='",s.makeChkClass(e,t),"' treeNode",d.id.CHECK,!0===t.nocheck?" style='display:none;'":"","></span>"))}),l.addZTreeTools(function(e,t){t.checkNode=function(e,t,n,o){var a=this.setting.data.key.checked;if(!0!==e.chkDisabled&&(!0!==t&&!1!==t&&(t=!e[a]),o=!!o,(e[a]!==t||n)&&(!o||0!=i.apply(this.setting.callback.beforeCheck,[this.setting.treeId,e],!0))&&i.uCanDo(this.setting)&&this.setting.check.enable&&!0!==e.nocheck)){e[a]=t;var r=c(e,d.id.CHECK,this.setting);(n||this.setting.check.chkStyle===d.radio.STYLE)&&s.checkNodeRelation(this.setting,e),s.setChkClass(this.setting,r,e),s.repairParentChkClassWithSelf(this.setting,e),o&&this.setting.treeObj.trigger(d.event.CHECK,[null,this.setting.treeId,e])}},t.checkAllNodes=function(e){s.repairAllChk(this.setting,!!e)},t.getCheckedNodes=function(e){var t=this.setting.data.key.children;return e=!1!==e,l.getTreeCheckedNodes(this.setting,l.getRoot(this.setting)[t],e)},t.getChangeCheckedNodes=function(){var e=this.setting.data.key.children;return l.getTreeChangeCheckedNodes(this.setting,l.getRoot(this.setting)[e])},t.setChkDisabled=function(e,t,n,o){t=!!t,n=!!n,o=!!o,s.repairSonChkDisabled(this.setting,e,t,o),s.repairParentChkDisabled(this.setting,e.getParentNode(),t,n)};var n=t.updateNode;t.updateNode=function(e,o){if(n&&n.apply(t,arguments),e&&this.setting.check.enable&&c(e,this.setting).get(0)&&i.uCanDo(this.setting)){var a=c(e,d.id.CHECK,this.setting);1!=o&&this.setting.check.chkStyle!==d.radio.STYLE||s.checkNodeRelation(this.setting,e),s.setChkClass(this.setting,a,e),s.repairParentChkClassWithSelf(this.setting,e)}}});var u=s.createNodes;s.createNodes=function(e,t,n,o,a){u&&u.apply(s,arguments),n&&s.repairParentChkClassWithSelf(e,o)};var h=s.removeNode;s.removeNode=function(e,t){var n=t.getParentNode();h&&h.apply(s,arguments),t&&n&&(s.repairChkClass(e,n),s.repairParentChkClass(e,n))};var f=s.appendNodes;s.appendNodes=function(e,t,n,o,a,r,i){var d="";return f&&(d=f.apply(s,arguments)),o&&l.makeChkFlag(e,o),d}}(jQuery),function(e){var t={event:{DRAG:"ztree_drag",DROP:"ztree_drop",RENAME:"ztree_rename",DRAGMOVE:"ztree_dragmove"},id:{EDIT:"_edit",INPUT:"_input",REMOVE:"_remove"},move:{TYPE_INNER:"inner",TYPE_PREV:"prev",TYPE_NEXT:"next"},node:{CURSELECTED_EDIT:"curSelectedNode_Edit",TMPTARGET_TREE:"tmpTargetzTree",TMPTARGET_NODE:"tmpTargetNode"}},n={onHoverOverNode:function(e,t){var o=s.getSetting(e.data.treeId),a=s.getRoot(o);a.curHoverNode!=t&&n.onHoverOutNode(e),a.curHoverNode=t,d.addHoverDom(o,t)},onHoverOutNode:function(e,t){var n=s.getSetting(e.data.treeId),o=s.getRoot(n);o.curHoverNode&&!s.isSelectedNode(n,o.curHoverNode)&&(d.removeTreeDom(n,o.curHoverNode),o.curHoverNode=null)},onMousedownNode:function(n,o){var a,c,u=s.getSetting(n.data.treeId),h=s.getRoot(u),f=s.getRoots();if(2==n.button||!u.edit.enable||!u.edit.drag.isCopy&&!u.edit.drag.isMove)return!0;var p=n.target,g=s.getRoot(u).curSelectedList,N=[];if(s.isSelectedNode(u,o))for(a=0,c=g.length;a<c;a++){if(g[a].editNameFlag&&r.eqs(p.tagName,"input")&&null!==p.getAttribute("treeNode"+i.id.INPUT))return!0;if(N.push(g[a]),N[0].parentTId!==g[a].parentTId){N=[o];break}}else N=[o];d.editNodeBlur=!0,d.cancelCurEditNode(u);var v,k,C,m,T,y=e(u.treeObj.get(0).ownerDocument),b=e(u.treeObj.get(0).ownerDocument.body),E=!1,I=u,w=u,S=null,P=null,_=null,O=i.move.TYPE_INNER,D=n.clientX,R=n.clientY,L=(new Date).getTime();function A(n){if(0==h.dragFlag&&Math.abs(D-n.clientX)<u.edit.drag.minMoveSize&&Math.abs(R-n.clientY)<u.edit.drag.minMoveSize)return!0;var o,a,c,p,g,w=u.data.key.children;if(b.css("cursor","pointer"),0==h.dragFlag){if(0==r.apply(u.callback.beforeDrag,[u.treeId,N],!0))return x(n),!0;for(o=0,a=N.length;o<a;o++)0==o&&(h.dragNodeShowBefore=[]),(c=N[o]).isParent&&c.open?(d.expandCollapseNode(u,c,!c.open),h.dragNodeShowBefore[c.tId]=!0):h.dragNodeShowBefore[c.tId]=!1;h.dragFlag=1,f.showHoverDom=!1,r.showIfameMask(u,!0);var A=!0,M=-1;if(N.length>1){var j=N[0].parentTId?N[0].getParentNode()[w]:s.getNodes(u);for(g=[],o=0,a=j.length;o<a;o++)if(void 0!==h.dragNodeShowBefore[j[o].tId]&&(A&&M>-1&&M+1!==o&&(A=!1),g.push(j[o]),M=o),N.length===g.length){N=g;break}}for(A&&(m=N[0].getPreNode(),T=N[N.length-1].getNextNode()),v=l("<ul class='zTreeDragUL'></ul>",u),o=0,a=N.length;o<a;o++)(c=N[o]).editNameFlag=!1,d.selectNode(u,c,o>0),d.removeTreeDom(u,c),o>u.edit.drag.maxShowNodeNum-1||((p=l("<li id='"+c.tId+"_tmp'></li>",u)).append(l(c,i.id.A,u).clone()),p.css("padding","0"),p.children("#"+c.tId+i.id.A).removeClass(i.node.CURSELECTED),v.append(p),o==u.edit.drag.maxShowNodeNum-1&&(p=l("<li id='"+c.tId+"_moretmp'><a>  ...  </a></li>",u),v.append(p)));v.attr("id",N[0].tId+i.id.UL+"_tmp"),v.addClass(u.treeObj.attr("class")),v.appendTo(b),(k=l("<span class='tmpzTreeMove_arrow'></span>",u)).attr("id","zTreeMove_arrow_tmp"),k.appendTo(b),u.treeObj.trigger(i.event.DRAG,[n,u.treeId,N])}if(1==h.dragFlag){if(C&&k.attr("id")==n.target.id&&_&&n.clientX+y.scrollLeft()+2>e("#"+_+i.id.A,C).offset().left){var $=e("#"+_+i.id.A,C);n.target=$.length>0?$.get(0):n.target}else C&&(C.removeClass(i.node.TMPTARGET_TREE),_&&e("#"+_+i.id.A,C).removeClass(i.node.TMPTARGET_NODE+"_"+i.move.TYPE_PREV).removeClass(i.node.TMPTARGET_NODE+"_"+t.move.TYPE_NEXT).removeClass(i.node.TMPTARGET_NODE+"_"+t.move.TYPE_INNER));C=null,_=null,E=!1,I=u;var U=s.getSettings();for(var B in U)U[B].treeId&&U[B].edit.enable&&U[B].treeId!=u.treeId&&(n.target.id==U[B].treeId||e(n.target).parents("#"+U[B].treeId).length>0)&&(E=!0,I=U[B]);var F=y.scrollTop(),z=y.scrollLeft(),Y=I.treeObj.offset(),H=I.treeObj.get(0).scrollHeight,K=I.treeObj.get(0).scrollWidth,V=n.clientY+F-Y.top,q=I.treeObj.height()+Y.top-n.clientY-F,W=n.clientX+z-Y.left,G=I.treeObj.width()+Y.left-n.clientX-z,X=V<u.edit.drag.borderMax&&V>u.edit.drag.borderMin,Z=q<u.edit.drag.borderMax&&q>u.edit.drag.borderMin,Q=W<u.edit.drag.borderMax&&W>u.edit.drag.borderMin,J=G<u.edit.drag.borderMax&&G>u.edit.drag.borderMin,ee=V>u.edit.drag.borderMin&&q>u.edit.drag.borderMin&&W>u.edit.drag.borderMin&&G>u.edit.drag.borderMin,te=X&&I.treeObj.scrollTop()<=0,ne=Z&&I.treeObj.scrollTop()+I.treeObj.height()+10>=H,oe=Q&&I.treeObj.scrollLeft()<=0,ae=J&&I.treeObj.scrollLeft()+I.treeObj.width()+10>=K;if(n.target&&r.isChildOrSelf(n.target,I.treeId)){for(var re=n.target;re&&re.tagName&&!r.eqs(re.tagName,"li")&&re.id!=I.treeId;)re=re.parentNode;var ie=!0;for(o=0,a=N.length;o<a;o++){if(c=N[o],re.id===c.tId){ie=!1;break}if(l(c,u).find("#"+re.id).length>0){ie=!1;break}}ie&&n.target&&r.isChildOrSelf(n.target,re.id+i.id.A)&&(C=e(re),_=re.id)}c=N[0],ee&&r.isChildOrSelf(n.target,I.treeId)&&(!C&&(n.target.id==I.treeId||te||ne||oe||ae)&&(E||!E&&c.parentTId)&&(C=I.treeObj),X?I.treeObj.scrollTop(I.treeObj.scrollTop()-10):Z&&I.treeObj.scrollTop(I.treeObj.scrollTop()+10),Q?I.treeObj.scrollLeft(I.treeObj.scrollLeft()-10):J&&I.treeObj.scrollLeft(I.treeObj.scrollLeft()+10),C&&C!=I.treeObj&&C.offset().left<I.treeObj.offset().left&&I.treeObj.scrollLeft(I.treeObj.scrollLeft()+C.offset().left-I.treeObj.offset().left)),v.css({top:n.clientY+F+3+"px",left:n.clientX+z+3+"px"});var de=0,se=0;if(C&&C.attr("id")!=I.treeId){var le=null==_?null:s.getNodeCache(I,_),ce=(n.ctrlKey||n.metaKey)&&u.edit.drag.isMove&&u.edit.drag.isCopy||!u.edit.drag.isMove&&u.edit.drag.isCopy,ue=!(!m||_!==m.tId),he=!(!T||_!==T.tId),fe=c.parentTId&&c.parentTId==_,pe=(ce||!he)&&r.apply(I.edit.drag.prev,[I.treeId,N,le],!!I.edit.drag.prev),ge=(ce||!ue)&&r.apply(I.edit.drag.next,[I.treeId,N,le],!!I.edit.drag.next),Ne=(ce||!fe)&&!(I.data.keep.leaf&&!le.isParent)&&r.apply(I.edit.drag.inner,[I.treeId,N,le],!!I.edit.drag.inner);function ve(){C=null,_="",O=i.move.TYPE_INNER,k.css({display:"none"}),window.zTreeMoveTimer&&(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null)}if(pe||ge||Ne){var ke=e("#"+_+i.id.A,C),Ce=le.isLastNode?null:e("#"+le.getNextNode().tId+i.id.A,C.next()),me=ke.offset().top,Te=ke.offset().left,ye=pe?Ne?.25:ge?.5:1:-1,be=ge?Ne?.75:pe?.5:0:-1,Ee=(n.clientY+F-me)/ke.height();if((1==ye||Ee<=ye&&Ee>=-.2)&&pe?(de=1-k.width(),se=me-k.height()/2,O=i.move.TYPE_PREV):(0==be||Ee>=be&&Ee<=1.2)&&ge?(de=1-k.width(),se=null==Ce||le.isParent&&le.open?me+ke.height()-k.height()/2:Ce.offset().top-k.height()/2,O=i.move.TYPE_NEXT):Ne?(de=5-k.width(),se=me,O=i.move.TYPE_INNER):ve(),C&&(k.css({display:"block",top:se+"px",left:Te+de+"px"}),ke.addClass(i.node.TMPTARGET_NODE+"_"+O),S==_&&P==O||(L=(new Date).getTime()),le&&le.isParent&&O==i.move.TYPE_INNER)){var Ie=!0;window.zTreeMoveTimer&&window.zTreeMoveTargetNodeTId!==le.tId?(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null):window.zTreeMoveTimer&&window.zTreeMoveTargetNodeTId===le.tId&&(Ie=!1),Ie&&(window.zTreeMoveTimer=setTimeout(function(){O==i.move.TYPE_INNER&&le&&le.isParent&&!le.open&&(new Date).getTime()-L>I.edit.drag.autoOpenTime&&r.apply(I.callback.beforeDragOpen,[I.treeId,le],!0)&&(d.switchNode(I,le),I.edit.drag.autoExpandTrigger&&I.treeObj.trigger(i.event.EXPAND,[I.treeId,le]))},I.edit.drag.autoOpenTime+50),window.zTreeMoveTargetNodeTId=le.tId)}}else ve()}else O=i.move.TYPE_INNER,C&&r.apply(I.edit.drag.inner,[I.treeId,N,null],!!I.edit.drag.inner)?C.addClass(i.node.TMPTARGET_TREE):C=null,k.css({display:"none"}),window.zTreeMoveTimer&&(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null);S=_,P=O,u.treeObj.trigger(i.event.DRAGMOVE,[n,u.treeId,N])}return!1}function x(n){if(window.zTreeMoveTimer&&(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null),S=null,P=null,y.unbind("mousemove",A),y.unbind("mouseup",x),y.unbind("selectstart",M),b.css("cursor",""),C&&(C.removeClass(i.node.TMPTARGET_TREE),_&&e("#"+_+i.id.A,C).removeClass(i.node.TMPTARGET_NODE+"_"+i.move.TYPE_PREV).removeClass(i.node.TMPTARGET_NODE+"_"+t.move.TYPE_NEXT).removeClass(i.node.TMPTARGET_NODE+"_"+t.move.TYPE_INNER)),r.showIfameMask(u,!1),f.showHoverDom=!0,0!=h.dragFlag){var o,a,c;for(h.dragFlag=0,o=0,a=N.length;o<a;o++)(c=N[o]).isParent&&h.dragNodeShowBefore[c.tId]&&!c.open&&(d.expandCollapseNode(u,c,!c.open),delete h.dragNodeShowBefore[c.tId]);v&&v.remove(),k&&k.remove();var p=(n.ctrlKey||n.metaKey)&&u.edit.drag.isMove&&u.edit.drag.isCopy||!u.edit.drag.isMove&&u.edit.drag.isCopy;if(!p&&C&&_&&N[0].parentTId&&_==N[0].parentTId&&O==i.move.TYPE_INNER&&(C=null),C){var g=null==_?null:s.getNodeCache(I,_);if(0==r.apply(u.callback.beforeDrop,[I.treeId,N,g,O,p],!0))return void d.selectNodes(w,N);var m=p?r.clone(N):N;function T(){if(E){if(!p)for(var e=0,t=N.length;e<t;e++)d.removeNode(u,N[e]);O==i.move.TYPE_INNER?d.addNodes(I,g,-1,m):d.addNodes(I,g.getParentNode(),O==i.move.TYPE_PREV?g.getIndex():g.getIndex()+1,m)}else if(p&&O==i.move.TYPE_INNER)d.addNodes(I,g,-1,m);else if(p)d.addNodes(I,g.getParentNode(),O==i.move.TYPE_PREV?g.getIndex():g.getIndex()+1,m);else if(O!=i.move.TYPE_NEXT)for(e=0,t=m.length;e<t;e++)d.moveNode(I,g,m[e],O,!1);else for(e=-1,t=m.length-1;e<t;t--)d.moveNode(I,g,m[t],O,!1);d.selectNodes(I,m);var o=l(m[0],u).get(0);d.scrollIntoView(u,o),u.treeObj.trigger(i.event.DROP,[n,I.treeId,m,g,O,p])}O==i.move.TYPE_INNER&&r.canAsync(I,g)?d.asyncNode(I,g,!1,T):T()}else d.selectNodes(w,N),u.treeObj.trigger(i.event.DROP,[n,u.treeId,N,null,null,null])}}function M(){return!1}return r.uCanDo(u)&&y.bind("mousemove",A),y.bind("mouseup",x),y.bind("selectstart",M),n.preventDefault&&n.preventDefault(),!0}},o={tools:{getAbs:function(e){var t=e.getBoundingClientRect(),n=document.body.scrollTop+document.documentElement.scrollTop,o=document.body.scrollLeft+document.documentElement.scrollLeft;return[t.left+o,t.top+n]},inputFocus:function(e){e.get(0)&&(e.focus(),r.setCursorPosition(e.get(0),e.val().length))},inputSelect:function(e){e.get(0)&&(e.focus(),e.select())},setCursorPosition:function(e,t){if(e.setSelectionRange)e.focus(),e.setSelectionRange(t,t);else if(e.createTextRange){var n=e.createTextRange();n.collapse(!0),n.moveEnd("character",t),n.moveStart("character",t),n.select()}},showIfameMask:function(e,t){for(var n=s.getRoot(e);n.dragMaskList.length>0;)n.dragMaskList[0].remove(),n.dragMaskList.shift();if(t)for(var o=l("iframe",e),a=0,i=o.length;a<i;a++){var d=o.get(a),c=r.getAbs(d),u=l("<div id='zTreeMask_"+a+"' class='zTreeMask' style='top:"+c[1]+"px; left:"+c[0]+"px; width:"+d.offsetWidth+"px; height:"+d.offsetHeight+"px;'></div>",e);u.appendTo(l("body",e)),n.dragMaskList.push(u)}}},view:{addEditBtn:function(e,t){if(!(t.editNameFlag||l(t,i.id.EDIT,e).length>0)&&r.apply(e.edit.showRenameBtn,[e.treeId,t],e.edit.showRenameBtn)){var n=l(t,i.id.A,e),o="<span class='"+i.className.BUTTON+" edit' id='"+t.tId+i.id.EDIT+"' title='"+r.apply(e.edit.renameTitle,[e.treeId,t],e.edit.renameTitle)+"' treeNode"+i.id.EDIT+" style='display:none;'></span>";n.append(o),l(t,i.id.EDIT,e).bind("click",function(){return!(!r.uCanDo(e)||0==r.apply(e.callback.beforeEditName,[e.treeId,t],!0))&&(d.editNode(e,t),!1)}).show()}},addRemoveBtn:function(e,t){if(!(t.editNameFlag||l(t,i.id.REMOVE,e).length>0)&&r.apply(e.edit.showRemoveBtn,[e.treeId,t],e.edit.showRemoveBtn)){var n=l(t,i.id.A,e),o="<span class='"+i.className.BUTTON+" remove' id='"+t.tId+i.id.REMOVE+"' title='"+r.apply(e.edit.removeTitle,[e.treeId,t],e.edit.removeTitle)+"' treeNode"+i.id.REMOVE+" style='display:none;'></span>";n.append(o),l(t,i.id.REMOVE,e).bind("click",function(){return!(!r.uCanDo(e)||0==r.apply(e.callback.beforeRemove,[e.treeId,t],!0))&&(d.removeNode(e,t),e.treeObj.trigger(i.event.REMOVE,[e.treeId,t]),!1)}).bind("mousedown",function(e){return!0}).show()}},addHoverDom:function(e,t){s.getRoots().showHoverDom&&(t.isHover=!0,e.edit.enable&&(d.addEditBtn(e,t),d.addRemoveBtn(e,t)),r.apply(e.view.addHoverDom,[e.treeId,t]))},cancelCurEditNode:function(e,t,n){var o=s.getRoot(e),a=e.data.key.name,c=o.curEditNode;if(c){var u=o.curEditInput,h=t||(n?c[a]:u.val());if(!1===r.apply(e.callback.beforeRename,[e.treeId,c,h,n],!0))return!1;c[a]=h,l(c,i.id.A,e).removeClass(i.node.CURSELECTED_EDIT),u.unbind(),d.setNodeName(e,c),c.editNameFlag=!1,o.curEditNode=null,o.curEditInput=null,d.selectNode(e,c,!1),e.treeObj.trigger(i.event.RENAME,[e.treeId,c,n])}return o.noSelection=!0,!0},editNode:function(e,t){var n=s.getRoot(e);if(d.editNodeBlur=!1,s.isSelectedNode(e,t)&&n.curEditNode==t&&t.editNameFlag)setTimeout(function(){r.inputFocus(n.curEditInput)},0);else{var o=e.data.key.name;t.editNameFlag=!0,d.removeTreeDom(e,t),d.cancelCurEditNode(e),d.selectNode(e,t,!1),l(t,i.id.SPAN,e).html("<input type=text class='rename' id='"+t.tId+i.id.INPUT+"' treeNode"+i.id.INPUT+" >");var a=l(t,i.id.INPUT,e);a.attr("value",t[o]),e.edit.editNameSelectAll?r.inputSelect(a):r.inputFocus(a),a.bind("blur",function(t){d.editNodeBlur||d.cancelCurEditNode(e)}).bind("keydown",function(t){"13"==t.keyCode?(d.editNodeBlur=!0,d.cancelCurEditNode(e)):"27"==t.keyCode&&d.cancelCurEditNode(e,null,!0)}).bind("click",function(e){return!1}).bind("dblclick",function(e){return!1}),l(t,i.id.A,e).addClass(i.node.CURSELECTED_EDIT),n.curEditInput=a,n.noSelection=!1,n.curEditNode=t}},moveNode:function(e,t,n,o,a,r){var c=s.getRoot(e),u=e.data.key.children;if(t!=n&&(!e.data.keep.leaf||!t||t.isParent||o!=i.move.TYPE_INNER)){var h=n.parentTId?n.getParentNode():c,f=null===t||t==c;f&&null===t&&(t=c),f&&(o=i.move.TYPE_INNER);var p,g,N=t.parentTId?t.getParentNode():c;if(o!=i.move.TYPE_PREV&&o!=i.move.TYPE_NEXT&&(o=i.move.TYPE_INNER),o==i.move.TYPE_INNER&&(f?n.parentTId=null:(t.isParent||(t.isParent=!0,t.open=!!t.open,d.setNodeLineIcos(e,t)),n.parentTId=t.tId)),f)g=p=e.treeObj;else{if(r||o!=i.move.TYPE_INNER?r||d.expandCollapseNode(e,t.getParentNode(),!0,!1):d.expandCollapseNode(e,t,!0,!1),p=l(t,e),g=l(t,i.id.UL,e),p.get(0)&&!g.get(0)){var v=[];d.makeUlHtml(e,t,v,""),p.append(v.join(""))}g=l(t,i.id.UL,e)}var k=l(n,e);k.get(0)?p.get(0)||k.remove():k=d.appendNodes(e,n.level,[n],null,-1,!1,!0).join(""),g.get(0)&&o==i.move.TYPE_INNER?g.append(k):p.get(0)&&o==i.move.TYPE_PREV?p.before(k):p.get(0)&&o==i.move.TYPE_NEXT&&p.after(k);var C,m,T=-1,y=0,b=null,E=null,I=n.level;if(n.isFirstNode)T=0,h[u].length>1&&((b=h[u][1]).isFirstNode=!0);else if(n.isLastNode)T=h[u].length-1,(b=h[u][T-1]).isLastNode=!0;else for(C=0,m=h[u].length;C<m;C++)if(h[u][C].tId==n.tId){T=C;break}if(T>=0&&h[u].splice(T,1),o!=i.move.TYPE_INNER)for(C=0,m=N[u].length;C<m;C++)N[u][C].tId==t.tId&&(y=C);if(o==i.move.TYPE_INNER?(t[u]||(t[u]=new Array),t[u].length>0&&((E=t[u][t[u].length-1]).isLastNode=!1),t[u].splice(t[u].length,0,n),n.isLastNode=!0,n.isFirstNode=1==t[u].length):t.isFirstNode&&o==i.move.TYPE_PREV?(N[u].splice(y,0,n),(E=t).isFirstNode=!1,n.parentTId=t.parentTId,n.isFirstNode=!0,n.isLastNode=!1):t.isLastNode&&o==i.move.TYPE_NEXT?(N[u].splice(y+1,0,n),(E=t).isLastNode=!1,n.parentTId=t.parentTId,n.isFirstNode=!1,n.isLastNode=!0):(o==i.move.TYPE_PREV?N[u].splice(y,0,n):N[u].splice(y+1,0,n),n.parentTId=t.parentTId,n.isFirstNode=!1,n.isLastNode=!1),s.fixPIdKeyValue(e,n),s.setSonNodeLevel(e,n.getParentNode(),n),d.setNodeLineIcos(e,n),d.repairNodeLevelClass(e,n,I),!e.data.keep.parent&&h[u].length<1){h.isParent=!1,h.open=!1;var w=l(h,i.id.UL,e),S=l(h,i.id.SWITCH,e),P=l(h,i.id.ICON,e);d.replaceSwitchClass(h,S,i.folder.DOCU),d.replaceIcoClass(h,P,i.folder.DOCU),w.css("display","none")}else b&&d.setNodeLineIcos(e,b);E&&d.setNodeLineIcos(e,E),e.check&&e.check.enable&&d.repairChkClass&&(d.repairChkClass(e,h),d.repairParentChkClassWithSelf(e,h),h!=n.parent&&d.repairParentChkClassWithSelf(e,n)),r||d.expandCollapseParentNode(e,n.getParentNode(),!0,a)}},removeEditBtn:function(e,t){l(t,i.id.EDIT,e).unbind().remove()},removeRemoveBtn:function(e,t){l(t,i.id.REMOVE,e).unbind().remove()},removeTreeDom:function(e,t){t.isHover=!1,d.removeEditBtn(e,t),d.removeRemoveBtn(e,t),r.apply(e.view.removeHoverDom,[e.treeId,t])},repairNodeLevelClass:function(e,t,n){if(n!==t.level){var o=l(t,e),a=l(t,i.id.A,e),r=l(t,i.id.UL,e),d=i.className.LEVEL+n,s=i.className.LEVEL+t.level;o.removeClass(d),o.addClass(s),a.removeClass(d),a.addClass(s),r.removeClass(d),r.addClass(s)}},selectNodes:function(e,t){for(var n=0,o=t.length;n<o;n++)d.selectNode(e,t[n],n>0)}},event:{},data:{setSonNodeLevel:function(e,t,n){if(n){var o=e.data.key.children;if(n.level=t?t.level+1:0,n[o])for(var a=0,r=n[o].length;a<r;a++)n[o][a]&&s.setSonNodeLevel(e,n,n[o][a])}}}};e.extend(!0,e.fn.zTree.consts,t),e.extend(!0,e.fn.zTree._z,o);var a=e.fn.zTree,r=a._z.tools,i=a.consts,d=a._z.view,s=a._z.data,l=(a._z.event,r.$);s.exSetting({edit:{enable:!1,editNameSelectAll:!1,showRemoveBtn:!0,showRenameBtn:!0,removeTitle:"remove",renameTitle:"rename",drag:{autoExpandTrigger:!1,isCopy:!0,isMove:!0,prev:!0,next:!0,inner:!0,minMoveSize:5,borderMax:10,borderMin:-5,maxShowNodeNum:5,autoOpenTime:500}},view:{addHoverDom:null,removeHoverDom:null},callback:{beforeDrag:null,beforeDragOpen:null,beforeDrop:null,beforeEditName:null,beforeRename:null,onDrag:null,onDragMove:null,onDrop:null,onRename:null}}),s.addInitBind(function(e){var t=e.treeObj,n=i.event;t.bind(n.RENAME,function(t,n,o,a){r.apply(e.callback.onRename,[t,n,o,a])}),t.bind(n.DRAG,function(t,n,o,a){r.apply(e.callback.onDrag,[n,o,a])}),t.bind(n.DRAGMOVE,function(t,n,o,a){r.apply(e.callback.onDragMove,[n,o,a])}),t.bind(n.DROP,function(t,n,o,a,i,d,s){r.apply(e.callback.onDrop,[n,o,a,i,d,s])})}),s.addInitUnBind(function(e){var t=e.treeObj,n=i.event;t.unbind(n.RENAME),t.unbind(n.DRAG),t.unbind(n.DRAGMOVE),t.unbind(n.DROP)}),s.addInitCache(function(e){}),s.addInitNode(function(e,t,n,o,a,r,i){n&&(n.isHover=!1,n.editNameFlag=!1)}),s.addInitProxy(function(e){var t=e.target,o=s.getSetting(e.data.treeId),a=e.relatedTarget,d="",l=null,c="",u=null,h=null;if(r.eqs(e.type,"mouseover")?(h=r.getMDom(o,t,[{tagName:"a",attrName:"treeNode"+i.id.A}]))&&(d=r.getNodeMainDom(h).id,c="hoverOverNode"):r.eqs(e.type,"mouseout")?(h=r.getMDom(o,a,[{tagName:"a",attrName:"treeNode"+i.id.A}]))||(d="remove",c="hoverOutNode"):r.eqs(e.type,"mousedown")&&(h=r.getMDom(o,t,[{tagName:"a",attrName:"treeNode"+i.id.A}]))&&(d=r.getNodeMainDom(h).id,c="mousedownNode"),d.length>0)switch(l=s.getNodeCache(o,d),c){case"mousedownNode":u=n.onMousedownNode;break;case"hoverOverNode":u=n.onHoverOverNode;break;case"hoverOutNode":u=n.onHoverOutNode}return{stop:!1,node:l,nodeEventType:c,nodeEventCallback:u,treeEventType:"",treeEventCallback:null}}),s.addInitRoot(function(e){var t=s.getRoot(e),n=s.getRoots();t.curEditNode=null,t.curEditInput=null,t.curHoverNode=null,t.dragFlag=0,t.dragNodeShowBefore=[],t.dragMaskList=new Array,n.showHoverDom=!0}),s.addZTreeTools(function(e,t){t.cancelEditName=function(e){s.getRoot(this.setting).curEditNode&&d.cancelCurEditNode(this.setting,e||null,!0)},t.copyNode=function(e,t,n,o){if(!t)return null;if(e&&!e.isParent&&this.setting.data.keep.leaf&&n===i.move.TYPE_INNER)return null;var a=this,s=r.clone(t);if(e||(e=null,n=i.move.TYPE_INNER),n==i.move.TYPE_INNER){function l(){d.addNodes(a.setting,e,-1,[s],o)}r.canAsync(this.setting,e)?d.asyncNode(this.setting,e,o,l):l()}else d.addNodes(this.setting,e.parentNode,-1,[s],o),d.moveNode(this.setting,e,s,n,!1,o);return s},t.editName=function(e){e&&e.tId&&e===s.getNodeCache(this.setting,e.tId)&&(e.parentTId&&d.expandCollapseParentNode(this.setting,e.getParentNode(),!0),d.editNode(this.setting,e))},t.moveNode=function(e,t,n,o){if(!t)return t;if(e&&!e.isParent&&this.setting.data.keep.leaf&&n===i.move.TYPE_INNER)return null;if(e&&(t.parentTId==e.tId&&n==i.move.TYPE_INNER||l(t,this.setting).find("#"+e.tId).length>0))return null;e||(e=null);var a=this;function s(){d.moveNode(a.setting,e,t,n,!1,o)}return r.canAsync(this.setting,e)&&n===i.move.TYPE_INNER?d.asyncNode(this.setting,e,o,s):s(),t},t.setEditable=function(e){return this.setting.edit.enable=e,this.refresh()}});var c=d.cancelPreSelectedNode;d.cancelPreSelectedNode=function(e,t){for(var n=s.getRoot(e).curSelectedList,o=0,a=n.length;o<a&&(t&&t!==n[o]||(d.removeTreeDom(e,n[o]),!t));o++);c&&c.apply(d,arguments)};var u=d.createNodes;d.createNodes=function(e,t,n,o,a){u&&u.apply(d,arguments),n&&d.repairParentChkClassWithSelf&&d.repairParentChkClassWithSelf(e,o)};var h=d.makeNodeUrl;d.makeNodeUrl=function(e,t){return e.edit.enable?null:h.apply(d,arguments)};var f=d.removeNode;d.removeNode=function(e,t){var n=s.getRoot(e);n.curEditNode===t&&(n.curEditNode=null),f&&f.apply(d,arguments)};var p=d.selectNode;d.selectNode=function(e,t,n){var o=s.getRoot(e);return(!s.isSelectedNode(e,t)||o.curEditNode!=t||!t.editNameFlag)&&(p&&p.apply(d,arguments),d.addHoverDom(e,t),!0)};var g=r.uCanDo;r.uCanDo=function(e,t){var n=s.getRoot(e);return!(!t||!(r.eqs(t.type,"mouseover")||r.eqs(t.type,"mouseout")||r.eqs(t.type,"mousedown")||r.eqs(t.type,"mouseup")))||(n.curEditNode&&(d.editNodeBlur=!1,n.curEditInput.focus()),!n.curEditNode&&(!g||g.apply(d,arguments)))}}(jQuery);