/*分页结构*/
.pagination a {
    text-decoration: none;
	border: solid 1px;	
}


.pagination .pxofy{
	float:left;
	margin-left: 5px;
	height:20px;
	*padding-top:1px; line-height:20px;
}	

.pagination a, .pagination span {
	margin-right:5px;	
    display: block;
    float: left;
	height:20px;
	line-height:20px;
	padding:0;
	font-family:Arial, Helvetica, sans-serif !important;
	width:20px;
	font-size:12px;
}

.pagination .current {
	cursor:default;
	border:0 ; background:#51a91e;
}

.pagination .prev, .pagination .next{
	*line-height:20px;
}

/*分页样式*/
.pagination a{
	color: #fff;
	border:0;
	background: URL(../Images/cc.png) 0 0 no-repeat;
}

.pagination a:hover{
	color:#fff;
		background:URL(../Images/page_current.png) 0 0 no-repeat;
}

.pagination .current{
	color:#fff;
	border:0;width:20px;
	background:URL(../Images/c.png) 0 0 no-repeat;
}

.pagination .current.prev{
	 border:0; width:20px;
	background:URL(../Images/ll.png) 0 0 no-repeat;
}
.pagination a.prev{ border:0; width:20px;
	background:URL(../Images/l.png) 0 0 no-repeat;}
.pagination a.next{ border:0; width:20px;
	background:URL(../Images/r.png) 0 0 no-repeat;}
.pagination .current.next{border:0; width:20px;
	background:URL(../Images/rr.png) 0 0 no-repeat;}
.pagination .pxofy{
	font-family:Microsoft YaHei;
}


