@charset "utf-8";
/* CSS Document */
* {
	margin: 0px;
	padding: 0px;
}
@font-face {
    font-family: LED;
    src: url('../css/font/LEDNUM.eot');
    src: local('☺'), url('../css/font/LEDNUM.ttf'),url('../css/font/LEDNUM.otf'),url('../css/font/LEDNUM.woff'),url('../css/font/LEDNUM.svg');
}
body {
	text-align: center;
	font-size: 12px;
	font-family: Microsoft YaHei;
	height: 100%;
	color: #4b4b4b;
	background: #eff5fb;
}

ul li {
	list-style: none;
	padding: 0px;
	text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
}
img {
	border: 0;
}
.alL {
	text-align: left;
}
.alR {
	text-align: right;
}
.alC {
	text-align: center;
}
.fB {
	font-weight: bold;
}
.mt20 {
	margin-top: 20px;
}
.ml20 {
	margin-left: 20px;
}
.mt2 {
	margin-top: 2px;
}
.mt3 {
	margin-top: 3px;
}
.ml5 {
	margin-left: 5px;
}
.ml_10 {
	margin-left: 10px;
}
.mr5 {
	margin-right: 5px;
}
.mr20 {
	margin-right: 20px;
}
.mt5 {
	margin-top: 5px;
}
.mt10 {
	margin-top: 10px;
}
.mb5 {
	margin-bottom: 5px;
}
.mb10 {
	margin-bottom: 10px;
}
.mb20 {
	margin-bottom: 20px;
}
.pb10 {
	padding-bottom: 10px;
}
.pl10 {
	padding-left: 10px;
}
.pl20 {
	padding-left: 20px;
}
.pt5 {
	padding-top: 5px;
}
.p5 {
	padding: 3px 12px;
}
.pt20 {
	padding-top: 20px;
}
.tcred {
	color: #ff0000;
}
.txtbox {
	width: 150px;
}
.selbox {
	width: 154px;
}
.hide {
	display: none;
}
.f16px {
	font-size: 16px;
}
.f24px {
	font-size: 24px;
}
.f14px {
	font-size: 14px;
}
.fm_yahei {
	font-family: Microsoft YaHei;
}
.h24 {
	height: 24px;
}
.lh24 {
	line-height: 24px;
}
.lh26 {
	line-height: 26px;
}
.lh22 {
	line-height: 22px;
}
.lh20 {
	line-height: 20px;
}
.h30 {
	height: 30px;
}
.lh30 {
	line-height: 30px;
}
.h35 {
	height: 35px;
}
.lh35 {
	line-height: 35px;
}
.L {
	float: left;
}
.clear {
	clear: both;
}
.no_br {
	display:block;white-space:nowrap; overflow:hidden; text-overflow:ellipsis;
}
.ue-table .table-panel td .no_br {
 margin:0 auto;white-space:nowrap; overflow:hidden; text-overflow:ellipsis; word-break:normal; word-wrap:normal;
}
.R {
	float: right;
}
.tc_green {
	color: #2ed900;
}
.tc_orange {
	color: #f06e09;
}
.tc_fa9800 {
	color: #fa9800;
}
.tcff7800 {
	color: #ff7800;
}
.button_s {
	height: 20px;
	line-height: 20px;
	display: inline;
	padding: 0 7px;
	padding-top: 1px;
	cursor: pointer;
	padding-bottom: 1px;
	background: #fff;
	border: 1px solid #bababa;
}
textarea {
	resize: none;
}
.w100 {
	width: 100%;
	overflow: hidden
}
.w50px {
	width: 50px;
}
.button_box_common {
	text-align: right;
	float: right;
	height: 35px;
	margin-top: 5px;
}
.button_box_common.m0 {
	margin: 0;
}
.hand {
	cursor: pointer;
}
.t_indent1 {
	text-indent: 1em;
}
.t_indent2 {
	text-indent: 2em;
}
.delete_icona {
	position: absolute;
	right: 5px;
	top: 5px;
}
.dot_white {
	background: url(../Images/dott2.png) repeat-x 0 bottom;
}
.checkbox {
	height: 13px;
	line-height: 13px;
	vertical-align: middle;
	margin-right: 5px;
}
.font_normal {
	font-weight: 100;
	font-size: 12px
}
.plr_5 {
	padding: 0 5px;
}
#totalFee,#totalFee2,#changeTotalFee,#changePercent{
	color: #ff7800;
	font-size: 16px;
	font-weight: bold;
}

/*文字数字显示*/
.num_title {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	text-align: right;
}
.num_title.add_bg_01 {
	background: url(../Images/num_list_bg_02.png) left center no-repeat;
}
.num_title.add_bg_02 {
	background: url(../Images/num_list_bg_03.png) left center no-repeat;
}
.num_title.add_bg_03 {
	background: url(../Images/num_list_bg_04.png) left center no-repeat;
}
.num_title.add_bg_04 {
	background: url(../Images/num_list_bg_05.png) left center no-repeat;
}
.num_text_normal {
	font-size: 16px;
	text-align: right;
	font-family: Microsoft YaHei;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.num_unit {
	padding-top: 0px;
}
.num_unit_w {
	width: 80px;
	text-align: left;
}
.num_four .num_title {
	width: 48px;
}
.num_six .num_title {
	width: 60px;
}
.num_four .num_text_normal {
	width: 80px;
}
.num_six .num_text_normal {
	width: 70px;
}
.unit_num {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 38px;
	display: block;
	float: left;
}
.U {
	text-decoration: underline;
}
/* 框架头部*/
.main_body {
	min-width: 1280px;
}
.header {
	padding-left: 55px;
	height: 81px;
	background: url() 25px 6px no-repeat;
	background-color: #1487e6;
	border-bottom: 5px solid #fa9800;
	position: relative;
}
.weather {
	width: 300px;
	position: absolute;
	left: 50%;
	margin-left: -150px;
	text-align: center;
	color: #fff;
	top: 5px;
}
.right_link {
	float: right;
	background: url(../Images/head_link_c.png) repeat-x;
	height: 29px;
	margin-top: 5px;
	margin-right: 44px;
}
.header .header_link {
	float: right;
	width: auto;
	height: 29px;
	background: url(../Images/head_link_r.png) right no-repeat;
	margin-right: -14px;
}
.header .header_link li {
	float: left;
	margin-top: 2px;
	margin-right: 4px;
	height: 25px;
	line-height: 25px;
	padding-right: 15px;
	padding-left: 30px;
	color: #fff;
	font-size: 12px;
	text-align: left;
	display: block;
}
.header .header_link li {
	cursor: pointer;
}
.header .header_link .pass {
	background: url(../Images/pwd_icon.png) no-repeat;
}
.header .header_link .pass:hover {
	background: url(../Images/pwd_h_icon.png) no-repeat;
}
.header .header_link .contact {
	background: url(../Images/pwd_icon.png) no-repeat;
}
.header .header_link .contact:hover {
	background: url(../Images/pwd_h_icon.png) no-repeat;
}
.header .header_link .help {
	background: url(../Images/help_icon.png) no-repeat;
}
.header .header_link .help:hover {
	background: url(../Images/help_h_icon.png) no-repeat;
}
.header .header_link .exit {
	background: url(../Images/exit_icon.png) no-repeat;
}
.header .header_link .exit:hover {
	background: url(../Images/exit_h_icon.png) no-repeat;
}
.header .header_link .pass_on {
	background: url(../Images/pwd_on_icon.png) no-repeat;
}
.header .header_link .exit_on {
	background: url(../Images/exit_on_icon.png) no-repeat;
}
/*导航菜单*/
.menu_main {
	width: 100%;
	float: left;
	height: 35px;
	position: relative;
	margin-top: 12px;
}
.welcome {
	width: auto;
	float: right;
	height: 29px;
	padding-left: 34px;
	background: url(../Images/wel_admin_icon.png) 0 center no-repeat;
	margin-right: 250px;
	position: absolute;
	right: 275px;
}
.global-top-right li.wel_user_text {
	padding-right: 65px;
}
.level_bg {
	margin-left: 5px;
	cursor: pointer;
	position: absolute;
	right: 0;
	top: 3px;
}
.level_num {
	position: absolute;
	top: 8px;
	right: 18px;
	font-weight: bold;
	line-height: 12px;
}
.wel_txt {
	font-family: Microsoft YaHei;
	text-align: left;
	float: left;
	height: 29px;
	line-height: 29px;
	color: #fff;
	width: 100px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.exit {
	float: left;
}
.menu_main_l {
	float: right;
	padding-left: 10px;
	height: 35px;
}
.menu {
	float: right;
	margin-right: 60px;
	background-color: #4fa5ec;
}
.menu>li {
	width: 87px;
	height: 35px;
	line-height: 35px;
	float: left;
	border-right: 1px solid #309de5;
	border-left: 1px solid #5dafed;
	cursor: pointer;
	color: #fff;
	font-size: 16px;
	font-family: Microsoft YaHei;
	position: relative;
}
.menu>li a {
	color: #fff;
	text-decoration: none
}
.menu>li:first-child {
	background: url(../Images/main_menu_l.png) no-repeat;
	border-left: 0;
}
.menu>li:first-child.on {
	background: url(../Images/main_menu_l_on.png) no-repeat;
	background-color: #fa9800;
}
.menu>li:first-child.hov {
	background: url(../Images/main_menu_l_h.png) no-repeat;
	background-color: #0A4373;
}
.menu>li.last_child {
	background: url(../Images/main_menu_r.png) right no-repeat;
	border-right: 0;
}
.menu>li.last_child.on {
	background: url(../Images/main_menu_r_on.png) right no-repeat;
	background-color: #fa9800;
}
.menu>li.last_child.hov {
	background: url(../Images/main_menu_r_h.png) right no-repeat;
	background-color: #0A4373;
}
.menu li.on {
	background-color: #fa9800;
}
.menu li.hov {
	background-color: #0A4373;
}
.menu li .menu_s {
	position: absolute;
	top: 34px;
	left: 0;
	background: #4fa5ec;
	width: 77px;
	padding: 5px;
	z-index: 99;
}
.menu li .menu_s li {
	width: auto;
	height: 24px;
	float: none;
	font: 12px '宋体';
	font-weight: 100;
	line-height: 24px;
	text-align: left;
	text-indent: 5px;
}
.menu li .menu_s li a {
	color: #fff;
	text-decoration: none;
}
.menu li .menu_s li a:hover {
	text-decoration: underline;
}
/*框架底部*/ 
.bottom {
	height: 27px;
	color: #d6d7d8;
	text-align: center;
	background: #393f46;
	padding-right: 20px;
	line-height: 27px;
}
/*框架内容页*/
.UI_main {
	padding: 0 0 0 55px;
	margin: 0 auto;
}
.UI_main .UI_main_tree {
	width: 203px;
	background: #4d5a67;
}
.UI_main .UI_main_tree .tree_title {
	height: 34px;
	line-height: 34px;
	font-size: 16px;
	color: #414141;
	padding-bottom: 16px;
	font-family: Microsoft YaHei;
}
.UI_main .UI_main_tree .Tree_main {
	padding-bottom: 0px;
	height: 100%;
}
.UI_main .Tree_main_content {
	width: 203px;
	padding: 0;
	height: 100%;
}
.UI_main .Tree_main_content .treemain_c {
	width: 100%;
	overflow: auto;
	height: 100%;
}
.UI_main .UI_main_content {
	height: 100%;
}
.UI_main .UI_main_content .top_title {
	height: 35px;
	background: #f1f1f1;
	padding-left: 9px;
}
.web_url {
	background: url(../Images/pos_info_l.gif) no-repeat;
	padding-left: 5px;
	height: 25px;
	line-height: 25px;
	font-size: 14px;
	font-family: Microsoft YaHei;
	float: left;
	margin-top: 5px;
}
.web_url .web_url_r {
	padding-right: 5px;
	background: url(../Images/pos_info_r.gif) right 0 no-repeat;
	height: 25px;
}
.web_url .add_text {
	display: block;
	float: left;
	background: url(../Images/pos_info_def_bg.gif) repeat-x;
	color: #87C6FF;
}
.web_url .add_text a {
	color: #87C6FF;
	text-decoration: none;
	float: left;
	padding: 0 4px;
}
.add_text a.on_page {
	background: url(../Images/pos_info_cur_bg.gif) repeat-x;
	color: #fff;
}
.button_energy {
	float: right;
}
.button_energy .web_url_r {
	background: url(../Images/pos_info_l2.gif) right 0 no-repeat;
}
.button_energy .web_url_r a:hover {
	background: url(../Images/pos_info_cur_bg.gif) repeat-x;
	color: #fff;
}
.web_url i {
	background: url(../Images/pos_info_lr.gif);
	height: 25px;
	width: 2px;
	display: block;
	float: left;
}
.UI_main .UI_main_content .Main_c {
	padding: 0;height:650px;
}
.UI_main .UI_main_content .choose_option {
	background: url(../Images/item_nav.png) 0 center no-repeat;
	padding-left: 16px;
	font-size: 14px;
	color: #4b4b4b;
	font-weight: 100;
	text-align: left;
	height: 35px;
	font-family: Microsoft YaHei;
	line-height: 35px;
	float: left;
}
.UI_main .UI_main_content .Main_c .Main_c_top_l {
	background: url(../Images/top_l_bg_cc.png) no-repeat;
	height: 8px;
	padding-left: 8px;
	margin-top: 5px;
}
.UI_main .UI_main_content .Main_c .Main_c_top_r {
	background: url(../Images/top_r_bg_cc.png) right 0 no-repeat;
	height: 8px;
	padding-right: 8px;
}
.UI_main .UI_main_content .Main_c .Main_c_top_m {
	background: #9ac5f0;
	height: 8px;
}
.UI_main .UI_main_content .bg_bottom_l {
	background: url(../Images/main_bg_l_bottom.png) no-repeat;
	padding-left: 21px;
	height: 22px;
}
.UI_main .UI_main_content .bg_bottom_r {
	background: url(../Images/main_bg_r_bottom.png) right 0 no-repeat;
	padding-right: 21px;
	height: 22px;
}
.UI_main .UI_main_content .bg_bottom_m {
	background: url(../Images/main_bg_m_bottom.png) repeat-x;
	height: 22px;
}
.Enegy_c_main {
	background: #f6f6f6;
	height: 100%;
}
.com .Enegy_c_main {
	height: 100%;
	background: #f6f6f6;
}
.search_page {
	background: #fff;
	padding: 10px 0;
	margin: 10px 0;
	border-bottom: 1px solid #d3d7da;
	border-top: 1px solid #d3d7da;

}
#search-panel .manager_page {
	background: #fff;
	padding: 10px 0;
	margin: 10px 0;
	border-bottom: 1px solid #d3d7da;
	border-top: 1px solid #d3d7da;
}
.search_page .search_condition {
	padding-bottom: 0px;
}
.input2 {
	line-height: 26px;
	color: #fff;
	height: 26px;
	padding: 0;
	font-size: 12px;
}
.input3 {
	line-height: 27px;
	color: #4b4b4b;
	height: 27px;
	padding: 0;
	font-size: 12px;
	width: 130px;
	border: 1px solid #b0b0b0;
	padding-left: 4px;
}
.input3.w50 {
	width: 50px;
}
.input3.w30 {
	width: 20px; text-align:center; margin:0 2px;
}
.input3.w80 {
	width: 80px;
}
.input3.w30 {
	width: 30px;
	background: #f5f3f3;
}
.input3.warea {
	width: 509px;
	height: 60px;
	line-height: 20px;
}
.input3.warea1 {
	width: 409px;
	height: 26px;
	line-height: 26px;
}
.input4 {
	color: #4b4b4b;
	height: 27px;
	padding: 0;
	text-align: center;
	font-size: 14px;
	width: 130px;
	border: 1px solid #b0b0b0;
	padding-left: 4px;
	background:#f5f3f3;
}
.scrool_num {
	width: 30px;
	height: 26px;
	background: url(../Images/scrollnum_btn.png) no-repeat;
}
.scrool_num .plus_b {
	width: 100%;
	height: 8px;
	display: block;
	margin-top: 2px;
	cursor: pointer;
}
.scrool_num .minus_b {
	width: 100%;
	height: 8px;
	display: block;
	margin-top: 5px;
	cursor: pointer;
}
.sel_list {
	display: block;
	font-size: 14px;
	color: #646c1c;
	height: 26px;
}
.header_sel_list li {
	font-size: 14px;
	color: #646c1c;
}
.sel_list2 {
	width: 100px;
}
.input2.blue300 {
	background: #437EC0;
	padding-left: 5px;
	width: 300px;
}
.input2.blue_p95 {
	padding-left: 5px;
	width: 93%;
}
.input_point {
	background: #437EC0;
	line-height: 16px;
	color: #000;
	font-weight: 100;
	border: 0;
	font-size: 12px;
	width: 350px;
	height: 150px;
	overflow: auto;
}
.input_point li {
	position: relative;
	float: left;
	padding: 2px;
	padding-right: 11px;
	float: left;
	margin: 3px;
	background: #b8dcff;
	-moz-border-radius: 3px;      /* Gecko browsers */
	-webkit-border-radius: 3px;   /* Webkit browsers */
	border-radius: 3px;            /* W3C syntax */
}
.input_point li .close_icon {
	position: absolute;
	right: 3px;
	top: 6px;
	cursor: pointer;
}
.textarea2 {
	line-height: 26px;
	color: #fff;
	padding: 0;
	border: 0;
	font-size: 12px;
	width: 300px;
	height: 26px;
	background: #437EC0;
}
.dropdown {
	float: left;
	margin: 0;
	padding: 0;
	display: block;
	width: 200px;
	position: relative;
}
.dropdown3 {
	width: 81px;
}
.dropdown4 {
	width: 136px;
}
.dropdown5 {
	width: 51px;
	text-align: left;
}
.dropdown6 {
	width: 101px;
}
.dropdown7 {
	width: 166px;
}
.dropdown8 {
	width: 62px;
	text-align: left;
}
.dropdown4 .tag_options {
	width: 134px;
	height: 120px;
	overflow: auto;
	border: 1px solid #fa9800;
	background: #fff;
}
.dropdown4.h_auto .tag_options {
	width: 134px;
	height: auto;
	overflow: auto;
}
.dropdown3 .tag_options {
	width: 79px;
	height: 100px;
	overflow: auto;
	border: 1px solid #fa9800;
	background: #fff;
}
.dropdown8 .tag_options {
	width: 60px;
	height: 100px;
	overflow: auto;
	border: 1px solid #fa9800;
	background: #fff;
}
.dropdown2 .tag_options {
	width: 114px;
	height: 100px;
	overflow: auto;
}
.dropdown5 .tag_options {
	width: 64px;
	height: 100px;
	overflow: auto;
}
.dropdown6 .tag_options {
	width: 99px;
	height: 125px;
	overflow: auto;
	border: 1px solid #fa9800;
	background: #fff;
}
.dropdown.hauto .tag_options {
	height: auto;
}
.dropdown7 .tag_options {
	width: 164px;
	height: 100px;
	overflow: auto;
	border: 1px solid #fa9800;
	background: #fff;
}
.dropdown.h70 .tag_options {
	width: 134px;
	height: 70px;
	overflow: auto;
}
.m0180 {
	margin: 0 auto;
	width: 180px;
}
.m084 {
	margin: 0 auto;
	width: 84px;
}
.time_text {
	margin-left: 2px;
	margin-right: 5px;
	color: #093D6D;
	font-weight: bold;
}
.mlr_1{ margin:0 1px;}
.time_text.ml_max {
	margin-left: 20px;
}
.tag_select {
	background: #f5f3f3 url(../Images/select_btn.png) right 0 no-repeat;
	height: 27px;
	line-height: 27px;
	color: #4b4b4b;
	border: 1px solid #b0b0b0;
	font-size: 12px;
	font-weight: normal;
	text-align: left;
	padding-left: 0;
	text-indent: 5px;
	border-right: 0;
	padding-right: 30px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	cursor: pointer;
}
.dropdown.disable .tag_select{background: #f5f3f3 url(../Images/select_btn_grey.png) right 0 no-repeat; color:#CCC;
	border-color:#ccc;}
.tag_select_open {
	border: 1px solid #fa9800;
	border-right: 0;
	background: #f5f3f3 url(../Images/select_open_btn.png) right 0 no-repeat;
}
.pl_5 .tag_select {
	padding-left: 5px;
}
.dropdown3.pl_5 .tag_options {
	width: 75px;
}
.text-Over .tag_select {
	width: 105px;
	padding-right: 29px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.tag_options {
	width: 214px;
}
.tag_options li {
	font-size: 12px;
	background: #fff6e7;
	color: #4b4b4b;
	height: 25px;
	line-height: 25px;
	text-decoration: none;
	padding-left: 5px;
	font-weight: normal;
	overflow: hidden;
	white-space: nowrap;
}
.tag_options li.open_hover {
	background: #fcc46c;
	color: #fff;
	font-weight: normal;
	font-size: 12px;
}
.tag_options li.open_selected {
	background: #fa9800;
	color: #fff;
	font-size: 12px;
}
.tag_options {
	position: absolute;
	margin: 0;
	list-style: none;
	padding: 0 0 1px;
	margin: 0;
	overflow: hidden;
	overflow-y: auto;
	font-size: 12px;
	cursor: pointer;
	z-index: 99999;
	top: 29px;
}
.bg_white .Wdate {
	border: 0;
	width: 120px;
}
/*.bg_white .tag_select{ background:url(../Images/select_btn.png) right 0 no-repeat;}*/
.tab_form {
	background: #bddcfa;
	width: 98%;
	margin: 10px auto;
	padding: 5px 0;
	position: relative;
}
.tab_form h4 {
	height: 23px;
	line-height: 23px;
	border-bottom: 1px solid #93c6ff;
	font-family: Simsun;
	font-size: 12px;
	font-weight: bold;
	color: #3d6389;
	padding-left: 10px;
}
.tab_form .tf_lu, .tab_form .tf_ru, .tab_form .tf_ld, .tab_form .tf_rd {
	display: block;
	width: 5px;
	height: 5px;
	position: absolute;
	font-size: 0;
}
.tab_form .tf_lu {
	background: url(../Images/tab_form_lu.gif) 0 0 no-repeat;
	left: 0;
	top: 0;
}
.tab_form .tf_ru {
	background: url(../Images/tab_form_ru.gif) 0 0 no-repeat;
	right: 0;
	top: 0;
}
.tab_form .tf_ld {
	background: url(../Images/tab_form_ld.gif) 0 0 no-repeat;
	left: 0;
	bottom: 0;
}
.tab_form .tf_rd {
	background: url(../Images/tab_form_rd.gif) 0 0 no-repeat;
	right: 0;
	bottom: 0;
}
.tab_form .tab_info {
	font-size: 12px;
	border-collapse: collapse;
	border: 0px solid #e1e1e1;
	width: 100%;
}
.tab_form .tab_info thead th {
	height: 40px;
	line-height: 20px;
	text-align: left;
	font-size: 12px;
	font-family: Simsun;
	color: #4a4a4a;
	padding-left: 5px;
}
.tab_form .tab_info tbody td {
	height: 30px;
	line-height: 30px;
	text-align: left;
	border: 0px solid #e1e1e1;
	font-size: 12px;
	font-family: Simsun;
	padding: 0 3px;
}
.tab_form .tab_info tbody input, .tab_form .tab_info tbody select {
	width: 99%;
	height: 21px;
	line-height: 21px;
	color: #ccs7df;
	font-size: 12px;
	padding-left: 2px;
	background: url(../Images/m_sel_inp_bg.gif) 0 0 repeat-x;
	border: 1px solid #8cc2ff;
}
.search_condition table, .manager_page table {
	width: auto;
	margin-left: 15px;
}
.search_condition table td, .manager_page table td {
	font-size: 14px;
	color: #4b4b4b;
	height: 31px;
	text-align: left;
	font-family: Microsoft YaHei;
	padding-left: 5px;
}
.manager_page.h30 table td {
	height: 31px;
}
.search_condition table td .normal, .manager_page table td .normal {
	font-size: 12px;
	font-weight: 100;
}
.search_condition table td .font_narmal, .manager_page table td .font_narmal {
	font-weight: 100;
	font-size: 12px;
	color: #000;
}
/*.search_condition table td.ener_r,.manager_page table td.ener_r{padding-right:10px;}*/
.search_condition table td.ener_r30, .manager_page table td.ener_r30 {
	padding-right: 30px;
}
.ener_r_box {
	text-align: right;
	padding-right: 10px;
	width: 80px;
	line-height: 26px;
}
.search_condition .e_zt, .manager_page .e_zt {
	color: #6e8dae;
	font-size: 12px;
	font-weight: normal;
	padding: 5px 0 0 35px;
	line-height: 24px;
	float: left;
	font-style: normal;
}
.search_condition .e_zt2, .manager_page .e_zt2 {
	color: #093D6D;
	font-weight: bold;
	padding: 0 10px 0 0;
	display: block;
	font-size: 14px;
}
.search_condition table td.tc_orange, .manager_page table td.tc_orange {
	color: #f06e09;
}
/*产品管理页面耗能种类样式*/
.input3.kinds_text {
	width: auto;
	min-width: 80px;
}
.down_drop_btn {
	width: 30px;
	height: 26px;
	background: url(../Images/select_btn.png) no-repeat;
}
.down_drop_btn.selected {
	width: 30px;
	height: 26px;
	background: url(../Images/select_btn_c.png) no-repeat;
}
.energy_kinds {
	width: 100%;
	border: 1px solid #437EC0;
	max-height: 80px;
	overflow: auto;
	position: absolute;
	top: 26px;
	left: 0;
	background: #BDDCFA;
	display: none;
}
.energy_kinds li {
	width: 100%;
	height: 15px;
	padding: 2px 0;
	color: #000;
	text-align: left;
	font-size: 12px;
	font-weight: 100;
}
.energy_kinds li .select_box {
	margin-right: 5px;
	margin-left: 5px;
}
/*弹出细框编辑table*/
.manager_page {
	text-align: left;
	background: #fff;
}
.manager_page .thin_table {
	background: #093D6D;
}
.manager_page .thin_table th {
	background: url(../Images/table_list_thgrey.png) repeat-x;
	height: 26px;
	line-height: 26px;
}
.manager_page .thin_table td {
	background: #bddcfa;
	font-size: 12px;
	font-weight: 100;
	height: 24px;
	line-height: 24px;
	text-align: center;
}
.manager_page .thin_table td .input3 {
	height: 20px;
	line-height: 20px;
}
.dotted {
	background: url(../Images/dotted_line.jpg) repeat-x 0 bottom;
	width: 100%;
	height: 5px;
	margin-bottom: 5px;
}
.dotted2 {
	background: url(../Images/dotted_line2.png) repeat-x 0 bottom;
	width: 100%;
	height: 5px;
	margin: 5px 0;
}
.dotted2.mt2 {
	margin-top: 2px;
}
.dotted2.mt0b2 {
	margin-top: 0px;
	margin-bottom: 2px;
}
.button3 {
	background: url(../Images/info_l.png) no-repeat left 0;
	float: left;
	display: block;
	height: 31px;
	display: block;
	padding-left: 21px;
	margin-left: 5px;
}
.button3.export_excel {
	background: url(../Images/info_l2.png) no-repeat left 0;
}
.button4 {
	padding-left: 5px;
}
.button3.R {
	float: right
}
.button3 input {
	background: url(../Images/info_r.png) no-repeat right 0;
	float: left;
	display: block;
	height: 31px;
	line-height: 26px;
	color: #fff;
	padding: 3px 8px 3px 3px;
	border: 0;
	font-size: 15px;
	margin-right: 20px;
	display: inline;
	margin: 0;
	cursor: pointer;
	font-family: "微软雅黑"
}
.button3.f_aril input {
	font-family: Arial, Helvetica, sans-serif;
}
@-moz-document url-prefix() {
.button3 input {
padding-top:0;
}
}
.btn_input {
	padding: 0px 5px 0px 25px;
	height: 29px;
	line-height: 29px;
	color: #fff;
	border: 0;
	font-size: 15px;
	cursor: pointer;
	font-family: "微软雅黑";
	margin-top: 1px;
}
.btn_input.mt5 {
	margin-top: 5px;
}
.btn_search {
	background: url(../Images/search.png) no-repeat left 0;
	width: 66px;
}
.btn_search:hover {
	background: url(../Images/search_h.png) no-repeat left 0;
}
.btn_search2 {
	background: url(../Images/search2.png) no-repeat left 0;
	width: 99px;
}
.btn_search2:hover {
	background: url(../Images/search2_h.png) no-repeat left 0;
}
.btn_export {
	background: url(../Images/export.png) no-repeat left 0;
	width: 99px;
}
.btn_export:hover {
	background: url(../Images/export_h.png) no-repeat left 0;
	width: 99px;
}
.btn_analysis {
	background: url(../Images/analysis.png) no-repeat left 0;
	width: 99px;
}
.btn_analysis:hover {
	background: url(../Images/analysis_h.png) no-repeat left 0;
	width: 110px;
}
.btn_long {
	background: url(../Images/btn-long.png) no-repeat left 0;
	width: 121px;
	padding: 0;
}
.btn_long:hover {
	background: url(../Images/btn-long-on.png) no-repeat left 0;
	width: 121px;
}
.btn_99 {
	background: url(../Images/btn-99.png) no-repeat left 0;
	width: 99px;
	padding: 0;
}
.btn_99:hover {
	background: url(../Images/btn-99-on.png) no-repeat left 0;
	width: 99px;
}
.btn_md {
	background: url(../Images/md.png) no-repeat left 0;
	width: 99px;
}
.btn_md:hover {
	background: url(../Images/md_h.png) no-repeat left 0;
	width: 99px;
}
.btn_declare {
	background: url(../Images/declare.png) no-repeat left 0;
	width: 66px;
}
.btn_declare:hover {
	background: url(../Images/declare_h.png) no-repeat left 0;
	width: 66px;
}
.btn_save {
	padding: 0;
	background: url(../Images/save.png) no-repeat left 0;
	width: 66px;
}
.btn_save:hover {
	padding: 0;
	background: url(../Images/save_h.png) no-repeat left 0;
	width: 66px;
}
.btn_tuopu {
	background: url(../Images/tuopu.png) no-repeat left 0;
	width: 124px;
	float: right;
	margin-right: 52px;
	margin-top: 3px;
}
.button4 input {
	padding-right: 5px;
}
.manager_page .Data_List {
	width: 100%;
	text-align: center;
}
.Box_list {
	overflow: auto;
	margin-top: 5px;
}
.Box_list .table_l {
	overflow: auto;
	height: 280px;
	margin-top: 5px;
}
.Data_List {
	width: 100%;
	border: 0;
	border-spacing: 0px;
	font-family: Microsoft YaHei;
}
.Data_List th {
	height: 26px;
	line-height: 26px;
	background: #b0caea;
	font-size: 14px;
}
.Data_List td {
	text-align: center;
	line-height: 26px;
	font-size: 12px;
}
.border_table th {
	border-right: 1px solid #c3c3c3;
	border-bottom: 1px solid #c3c3c3;
	border-top: 1px solid #c3c3c3;
	color: #292e33;
}
.border_table th a {
	color: #292e33;
	text-decoration: none;
}
.border_table td {
	border-bottom: 1px solid #c3c3c3;
	border-right: 1px solid #c3c3c3;
}
.Data_List thead td {
	font-weight: bold;
	line-height: 20px;
}
.Data_List thead.bg33 td {
	background: #b0caea;
	color: #fff;
	font-family: Microsoft YaHei;
	font-weight: 100;
	border-right: 1px solid #e0d8cf;
	border-bottom: 1px solid #e0d8cf;
}
.Data_List.th_border th {
}
.Data_List td .pro_name {
	line-height: 20px;
}
.Data_List td .pro_img {
	width: 50px;
}
.manager_page .Data_List td {
	text-align: center;
	height: 30px;
	line-height: 30px;
	border-top: 1px solid #f4eee7;
	border-left: 1px solid #f4eee7;
	color: #000;
	font-weight: normal;
	border-bottom: 1px solid #e0d8cf;
	border-right: 1px solid #e0d8cf;
}
.manager_page .Data_List th {
	color: #fff;
	font-weight: bold;
}
.Data_List .alL {
	text-align: left;
	padding-left: 5px;
}
.Data_List .line-even td {
	background: #E2EAFC;
}
.Data_List thead.td_font-normal td {
	font-weight: 100;
}
.manager_page fieldset {
	margin: 0 5px;
	border: 1px solid #84b0dc;
	width: 97%;
	margin: 0 auto;
	font-size: 14px;
	color: #093D6D;
	font-weight: bold;
	padding: 5px;
	text-align: left; height:auto;
}
.manager_page fieldset legend {
	padding: 0 5px;
	color: #093D6D;
}
.manager_page fieldset fieldset {
	margin-bottom: 5px;
}
.manager_page fieldset ul {
	margin-left: 11px;
}
.manager_page fieldset li {
	float: left;
	font-size: 12px;
	margin-left: 15px;
	margin-top: 5px;
}
.manager_page fieldset.m0 ul,.manager_page fieldset.m0 li{ margin:0; clear:both; float:none; text-align:left;}
.manager_page fieldset.m0 .team_table td{ text-align:center; font-weight:normal;}
.manager_page fieldset.nostyle ul {
	margin: 0;
	text-align: left;
}
.manager_page fieldset.nostyle ul li {
	float: inherit;
	margin: 0;
}
.choice_item {
	float: left;
	margin-left: 210px;
	font-size: 14px;
	color: #093D6D;
	line-height: 15px;
	margin-top: 20px;
}
.page_button_top {
	width: 100%;
	height: 464px;
	overflow: auto;
}
.page_button_top .data_dictionary_table {
	width: 100%;
	height: 464px;
}
.tab_list_ul {
	height: 34px;
	font-size: 16px;
	padding: 0;
	padding-bottom: 0;
	background: #f1f1f1;
	border: 1px solid #d3d7da;
	border-left: 0;
	border-right: 0;
}
.tab_list_ul li {
	line-height: 34px;
	height: 34px;
	color: #093D6D;
	float: left;
	border-right: 1px solid #d3d7da;
	padding: 0 10px;
}
.tab_list_ul li .tab_bg {
	display: block;
	float: left;
	padding-left: 16px;
}
.tab_list_ul li .tab_bg.icon_12 {
	background: url(../Images/icon_ele.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_13 {
	background: url(../Images/icon_curve.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_201 {
	background: url(../Images/icon_201.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_202 {
	background: url(../Images/icon_202.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_203 {
	background: url(../Images/icon_203.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_204 {
	background: url(../Images/icon_204.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_205 {
	background: url(../Images/icon_event.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_206 {
	background: url(../Images/icon_206.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_207 {
	background: url(../Images/icon_table.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_211 {
	background: url(../Images/icon_clock.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_214 {
	background: url(../Images/icon_214.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_124 {
	background: url(../Images/icon_124.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_125 {
	background: url(../Images/icon_125.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_126 {
	background: url(../Images/icon_126.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_127 {
	background: url(../Images/icon_127.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_128 {
	background: url(../Images/icon_128.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_40 {
	background: url(../Images/icon_xb.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_41 {
	background: url(../Images/icon_sz.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_29 {
	background: url(../Images/icon_fx.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_42 {
	background: url(../Images/icon_js.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_43 {
	background: url(../Images/icon_md.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_11 {
	background: url(../Images/icon_pm.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_21 {
	background: url(../Images/icon_annulus.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_25 {
	background: url(../Images/icon_household.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_102 {
	background: url(../Images/icon_fzl.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_103 {
	background: url(../Images/icon_bph.png) left center no-repeat;
}
.tab_list_ul li .tab_bg.icon_104 {
	background: url(../Images/icon_lineloss.png) left center no-repeat;
}
.tab_list_ul li .tab_bg a {
	display: block;
	padding-right: 5px;
	padding-left: 5px;
	font-family: "Microsoft YaHei", "helvetica neue", arial, sans-serif;
	height: 34px;
	color: #7b7b7b;
	font-size: 14px;
	font-weight: bold;
	text-decoration: none;
}
.tab_list_ul li.on {
	background: url(../Images/nav_bg_line.png) 0 top repeat-x;
}
.tab_search_list {
	height: 100%;
}
.tab_search_chat {
	overflow-x: hidden;
}
.position_a {
	position: absolute;
	left: 0;
	top: 0;
}
.tab_search_chat .chat_box {
	width: 100%;
	height: 240px;
}
.chat_box_100_p {
	width: 100%;
	height: 100%;
}
.Data_List.voltage_box {
	width: 80%;
	margin: 0 auto;
	color: #093D6D;
}
.Data_List.voltage_box td {
	padding: 0 5px;
}
.tc093D6D {
	color: #093D6D;
}
.tab_search_chat.m0 {
	margin: 0;
	margin-top: 10px;
}
.tab_search_chat.tab0 {
	margin: 0;
	margin-top: 10px;
}
/*.tab_search_chat.subitem2{ height:417px;}*/

/*能源分析*/
.energy_classify_l {
	border: 1px solid #d3d7da;
	background: url(../Images/chat_repeat_bg_green.png) 0 bottom repeat-x;
	padding-bottom: 5px;
	background: #fff;
}
.title_h {
	background: #f1f1f1;
	font-size: 14px;
	font-weight: 100;
	color: #4b4b4b;
	text-align: left;
	text-indent: 10px;
	height: 40px;
	line-height: 40px;
	border-bottom: 1px solid #d3d7da;
	font-family: Microsoft YaHei;
	margin-bottom: 5px;
}
.energy_classify_l .curve_l_top {
	height: 245px;
	margin: 0 auto;
}
.energy_classify_l .curve_l_top.w792 {
	width: 792px;
}
.curve_l_top.subitem2 {
	height: 279px;
}
.curve_l_top.subitem3 {
	height: 350px;
}
.energy_classify_l .curve_l_top.ranking {
	width: 692px;
	height: 410px;
	margin: 0 auto;
}
.title_main {
	font-size: 16px;
	height: 30px;
	line-height: 30px;
	font-family: Microsoft YaHei;
	color: #093D6D;
}
/*.energy_classify_l .curve_l_top .chat_box{ width:100%; height:200px; margin-top:5px;}*/
.energy_classify_l .curve_l_top.subitem .chat_box {
	width: 100%;
	height: 250px;
	margin-top: 5px;
}
.chat_box_h100 {
	width: 95%;
	margin: 0 auto;
	height: 100px;
}
.energy_classify_l .curve_l_top.subitem2 .chat_box {
	width: 100%;
	height: 250px;
	margin-top: 5px;
}
.energy_classify_l .curve_l_top.ranking .chat_box {
	width: 100%;
	height: 379px;
	margin-top: 5px;
}
.energy_classify_l .curve_l_bottom {
	width: 692px;
	height: 125px;
	background: #fff;
	margin: 0 auto;
}
.energy_classify_l .curve_l_bottom .chat_box {
	width: 100%;
	height: 125px;
}
.energy_classify_r {
	width: 305px;
	border: 1px solid #d3d7da;
	background: url(../Images/chat_repeat_bg_green.png) repeat-x;
	background: #fff;
}
.energy_classify_r.w195 {
	width: 195px;
}
.energy_classify_r.pr10 {
	padding-right: 10px;
}
.energy_classify_r .curve_r_top {
	width: 100%;
	height: 260px;
	margin: 0 auto;
	margin: 0 auto;
}
.energy_classify_r .curve_r_top.subitem {
	height: 181px;
	width: 285px;
	margin-bottom: 5px;
}
.energy_classify_r .curve_r_top.subitem2 {
	height: 180px;
	width: 285px;
}
.energy_classify_r .curve_r_top .title_main {
	height: 16px;
	line-height: 16px;
	font-size: 12px;
	color: #000;
}
.energy_classify_r .curve_r_top .chat_box_r {
	width: 285px;
	height: 125px;
	margin: 0 auto;
	margin-bottom: 5px;
}
.energy_classify_r .curve_r_top.subitem .chat_box_r {
	width: 247px;
	height: 150px;
	margin: 0 auto;
}
.ranking_data_right_t {
	height: 414px;
}
.ranking_data_right_t .ranking_data_right_b {
	height: 409px;
	padding-bottom: 5px;
}
.ranking_data_right {
	height: 409px;
}
.curve_r_bottom.subitem {
	width: 285px;
	height: 110px;
	margin: 10px auto;
	position: relative;
}
.curve_r_bottom.subitem .subitem_datalist {
	height: 100%;
	overflow: auto;
	padding: 0 25px 0 0px;
}
.curve_r_bottom.subitem .subitem_datalist li {
	height: 21px;
	width: 100%;
	float: left;
	line-height: 21px;
	text-align: left;
	margin-bottom: 5px;
}
.curve_r_bottom.subitem .subitem_datalist li .item_n {
	display: block;
	float: left;
	width: 70px;
	line-height: 25px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	text-align: right;
	margin-right: 8px;
}
.curve_r_bottom.subitem .subitem_datalist li .item_money {
	display: block;
	float: left;
	background-color: #dddddd;
	width: 90px;
	color: #000;
	height: 21px;
	font-style: normal;
}
.curve_r_bottom.subitem .subitem_datalist li .item_moner_text {
	height: 0px;
}
.curve_r_bottom.subitem .subitem_datalist li .item_money .item_unit {
	color: #000;
	font-size: 11px;
	font-family: "宋体";
	font-style: normal;
	font-weight: normal;
}
.curve_r_bottom.subitem .subitem_datalist li .item_money .percent_bg {
	height: 21px;
	float: left;
	width: 20%;
	background-color: #00a0e9;
}
.curve_r_bottom.subitem .subitem_datalist li .item_percent {
	margin-left: 2px;
	display: block;
	float: left;
	font-size: 8px;
	width: auto;
	text-align: center;
	font-family: Arial, Helvetica, sans-serif;
	height: 21px;
	background: url(../Images/icon.png) -82px -85px no-repeat;
	color: #fff;
	padding-right: 17px;
}
.curve_r_bottom.subitem .subitem_datalist li .item_percent b {
	float: left;
	width: 48px;
	padding-right: 3px;
	text-align: right;
	font-size: 10px;
	letter-spacing: -0.5px;
}
.curve_r_bottom.subitem .more_list {
	width: 16px;
	height: 16px;
	background: url(../Images/item_more.png) no-repeat;
	cursor: pointer;
	position: absolute;
	right: 22px;
	top: 75px;
}
.curve_r_bottom.subitem .more_list.on {
	background: url(../Images/item_less.png) no-repeat;
}
.energy_classify_r .curve_r_bottom.long_mid {
	margin-top: 8px;
	height: 275px
}
.energy_classify_r .curve_r_bottom.long_mid .more_list {
	top: 230px;
}
.data_bottom {
	margin: 5px;
	border: 1px solid #d3d7da;
	overflow: hidden;
	height: 165px;
	padding: 10px;
	background: #fff;
}
.subitem_data_bottom li {
	width: 33%;
	float: left;
	height: 100%;
	padding-right: 1px;
}
.subitem_data_bottom.ranking {
	background: none;
}
.subitem_data_bottom.ranking li {
	width: 100%;
	margin: 0 auto;
	background: none;
	padding: 0;
	height: auto;
}
.subitem_data_bottom.contrast li {
	width: 24.5%;
	height: 165px;
	border-right: 1px solid #c6c9ca;
}
.subitem_data_bottom li .data_detail {
	padding: 0 10px;
	margin: 0 auto;
	width: 200px;
	overflow: hidden;
}
.subitem_data_bottom li .energy_data_title {
	color: #39474e;
	text-align: left;
	font-size: 14px;
	margin-top: 5px;
	padding: 2px 5px;
	font-family: "微软雅黑";
	background-color: #f1f1f1;
}
/*.subitem_data_bottom li .energy_data_title.blue{background:url(../Images/title_bg_blue.png) repeat-y;}
.subitem_data_bottom li .energy_data_title.green{background:url(../Images/title_bg_green.png) repeat-y;}
.subitem_data_bottom li .energy_data_title.purple{background:url(../Images/title_bg_zi.png) repeat-y;}
.subitem_data_bottom li .energy_data_title.yellow{background:url(../Images/title_bg_yellow.png) repeat-y;}*/
.subitem_data_bottom.ranking li .energy_data_title {
	margin-top: 0;
	line-height: 18px;
	height: 18px;
}
.subitem_data_bottom li .ele_energy_today {
	width: 86px;
	height: 75px;
	background: url(../Images/icon.png) no-repeat 0px -107px;
	font-weight: bold;
	font-size: 14px;
	color: #2ed900;
	float: left;
	margin-top: 15px;
	padding-top: 20px;
}
.subitem_data_bottom li .ele_energy_today .energy_data_num {
	line-height: 22px;
	font-family: Microsoft YaHei;
	white-space: nowrap;
	display: block;
	margin: 0 auto;
	overflow: hidden;
	text-overflow: ellipsis;
	width: auto;
	height: 22px;
	font-size: 16px;
	font-weight: 100;
}
.subitem_data_bottom li .period_lastyear {
	float: left;
	padding-top: 5px;
}
.subitem_data_bottom li .period_lastyear b {
	color: #666;
}
.period_lastyear .bg1 {
	background: url(../Images/icon.png) no-repeat 0px -30px;
	width: 49px;
	height: 39px;
	margin-top: 5px;
	padding-top: 10px;
}
.period_lastyear .bg2 {
	background: url(../Images/icon.png) no-repeat -49px -30px;
	width: 49px;
	height: 39px;
	margin-top: 5px;
	padding-top: 10px;
}
.period_lastyear .bg3 {
	background: url(../Images/icon.png) no-repeat -98px -30px;
	width: 49px;
	height: 39px;
	margin-top: 5px;
	padding-top: 10px;
}
.ele_energy_today .unit_num {
	font-weight: 100;
	font-size: 12px;
	clear: both;
	float: none;
	width: auto;
	display: inherit;
	max-width: inherit;
	color: #4d5864;
}
.period_num {
	width: 100%;
	padding-top: 18px;
	font-family: Microsoft YaHei;
	font-size: 14px;
	margin-top: 5px;
	margin-left: 15px;
}
.period_num.up {
	background: url(../Images/icon.png) -41px 0px no-repeat;
	width: 21px;
	height: 7px;
}
.period_num.down {
	background: url(../Images/icon.png) -64px 0px no-repeat;
	width: 21px;
	height: 7px;
}
.period_num.period_num_h {
	background-position: 50px center;
	padding: 0;
	text-indent: 35px;
	line-height: 30px;
	margin: 0;
}
.period_num .num {
	font-size: 20px;
	color: #666;
	font-weight: 100;
}
.total_num_24 {
	font-size: 24px;
	line-height: 20px;
	font-family: Microsoft YaHei;
	margin: 0 5px;
}
.data_table_subitem {
	border: 1px dashed #d3d7da;
	border-top: 0px;
	width: 100%;
	padding-left: 10px;
	padding-right: 10px;
	height: 135px;
}
.energy_top3 {
	width: 100%;
	margin: 0 auto;
	margin-top: 1px;
	line-height: 20px;
	background: #fff;
}
.energy_top3.w100 {
	width: 100%;
}
.energy_list {
	width: 95%;
	margin: 0 auto;
	line-height: 19px;
}
.subitem_data_bottom.ranking .energy_top3 {
	line-height: 18px;
	width: 90%;
	margin: 0 auto;
}
.energy_top3 th {
	line-height: 24px;
	background: #accbe0;
	font-family: Microsoft YaHei;
	font-weight: 100;
}
.energy_top3 td {
	border-bottom: 1px dotted #d3d3d3;
	border-right: 1px dotted #d3d3d3;
	line-height: 21px;
	background: #f4f4f4;
}
.subitem_data_bottom.contrast .energy_top3 {
	width: 90%;
	margin-top: 25px;
}
.subitem_data_bottom.contrast .energy_top3.m0 {
	margin-top: 3px;
}
.forecast .w_small {
	width: 30%;
}
.forecast .w_large {
	width: 38%;
}
.forecast .w_middle {
	width: 28%;
}
.forecast li {
	width: auto;
}
.data_table_subitem p {
	overflow: hidden;
	line-height: 24px;
}
.data_table_subitem p em {
	font-style: normal;
	font-size: 21px;
	font-family: Microsoft YaHei;
}
.frame_total_classi {
	width: 285px;
	overflow: hidden;
	margin: 0 auto;
	background: #efefef;
	height: 110px;
	line-height: 24px;
}
.frame_total_classi.h80 {
	height: 80px;
	width: 260px;
	margin: 0 auto;
	padding: 0 12px;
}
.frame_total_classi p {
	overflow: hidden;
	line-height: 24px;
}
.money_name {
	width: 60px;
	text-align: right;
}
.cost_num {
	text-align: center;
	width: 70px;
	font-size: 18px;
	font-family: Microsoft YaHei;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.frame_total_t {
	width: 247px;
	background: url(../Images/totla_right_bg_top.png) no-repeat;
	padding-top: 5px;
	margin: 0 auto;
}
.frame_total_b {
	width: 100%;
	background: url(../Images/totla_right_bg_bottom.png) 0 bottom no-repeat;
	padding-bottom: 5px;
}
.total_main {
	height: 85px;
	background: url(../Images/totla_right_bg_mid.png) center center repeat-x;
	padding: 0 5px;
}
.total_main .system_list {
	text-align: left;
}
em {
	font-style: normal;
}
.total_main .system_list p {
	width: 100%;
	overflow: hidden;
	line-height: 22px;
}
.total_main .system_list .money_name {
	width: 60px;
}
.energy_data_list .title_l_h {
	font-size: 16px;
	color: #093D6D;
	text-align: center;
	font-weight: bold;
	height: 33px;
	line-height: 33px;
	font-family: Microsoft YaHei;
}
.button3 .detail_button {
	background: url(../Images/info_r_open.png) right 0 no-repeat;
	padding-right: 25px;
}
.button3 .detail_button.on {
	background: url(../Images/info_r_close.png) right 0 no-repeat;
	padding-right: 25px;
}
.td_list_bg {
	height: 16px;
	text-align: left;
	text-indent: 5px;
	white-space: nowrap;
	background: url(../Images/table_list_th.png) center center repeat-x;
}
/*.td_bg01{ background:url(../Images/table_list_th.png) center center repeat-x;  }
.td_bg02{ background:url(../Images/table_list_th2.png) center center repeat-x; }
.td_bg03{ background:url(../Images/table_list_th3.png) center center repeat-x; }*/
.td_bggrey {
	background: url(../Images/table_list_thgrey.png) repeat-x 0 0;
}
/*数据频率*/
.date_list {
	height: 27px;
	line-height: 27px;
	margin-right: 5px;
	border: 1px solid #cae9fb;
	border-right: 0;
}
.date_list li {
	height: 27px;
	width: 27px;
	background: #f0f7fb;
	float: left;
	cursor: pointer;
	border-right: 1px solid #cae9fb
}
.date_list li.on {
	background: #16a0e8;
	color: #fff;
}
/*分户统计*/

.sort_icon .sort_item {
	display: block;
	float: left;
	width: 20px;
	height: 20px;
	cursor: pointer;
	margin-right: 5px;
}
.sort_icon .sort_item.electric {
	background: url(../Images/sort_icon.png) no-repeat 0 0;
}
.sort_icon .sort_item.water {
	background: url(../Images/sort_icon.png) no-repeat -89px 0;
}
.sort_icon .sort_item.gas {
	background: url(../Images/sort_icon.png) no-repeat -59px 0;
}
.sort_icon .sort_item.shine {
	background: url(../Images/sort_icon.png) no-repeat -30px 0;
}
.sort_icon .sort_item.electric.on {
	background: url(../Images/sort_icon.png) no-repeat 0 bottom;
}
.sort_icon .sort_item.water.on {
	background: url(../Images/sort_icon.png) no-repeat -89px bottom;
}
.sort_icon .sort_item.gas.on {
	background: url(../Images/sort_icon.png) no-repeat -59px bottom;
}
.sort_icon .sort_item.shine.on {
	background: url(../Images/sort_icon.png) no-repeat -30px bottom;
}
.sort_up {
	background: url(../Images/sort_up.png) 0 center no-repeat;
	padding-left: 10px;
}
.sort_down {
	background: url(../Images/sort_down.png) 0 center no-repeat;
	padding-left: 10px;
}
/*日周月年*/
.household_box {
	height: 329px;
	overflow: auto;
}
.household_pic {
	width: 120px;
	margin: 0 auto;
	height: 92px;
	border: 1px solid #093D6D;
	padding: 5px 5px 5.5px 5px;
	text-align: center;
	line-height: 20px;
	font-size: 14px;
	font-weight: bold;
	color: #093D6D;
}
.household_pic .img_detail {
	width: 120px;
	height: 72px;
	border: 1px solid #093D6D;
}
.sort_table {
	text-align: left;
	line-height: 16px;
}
.sort_table td {
	padding: 2px;
}
.sort_table tbody td {
	padding: 2px;
	background: #bddcfa;
	border-bottom: 1px solid #fff;
}
.p_reletive {
	position: relative;
}
.p_absolute {
	position: absolute;
	right: 0;
	top: 0;
}
.sort_table td .text_l {
	padding-right: 10px;
}
.Page_no {
	height: 30px;
	margin-top: 5px;
}
.two_chat {
	height: 224px;
}
.two_chat h2, .title_right {
	font-size: 12px;
	line-height: 20px;
	color: #fff;
	height: 20px;
	width: 100%;
}
.two_chat .chat_box {
	width: 692px;
	height: 196px;
	margin: 0 auto;
}
.two_chat_box_r {
	height: 90px;
	width: 290px;
	margin: 0 auto;
	margin-top: 5px;
}
.subitem_data_bottom.ranking .li_chat_123 {
	height: 123px;
}
.subitem_data_bottom.ranking .pro_energy_list {
	width: 100%;
}
.pro_energy_list .Data_List {
	width: 99%;
	margin: 0 auto;
}
.pro_energy_list .Data_List th {
	font-size: 12px;
	line-height: 22px;
	height: 22px;
}
.pro_energy_list .Data_List td {
	line-height: 20px;
}
.pro_energy_box {
	height: 90px;
	overflow: auto;
	padding: 0 5px;
}
.time_interval_main {
	width: 100%;
	height: 20px;
	float: left;
	padding-top: 12px;
	padding-bottom: 5px;
}
.time_interval {
	width: 90%;
	height: 5px;
	margin: 0 auto;
	background: #ccc;
}
.time_interval .span_clock {
	height: 5px;
	float: left;
	position: relative;
	width: 0;
}
.time_interval .span_red {
	background: #ff0000;
	width: 30%;
}
.time_interval .span_orange {
	background: #ff8b02;
	width: 27%;
}
.time_interval .span_yellow {
	background: #fdf401;
	width: 27%;
}
.time_interval .span_green {
	background: #1f6301;
	width: 27%;
}
.time_interval .span_blue {
	background: #247df7;
	width: 27%;
}
.time_interval .time_val {
	position: absolute;
	right: 0px;
	top: -12px;
}
.time_interval .time_val.start_no {
	left: 0px;
	right: auto;
}
.time_interval .time_val.time_overlap {
	right: -10px;
}
.time_interval .span_shift {
	position: absolute;
	top: 7px;
	width: 100%;
	text-align: center;
	left: 0;
}
.w90_percent {
	width: 90%;
	margin: 0 auto;
	overflow: hidden;
	padding-top: 5px;
	padding-bottom: 5px;
	line-height: 14px;
}
.button_adjust {
	width: 79px;
	height: 20px;
	line-height: 20px;
	background: url(../Images/button_s.png) no-repeat;
	text-decoration: none;
}
.adjust_interval {
	width: 180px;
	height: 20px;
	margin: 0 auto;
}
.subitem_data_bottom.ranking li.dot_white {
	background: url(../Images/dott2.png) repeat-x 0 bottom;
	margin-bottom: 5px;
	padding-bottom: 5px;
}
/*.subitem_data_bottom.ranking li.dot_white .energy_data_title{ height:24px;}*/
.system_list.w90_percent {
	width: 90%;
	margin: 0 auto;
}
.right_top_change .ranking_data_right_t {
	height: 243px;
	padding-top: 5px;
}
.right_top_change .ranking_data_right_t .ranking_data_right_b {
	height: 238px;
	padding-bottom: 5px;
}
.right_top_change .ranking_data_right {
	height: 238px;
}
.power_factor_list {
	padding: 10px;
	line-height: 24px;
	overflow: hidden;
	text-align: left;
}
.power_factor_list p {
	width: 100%;
	overflow: hidden;
}
.curve_r_bottom.subitem .subitem_datalist li {
	background: 0;
}
.saving_rank {
	height: 269px;
	overflow: hidden;
	width: 1015px;
	margin: 0 auto;
	margin-bottom: 3px;
}
.button_rank {
	position: relative
}
.rank_toleft {
	width: 20px;
	height: 23px;
	position: absolute;
	left: 5px;
	top: 115px
}
.rank_toright {
	width: 20px;
	height: 23px;
	position: absolute;
	right: 5px;
	top: 115px
}
.saving_rank .saving_rank_list {
	padding: 0 30px;
	padding-top: 2px;
	width: auto;
	margin: 0 auto;
	overflow: hidden;
}
.saving_rank .saving_rank_list li {
	float: left;
	background: #bddcfa;
	margin-right: 2.5px;
	margin-left: 2.5px;
	width: 186px;
	border-bottom: 2px solid #fe8815;
	height: 265px;
}
.saving_rank .saving_rank_list li h2 {
	text-align: center;
	color: #000;
	font-size: 12px;
	line-height: 30px;
	background: #fe8815;
	font-family: Microsoft YaHei;
}
.saving_rank .saving_rank_list li .morelist {
	text-decoration: none;
	color: #0096ce;
	line-height: 20px;
}
.saving_rank .saving_rank_list li table td {
	border-bottom: 1px dotted #ccc;
}
.num_01 {
	color: #3c6f0f;
	font-size: 16px;
}
.unit_title {
	font-size: 11px;
	font-weight: 100;
	color: #444;
	padding-left: 5px;
	font-family: Georgia, 'Times New Roman', Times, serif;
}
.serach_bg_area {
	width: auto;
	height: 26px;
	padding: 3px;
	float: left;
	text-align: center;
	line-height: 26px;
}
.serach_bg_area .input3.on{ border:1px solid #fa9800; 
	font-size: 12px;
	font-family: Microsoft YaHei;}
.chat_main_box {
	width: 98%;
	height: 330px;
	position: relative;
	margin: 0 auto;
	border: 1px solid #d3d7da;
	background: #fff;
	margin-top: 10px;
}
.chat_main_box2 {
	width: 80%;
	height: 240px;
	position: relative;
	margin: 0 auto;
	border: 1px solid #d3d7da;
	background: #fff;
	margin-top: 10px;
}
.chat_main_box3 {
	width: 100%;
	height: 240px;
	position: relative;
margin:0 auto border:1px solid #d3d7da;
	background: #fff;
	;
}
.chat_main_box4 {
	width: 100%;
	height: 100%;
	position: relative;
	margin: 0 auto;
	background: #fff;
}
.menuContent {
	display: none;
	position: absolute;
	z-index: 999;
	background: #f1f1f1;
	border: 1px solid #292e33;
	top: 29px;
	left: 0px;
	height: 150px;
	overflow: auto;
	width: 162px;
}
.treemain_c .menuContent {
	display: none;
	position: absolute;
	z-index: 999;
	background: #292e33;
	border: 1px solid #292e33;
	top: 29px;
	left: 0px;
	height: 150px;
	overflow: auto;
	width: 162px;
}
.menuContent.w80_w {
	width: 112px;
}
.menuContent .ztree li a {
}
.sort_data_type {
	background: url(../Images/sort_list.png) no-repeat;
	padding-left: 58px;
	height: 77px;
}
.sort_data_type .mid_box {
	background: url(../Images/sort_list_out.png) right center no-repeat;
	height: 77px;
	padding-right: 15px;
}
.sort_data_type .in_box {
	background: url(../Images/sort_list_in.png) left center repeat-x;
	height: 77px;
}
.sort_data_choice_ul {
	width: 850px;
	margin: 0 auto;
	height: 77px;
	float: right;
}
.sort_data_choice_ul li {
	width: 212px;
	height: 77px;
	float: left;
	color: #fff;
	text-align: center;
	font-family: Microsoft YaHei;
	font-size: 16px;
	line-height: 77px;
}
.sort_date {
	width: 66px;
	padding-right: 5px;
	color: #fff;
	height: 77px;
	font-size: 16px;
	text-align: center;
	margin: 0 auto;
	line-height: 77px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	background: url(../Images/sort_list_li_bg.png) no-repeat;
}
.sort_date.on {
	background: url(../Images/sort_list_li_bg_on.png) left center no-repeat;
	;
}
.sort_num {
	background: url(../Images/sort_num_l.png) no-repeat;
	height: 34px;
	padding-left: 8px;
	float: left;
	position: absolute;
	top: -2px;
	left: -2px;
}
.sort_num .num_text {
	background: url(../Images/sort_num_r.png) right 0 no-repeat;
	padding-right: 8px;
	float: left;
	height: 34px;
	line-height: 34px;
	color: #fff;
	font-size: 24px;
	font-family: Microsoft YaHei;
}
.data_voltage_tab {
	float: left;
	height: 22px;
	line-height: 22px;
	padding: 0 10px;
	color: #fff;
	background: #b0caea;
}
/*南通二期添加树tab分类查询*/
.Tree_type-tab {
	width: 55px;
	position: absolute;
	top: 0;
	left: 0;
	margin-top: 86px;
	margin-bottom: 27px;
	background: #292e33;
	z-index: 999;
}
.Tree_type-tab li {
	width: 35px;
	padding: 15px 10px;
	color: #fff;
	cursor: pointer;
	line-height: 18px;
	font-family: Microsoft YaHei;
	font-size: 14px;
	border-bottom: 1px solid #676b6e;
}
.Tree_type-tab li .user_icon {
	background: url(../Images/user_icon.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li .group_icon {
	background: url(../Images/group_icon.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li .point_icon {
	background: url(../Images/point_icon.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li .tree_icon {
	background: url(../Images/tree_icon.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li:hover {
	color: #fa9800;
	background-color: #224a6a;
}
.Tree_type-tab li.on {
	color: #fa9800;
	background-color: #4f412a;
}
.Tree_type-tab li.on .user_icon {
	background: url(../Images/user_icon_on.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li.on .group_icon {
	background: url(../Images/group_icon_on.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li.on .point_icon {
	background: url(../Images/point_icon_on.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li.on .tree_icon {
	background: url(../Images/tree_icon_on.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li:hover .user_icon {
	background: url(../Images/user_icon_on.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li:hover .group_icon {
	background: url(../Images/group_icon_on.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li:hover .point_icon {
	background: url(../Images/point_icon_on.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_type-tab li:hover .tree_icon {
	background: url(../Images/tree_icon_on.png) center top no-repeat;
	width: 15px;
	padding: 0 10px;
	padding-top: 38px;
}
.Tree_search {
	padding: 0;
	padding-top: 10px;
	line-height: 34px;
}
.Tree_search.h220 {
	height: 220px;
	font-size: 12px;
	overflow: auto;
}
.Tree_search .line_top {
	border-bottom: 1px solid #292e33;
	height: 270px;
}
.Tree_search .ener_r {
	color: #fff;
	width: 75px;
	text-align: right;
	padding-right: 5px;
	font-family: Microsoft YaHei;
	font-size: 12px;
}
.Tree_search .input3 {
	width: 123px;
	height: 29px;
	line-height: 29px;
	padding: 0;
	text-indent: 5px;
	background: url(../Images/input_bg.png) no-repeat;
	border: 0;
	color: #fff
}
.Tree_search .input3.onfcous_input {
	background: url(../Images/input_bg_on.png) no-repeat;
}
.Tree_search .dropdown4 {
	width: 123px;
	height: 29px;
	background: url(../Images/input_bg.png) no-repeat;
}
.Tree_search .dropdown4 .tag_options {
	width: 121px;
	top: 27px;
	background: #393f46;
	border: 1px solid #292e33;
}
.Tree_search .tag_select {
	background: url(../Images/slect_icon_right.png) center right no-repeat;
	height: 29px;
	border: 0;
	line-height: 29px;
	padding-left: 0;
	text-indent: 5px;
	text-align: left;
	color: #fff;
}
.Tree_search .tag_options li {
	padding-left: 5px;
	background: #393f46;
	height: 25px;
	line-height: 25px;
	color: #fff;
	text-align: left;
}
.Tree_search .tag_options li:nth-child(even) {
	background: #2e3339;
}
.Tree_search .tag_options li.open_hover {
	background: #4280a6;
}
.Tree_search .tag_options li.open_selected {
	background: #a37020;
}
.Tree_main .ztree li a {
	color: #cfcfcf;
}
.region_search {
	height: 26px;
	line-height: 26px;
	width: 100%;
	border: 1px solid #9AC5F0;
	overflow: visible;
	border-bottom: 0;
}
.region_search.hover {
	background: #BDDCFA;
	position: relative;
	border: 1px solid #7EB5EF;
	border-bottom: 0;
}
.region_search .region_checked {
	padding-left: 12px;
	width: 68px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	height: 26px;
	line-height: 26px;
	background: url(../Images/area.png) 0 center no-repeat;
	font-family: Arial, Verdana, "宋体";
	float: left;
	color: #093D6D;
	cursor: pointer;
}
.region_search .region_select {
	display: none;
	width: 380px;
	height: 20px;
	padding: 5px;
	position: absolute;
	left: -1px;
	top: 26px;
	background: #BDDCFA;
	border: 1px solid #7EB5EF;
	border-top: 0;
}
.region_search .region_select .close {
	width: 18px;
	height: 17px;
	position: absolute;
	background: url(../Images/area_close.png) 0 center no-repeat;
	right: -7px;
	top: -7px;
	cursor: pointer;
}
.region_search .region_select .dropdown {
	margin-right: 15px;
}
.tree_search_button {
	width: 66px;
	height: 31px;
	background: url(../Images/go-blue.png) no-repeat;
	border: 0;
	margin: 0 auto;
	cursor: pointer;
	margin-top: 10px;
	margin-bottom: 25px;
}
.tree_search_button:hover {
	background: url(../Images/go-blue_h.png) no-repeat;
}
.tree_search_total {
	height: 38px;
	line-height: 38px;
	border-top: 1px solid #5a6571;
	background: #2d343c;
	border-bottom: 1px solid #2a2f35;
	border-left: 0;
	color: #ffffff;
	font-family: Microsoft YaHei;
	border-right: 0;
	padding: 0 10px;
}
.tree_search-list {
	padding: 0;
	overflow: auto;
}
.tree_search-list li {
	border-bottom: 1px solid #2a2f35;
	border-top: 1px solid #5a6571;
	padding: 0 10px;
	height: 40px;
	line-height: 40px;
	font-family: Microsoft YaHei;
	font-size: 14px;
}
.tree_search-list li a {
	color: #cfcfcf;
}
.tree_search-list li .user_name {
	width: 105px;
	overflow: hidden;
	text-overflow: ellipsis;
	text-align: left;
	-o-text-overflow: ellipsis;
	white-space: nowrap;
}
.tree_search-list li .user_num {
	width: 60px;
	overflow: hidden;
	text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	white-space: nowrap;
	color: #fa9800;
	text-align: right;
}
.tree_search-list li.on {
	background: #39424a;
}
.Tree_search fieldset {
	width: auto;
	font-size: 12px;
	font-family: Microsoft YaHei;
	font-weight: 100;
	border-color: #437EC0;
	background: #BDDCFA;
}
.Tree_search fieldset legend {
	color: #000;
	background: url(../Images/group-leg.png) 0 1px no-repeat;
}
.group_tab li {
	float: left;
	margin-right: 15px;
	color: #333;
	font-weight: 100;
	cursor: pointer;
}
.group_tab li.on {
	color: #fff;
	font-weight: bold;
}
.group_main {
	width: 185px;
	overflow: hidden;
	margin: 0 auto;
	text-align: left;
}
.private_group_title {
	width: 185px;
	height: 30px;
	background: url(../Images/private_group_title.png) no-repeat;
	font-family: "微软雅黑";
	text-indent: 14px;
	font-size: 13px;
	color: #8dc913;
}
.public_group_title {
	width: 185px;
	height: 30px;
	background: url(../Images/public_group_title.png) no-repeat;
	font-family: "微软雅黑";
	text-indent: 14px;
	font-size: 13px;
	color: #8dc913;
}
.Tree_search .group_list {
	width: 157px;
	padding: 0 14px;
	overflow: auto;
	height: 96px;
	background: url(../Images/group_bottom.png) bottom no-repeat;
	padding-bottom: 6px;
	background-color: #383f47;
}
.Tree_search .group_list li {
	margin: 0;
	height: 23px;
	border-bottom: 1px solid #8f9294;
	line-height: 23px;
}
.Tree_search .group_list li .group_name {
	width: 75px;
	overflow: hidden;
	text-overflow: ellipsis;
	text-align: left;
	-o-text-overflow: ellipsis;
	white-space: nowrap;
	color: #cfcfcf;
}
.Tree_search .group_list li .group_user {
	width: 67px;
	overflow: hidden;
	text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	white-space: nowrap;
	color: #cfcfcf;
	text-align: left;
}
.Tree_search .group_list li:hover {
	background: #21262a;
}
.Tree_search .group_list li.on {
	background: #21262a;
}
/*下拉选择框*/
.data_w100 {
	width: 100%;
	height: 30px;
}
.view_txtbox {
	width: 106px;
	text-align: left;
	font-size: 12px;
	color: #black;
	font-weight: 100;
	background: url(../Images/search_btn.png) right center no-repeat;
	border: 1px solid #b0b0b0;
	cursor: pointer;
	padding-left: 4px;
	line-height: 26px;
	height: 26px;
	padding-right: 25px;
}
.choice_box_a {
	position: absolute;
	top: 26px;
	left: 0;
	width: 135px;
	height: 120px;
	overflow: auto;
	z-index: 9;
	display: none;
	background:#fff;
}
.check-box_sel {
	line-height: 24px;
	display: block;
	width: 98%;
	border: 1px solid #b0b0b0;
}
.check-box_sel li {
	height: 22px;
	font-size: 12px;
	font-weight: 100;
	padding: 0 3px;
	color: #333;
}
.check-box_sel li:hover {
	background: #4a7ec0;
	font-weight: bold;
}
.check-box_sel li.selected {
	background: #3B6EA1;
	font-weight: bold;
}
.data_limit_font {
	font-size: 12px;
	font-weight: 100;
}
/*分户管理南通二期*/
.ledger_region .region_search {
	border-color: #fff;
	font-size: 12px;
	font-weight: 100
}
.ledger_region .region_search .region_select {
	height: 26px;
	right: 0px;
	left: auto;
	border: 0;
}
.ledger_region .region_search .region_select .close {
	left: -7px;
}
/*STS营业厅购电*/
.sts_purchase {
	overflow: hidden;
	padding: 0 5px;
}
.sts_purchase li {
	width: 33%;
	float: left;
	text-align: left;
	line-height: 20px;
	color: #093D6D;
}
.sts_purchase_info {
	background: #f2f2f2;
	height: 460px;
	padding: 10px 0;
	margin: 0 auto;
	margin-top: 15px;
	color: #000;
}
.sts_purchase_info li {
	color: #333;
	line-height: 26px;
}
.price_text {
	font-size: 16px;
	font-weight: bold;
	font-family: Microsoft YaHei;
}
.price_box {
	border: 1px solid #7f9db9;
	height: 30px;
	line-height: 30px;
	font-family: Microsoft YaHei;
	font-size: 16px;
	font-weight: bold;
	color: #ff7800;
	text-align: center;
	margin-left: 10px;
	margin-right: 5px;
	padding: 0 5px;
	background: #fff;
	-moz-border-radius: 5px;      /* Gecko browsers */
	-webkit-border-radius: 5px;   /* Webkit browsers */
	border-radius: 5px;            /* W3C syntax */
}
.purchase_btn-box {
	width: 300px;
	margin: 0 auto;
	height: 38px;
	margin-top: 10px;
}
.purchase_btn {
	width: 118px;
	height: 38px;
	float: left;
	background: url(../Images/purchase_btn.png) no-repeat;
	border: 0;
	cursor: pointer;
}
.purchase_btn-online {
	width: 118px;
	height: 38px;
	float: right;
	background: url(../Images/purchase_btn-online.png) no-repeat;
	border: 0;
	cursor: pointer;
}
.purchase_resault {
	width: 460px;
	border-top: 2px dashed #9ac5f0;
	overflow: hidden;
	margin: 0 auto;
	margin-top: 30px;
	padding: 0 260px;
	padding-top: 40px;
}
.text_resault {
	width: 310px;
	float: left;
	overflow: hidden;
}
.price_box {
	width: 225px;
	color: #093D6D;
	background: #9ac5f0;
}
.price_box.w55px {
	width: 108px;
}
.img_resault {
	width: 135px;
	height: 135px;
	float: left;
	border: 1px solid #ccc;
	background: #fff;
	padding: 5px;
}
.massage_resault {
	height: 32px;
	line-height: 32px;
	padding: 10px 0;
	padding-left: 36px;
	font-size: 14px;
	font-family: Microsoft YaHei;
	margin: 0 auto;
	width: auto;
}
.massage_resault.success {
	background: url(../Images/success.png) left center no-repeat;
}
.massage_resault.error {
	background: url(../Images/error.png) left center no-repeat;
}
/*打印票据*/
.print_bill {
	width: 290px;
	overflow: hidden;
	background: #fff;
	padding: 10px;
	margin: 0 auto;
}
.print_bill .user_title {
	height: 45px;
	line-height: 45px;
	border-bottom: 1px solid #000;
	background: url(../Images/electricity.png) left center no-repeat;
	font-weight: 100;
	font-size: 12px;
	text-align: left;
	text-indent: 30px;
}
.print_bill .print_detail {
	line-height: 20px;
	text-align: left;
}
.print_bill .print_purchase_resault {
	overflow: hidden;
	border: 1px solid #000;
	padding-bottom: 10px;
}
.print_bill .print_purchase_resault .price_box {
	background: #ccc;
	color: #000;
	border: 1px solid #aaa;
}
.print_bill .print_purchase_resault .img_resault {
	clear: both;
	margin: 0 auto;
	margin-top: 10px;
	float: none;
}
.pop-up {
	position: absolute;
	bottom: 38px;
	right: 17px;
	width: 237px;
	z-index: 1;
}
.pop-up .title_wrap {
	height: 52px;
}
.pop-up .event_title {
	font-size: 14px;
	font-weight: bold;
}
.event_warning {
	position: absolute;
	left: 0px;
	top: 24px;
}
.close_win {
	width: 24px;
	height: 28px;
	background: url(../Images/delete_box.png);
	position: absolute;
	right: 0px;
	top: 24px;
	cursor: pointer;
}
.close_win:hover {
	background: url(../Images/delete_box_hover.png);
}
#event_content {
	border: 1px solid #d3d7da;
	border-top: none;
	height: 248px;
	padding:5px;
	background: #FFFFFF;
	overflow: auto;
}
#event_content div {
	text-align: left;
	margin: 0em 2px 1em 4px;
}
.manager_page fieldset.price_list {
	margin-top: 5px;
}
.manager_page fieldset.price_list legend {
	background: #BDDCFA;
	padding: 5px;
}
.manager_page .text_C td {
	text-align: center
}
.text_C th {
	text-align: center
}
.hr_line {
	height: 7px;
	background: url(../Images/hr_line.png) left no-repeat;
	background-color: #c0cbd8;
	margin-top: 10px;
	margin-bottom: 5px;
}
.total_hide .pxofy {
	display: none;
}
.pop_up_box {
	padding: 9px;
}
.pop_up_box .t1 td {
	background-color: #f1f3f4;
	text-align: left;
	padding-left: 10px;
	height: 29px;
	color: #4b4b4b;
	border-right: 1px solid #fff;
	border-bottom: 1px solid #fff;
}
.pop_up_box .t2 {
	margin-top: 12px;
}
.t2 th {
	border-spacing: 0px;
	height: 26px;
	line-height: 26px;
	background: #b0caea;
	font-size: 14px;
}
.pop_up_box .t2 td {
	background-color: #f1f3f4;
	text-align: left;
	height: 29px;
	color: #4b4b4b;
	padding-left: 18px;
}
.advise_span {
	color: #fa9800;
}
.title_H_bg {
	height: 36px;
	background: url(../Images/advise.png) left no-repeat;
}
.pop_up_box #inputTxt {
	width: 92px;
	height: 23px;
	border: 1px solid #fa9800;
	line-height: 23px;
}
.menuContent.page {
	background: #fff;
	border: 1px solid #b0b0b0;
	width: 158px;
}
.menuContent.page .ztree li a {
	color: #333;
}
.manager_page .MD_declare {
	text-align: center;
	font-size: 12px;
	font-weight: 100;
}
.manager_page .MD_declare table {
	margin: 0;
}
.manager_page .MD_declare .ue-table .table-panel {
	margin: 0;
}
.tdbg {
	background: #E2EAFC;
}
.tdbg1 {
	background: #eaf7db;
}
.tdbg2 {
	background: #e5eaef;
}
.tdbg3 {
	background: #e8ede0;
}
/**新增采集点**/
#meterEditForm table {
	margin-top: 6px;
}
#userEditForm {
	margin-top: 6px;
}
#roleEditForm {
	margin-top: 6px;
}
#ledgerForm {
	margin-top: 6px;
}
.modifyPassword {
	padding-top: 6px;
}
#passwordForm td {
	text-align: right;
}
.h40 {
	height: 40px;
}
.add_line_more {
	height: 32px;
	width: 68px;
	background: #14a7e8;
	border: 0px;
	color: #fff;
	font-size: 14px;
	cursor: pointer;
}
#mdAnalysis .num {
	font-size: 16px;
	color: #2ed900;
}
.tr_top td {
	border-top: 1px dotted #d3d3d3;
}
.fc_1 {
	color: #2ed900;
}
.fc_2 {
	color: #14a7e8;
}
.pop_win_table {
	width: 98%;
	padding-top: 50px;
	border-spacing: 0px;
	margin: auto;
}
.pop_win_table th {
	background: #b0caea;
	border-spacing: 0px;
	height: 26px;
	line-height: 26px;
	font-size: 14px;
}
.pop_win_table td {
	background: #E2EAFC;
	border-spacing: 0px;
	height: 26px;
	line-height: 26px;
	font-size: 14px;
}
.mt30 {
	margin-top: 30px;
}
.pl_10 {
	padding-left: 10px;
}
.d_title {
	height: 34px;
	background-color: #f1f1f1;
}
.s_title {
	font-size: 16px;
	height: 34px;
	line-height: 34px;
	color: #4b4b4b;
	margin-left: 166px;
}
.mb0 {
	margin-bottom: 0px;
}
.s_split {
	width: 10px;
	padding-left: 5px;
	height: 29px;
	line-height: 29px;
}
.tree_btn {
	width: 20px;
	height: 37px;
	position: absolute;
	top: 420px;
	left: 238px;
	background: url(../Images/tree_btn.png)
}
.tree_btn:hover {
	background: url(../Images/tree_btn_h.png);
	cursor: pointer;
}
#treeWin .ztree li span.button.roots_open, #treeWin .ztree li span.button.bottom_open, #treeWin .ztree li span.button.center_open {
	background-position: 3px -76px
}
#treeWin .ztree li span.button.roots_close, #treeWin .ztree li span.button.bottom_close, #treeWin .ztree li span.button.center_close {
	background-position: -20px -76px
}
.s_radio {
	background: url(../Images/radio.png) 0px 4px no-repeat;
	padding-left: 15px;
}
#confirmD {
	overflow: hidden;
}
.td_bl {
	border-left: 1px solid #c3c3c3;
}
#ticker {
	width: 250px;
	height: 30px;
	overflow: auto;
	margin-left: 880px;
}
#ticker dt {
	padding: 0 10px 5px 10px;
	padding-top: 10px;
	position: relative;
	font-weight: 100;
	font-size: 18px;
	color: #647581;
}
#ticker dd.last {
	border-bottom: 1px solid #ffffff;
}
#ticker div {
	margin-top: 0;
}
.t_l {
	text-align: left;
}
.ue-table .table-panel #tbody td .event_text {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 26px;
	word-wrap: normal;
	text-align: left;
}
.ue-table .table-panel #tbody td .suggest_text {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: 300px;
	height: 26px;
	word-wrap: normal;
	text-align: left;
}
#item5 div {
	width: 260px;
	min-height: 29px;
	line-height: 29px;
}
#selectTime {
	text-align: left;
	margin-left: 10px;
	font-size: 13px;
	color: #15a7e8;
	height: 20px;
}
.Data_List tbody .tl {
	text-align: left;
	padding-left: 10px;
}
.demo_header {
	height: 80px;
	line-height: 80px;
	background: repeating-linear-gradient(to right, #183052, #287eaf 32%, #19375d 100%);background-repeat:no-repeat;
}
.demo_header_content {
	width: 520px;
	position: absolute;
	left: 50%;
	margin-left: -260px;
	color: #fff;
	font-size: 30px;
	padding-left: 170px;
	text-align: left;
	background: url() 0px 0px no-repeat;
}
.Data_List.bb th {
	background: #E2EAFC;
	width: auto;
}
.out {
	border-top: 53px #E2EAFC solid;/*上边框宽度等于表格第一行行高*/
	width: 0px;/*让容器宽度为0*/
	height: 0px;/*让容器高度为0*/
	border-left: 100px #fff solid;/*左边框宽度等于表格第一行第一格宽度*/
	position: relative;/*让里面的两个子容器绝对定位*/
}
.out b {
	font-style: normal;
	display: block;
	position: absolute;
	top: -49px;
	left: -48px;
	width: 35px;
}
.out em {
	font-style: normal;
	display: block;
	position: absolute;
	top: -30px;
	left: -86px;
	width: 55x;
}
.validate {
	float: left;
	margin-top: 5px;
	width: 20px;
	height: 20px;
	margin-left: 5px;
}
.validate.success {
	background: url(../Images/success.png) center center no-repeat;
	background-size: 100% 100%;
	background-origin: content-box
}
.validate.error {
	background: url(../Images/error.png) center center no-repeat;
	background-size: 100% 100%;
	background-origin: content-box
}
.validate.tips {
	background: url(../Images/tips.png) center center no-repeat;
	background-size: 100% 100%;
	float: right;
	margin-right: 14px;
	background-origin: content-box
}
.out em {
	font-style: normal;
	display: block;
	position: absolute;
	top: -30px;
	left: -86px;
	width: 55px;
}
.opacity70 {
	filter: alpha(opacity=70); /*支持 IE 浏览器*/
	-moz-opacity: 0.70; /*支持 FireFox 浏览器*/
	opacity: 0.70; /*支持 Chrome, Opera, Safari 等浏览器*/
}
.contact_page {
	padding: 15px;
	text-align: left;
}
.contact_title {
	font-size: 16px;
	color: #343434;
	font-weight: bold;
}
.contact_content {
	line-height: 32px;
	font-size: 14px;
	color: #333;
	text-indent: 7px;
}
.advise_box {
	border: 1px solid #e3e3e3;
	border-left: 0;
	border-right: 0;
	padding: 10px 0;
	margin-top: 10px;
}
.advise_area {
	width: 548px;
	height: 114px;
	border: 0;
	border-bottom: 1px solid #d4d4d4;
	background: #f4f4f4;
	margin-left: 7px;
	margin-top: 10px;
	padding: 10px;
	resize: none;
	font-family: "微软雅黑";
	color: #bdbdbd;
	overflow: hidden
}
.advise_area2 {
	width: 548px;
	height: 114px;
	border: 0;
	border-bottom: 1px solid #d4d4d4;
	background: #f4f4f4;
	margin-left: 7px;
	margin-top: 10px;
	padding: 10px;
	resize: none;
	font-family: "微软雅黑";
	color: #333;
	overflow: hidden
}
.contactway-info {
	height: 32px;
	margin-left: 7px;
	line-height: 32px;
	margin-top: 10px;
}
.contactway-info .way_info {
	width: 502px;
	float: left;
	height: 31px;
	border: 0;
	border-bottom: 1px solid #d4d4d4;
	background: #f4f4f4;
	text-indent: 10px;
	color: #bdbdbd;
}
.contactway-info .way_info2 {
	width: 502px;
	float: left;
	height: 31px;
	border: 0;
	border-bottom: 1px solid #d4d4d4;
	background: #f4f4f4;
	text-indent: 10px;
	color: #333;
}
/*头部小工具*/

.global-top-right {
	height: 27px;
	line-height: 27px;
	float: right;
	margin-bottom: 7px;
}
.global-top-right li {
	float: left;
	color: #fff;
	font-weight: bold;
	cursor: pointer;
	padding: 0 7px;
	height: 27px;
	line-height: 28px;
}
.global-top-right .global-top-item {
	float: left;
	height: 27px;
	line-height: 27px;
	padding: 0 7px;
	font-style: normal;
	font-weight: 500;
}
.global-top-right li:hover {
	text-decoration: underline;
}
.global-top-right li .global-top-angle {
	width: 10px;
	height: 7px;
	background: url(../Images/global-top-angle.png) no-repeat;
	float: right;
	margin-top: 11px;
	margin-left: 5px;
	transition: transform 0.5s ease 0s;
	-moz-transition: -moz-transform 0.5s; /* Firefox 4 */
	-webkit-transition: -webkit-transform 0.5s; /* Safari and Chrome */
	-o-transition: -o-transform 0.5s; /* Opera */
}
.global-top-right li.role_change {
	position: relative;
}
.role_choose-list {
	background: #f2f2f2;
	padding: 5px;
	position: absolute;
	right: 0;
	top: 26px;
	width: 90px;
	z-index: 999;
	display: none;
}
.role_choose-list li {
	border-bottom: 1px solid #cccccc;
	padding: 0 5px;
	text-indent: 20px;
	text-align: left;
	height: 24px;
	line-height: 24px;
	color: #666;
	width: 120px;
}
.role_choose-list li.engineer {
	background: url(../Images/engineer.png) left center no-repeat;
}
.role_choose-list li.manager {
	background: url(../Images/manager.png) left center no-repeat;
	border: 0;
}
.role_choose-list li:hover {
	color: #ff6600;
}
.global-top-right li.role_change:hover {
	background: #f2f2f2;
	color: #3d474a;
}
.global-top-right li.show-on {
	background: #f2f2f2;
	color: #3d474a;
}
.global-top-right li:hover .global-top-angle {
	background: url(../Images/global-top-angle-hover.png) no-repeat;
	transform: rotate(180deg);
	-moz-transform: rotate(180deg); /* Firefox 4 */
	-webkit-transform: rotate(180deg); /* Safari and Chrome */
	-o-transform: rotate(180deg); /* Opera */
}
.global-top-right li.show-on .global-top-angle {
	background: url(../Images/global-top-angle-hover.png) no-repeat;
	transform: rotate(180deg);
	-moz-transform: rotate(180deg); /* Firefox 4 */
	-webkit-transform: rotate(180deg); /* Safari and Chrome */
	-o-transform: rotate(180deg); /* Opera */
}
.global-top-right li.show-on .role_choose-list {
	display: inline;
}
/* XL 电能示值查询--饼图 */
.dnsz_pieWrap {
	width: 100%;
	margin-top: 50px;
}
.dnsz_pieWrap .pieWrap_1, .dnsz_pieWrap .pieWrap_2 {
	margin-top: 35px;
}
.dnsz_pieWrap .pie_content {
	width: 100%;
}
.pie_content .pie_left, .pie_content .pie_right {
	width: 49%;
	float: left;
}
.pie_content .pie_left {
	border-top: 1px solid #d3d7da;
	margin-left: 0.9%;
}
.pie_content .pie_right {
	border: 1px solid #d3d7da;
	height: 300px;
}
/*  XL 电费分析弹出框样式 */
#costAnalysisWrap .part_2 .pictureWrap {
	background: #d1dbdd;
	padding: 4px;
	margin-top: 5px;
	height: 370px;
}
#costAnalysisWrap .part_2 .pic_line1 {
	background: #fff;
	border-bottom: 4px solid #d1dbdd;
	height: 181px;
}
#costAnalysisWrap .part_2 .pic_line2 {
	background: #fff;
	height: 185px;
}
#costAnalysisWrap .part_2 .picture1 {
	height: 100%;
	width: 30%;
	float: left;
}
#costAnalysisWrap .part_2 .picture2 {
	height: 100%;
	width: 26%;
	float: left;
}
#costAnalysisWrap .part_2 .word-1 {
	width: 42%;
	float: left;
	margin-top: 30px;
	font-size: 14px;
	margin-left: 8px;
	background-color: #f1f3f4;
}
#costAnalysisWrap .part_2 .word_1 {
	margin: 10px 0px 10px 9px;
}
#costAnalysisWrap .part_2 .picture3 {
	height: 100%;
	width: 30%;
	float: left;
}
#costAnalysisWrap .part_2 .picture4 {
	height: 100%;
	width: 26%;
	float: left;
}
#costAnalysisWrap .part_2 .word-2 {
	width: 42%;
	float: left;
	font-size: 14px;
	margin-top: 20px;
	margin-left: 8px;
	background-color: #f1f3f4;
}
#costAnalysisWrap .part_2 .word_2 {
	margin: 8px 0px 8px 9px;
}
.costAnalysis-text-time {
	width: 154px;
	float: left;
	text-align: left;
}
.costAnalysis-add {
	width: 110px;
	float: left;
	text-align: left;
}
.t_indent {
	text-indent: 8px;
}
#costAnalysisWrap .part_2 #p2_eleQ, #costAnalysisWrap .part_2 #p2_eleF {
	color: #2ed900;
}
#costAnalysisWrap .part_2 #p2_chart1, #costAnalysisWrap .part_2 #p2_chart3 {
	height: 160px;
}
#costAnalysisWrap .part_2 #progressbar1, #costAnalysisWrap .part_2 #progressbar2, #costAnalysisWrap .part_2 #progressbar3, #costAnalysisWrap .part_2 #progressbar4 {
	width: 100%;
	border-radius: 15px;
	background-color: #d3dbdd;
}
#costAnalysisWrap .part_2 #progressbar1 .ui-widget-header, #costAnalysisWrap .part_2 #progressbar3 .ui-widget-header {
	background-color: #2ed900;
	border-radius: 15px;
	height: inherit;
}
#costAnalysisWrap .part_2 #progressbar2 .ui-widget-header, #costAnalysisWrap .part_2 #progressbar4 .ui-widget-header {
	background-color: #14a7e8;
	border-radius: 15px;
	height: inherit;
}
#costAnalysisWrap .part_2 .last_this_q {
	margin: 20px 0px 2px 0px;
	text-align: left;
	text-indent: 8px;
}
#costAnalysisWrap .part_2 .last_this_fee {
	margin: 25px 0px 2px 0px;
	text-align: left;
	text-indent: 8px;
}
#costAnalysisWrap .part_2 .tipWrap {
	margin-top: 10px;
	float: left;
}
#tDeclareHead tr {
	background: #fff;
}
/*升级信息*/
.Upgrade_information {
	width: 1000px;
	margin: 0 auto;
	overflow: hidden;
	text-align: left;
	font-size: 14px;
	line-height: 30px;
	margin-top: 10px;
}
.Upgrade_information .info_indent {
	padding: 0 1em;
}
#updateHelp {
	background: #fff;
}
/*请选择公司页面*/
.table_edit {
	width: 622px;
	overflow: hidden;
	line-height: 40px;
	font-size: 14px;
	text-align: left;
}
.input_text_long {
	width: 190px;
	height: 27px;
	background: #f5f3f3;
	border: 1px solid #b0b0b0;
	text-indent: 5px;
	line-height: 27px;
	font-size: 12px;
}
.input_choice_company {
	width: 420px;
	height: 27px;
	border: 1px solid #b0b0b0;
	background: url(../Images/select_btn.png) center right no-repeat;
	border-right: 0;
	background-color: #f5f3f3;
	text-indent: 10px;
	line-height: 27px;
	font-size: 12px;
	margin-top: 5px;
	cursor: default;
}
.select_company {
	width: 409px;
	height: 217px;
	background: #f7fafd;
	border: 1px solid #d1d4d7;
	margin-top: 10px;
	padding: 5px;
	line-height: 24px;
	overflow: auto;
}
.select_company li {
	background: url(../Images/checkbox.png) no-repeat left 6px;
	padding-left: 20px;
	font-size: 12px;
}
.select_company li.on {
	background: url(../Images/checkbox-checked.png) no-repeat left center;
}
#updateHelp {
	background: #fff;
}
a:focus {
	outline: none;
}
/*大屏树*/
.bg_screen {
	background: url(../Images/back.png) no-repeat 100% 100%;
	background-size: cover;
}
.screen-tree-top {
	background: url(../Images/screen-tree-top.png) no-repeat;
	padding-top: 39px;
}
.screen-tree-bottom {
	background: url(../Images/screen-tree-bottom.png) 0 bottom no-repeat;
	padding-bottom: 39px;
}
.screen-tree-center {
	background: url(../Images/screen-tree-center.png) repeat-y;
	height: 100%;
}
.screen-tree-center .ztree li a {
	font-size: 14px;
	color: #cfcfcf;
}
.screen-tree-center .ztree * {
	font-size: 14px;
}
/*服务报告*/
.Service-Reporting {
	width: 100%;
	overflow: hidden;
}
.report-title {
	background: #06195a;
	height: 89px;
	line-height: 89px;
	color: #fff;
	font-size: 32px;
}
.report-wel-com {
	height: 52px;
	line-height: 52px;
	background: #e3edfa;
	color: #15a7e8;
	font-size: 18px;
	text-align: left;
	text-indent: 18px;
}
.Report-Content {
	padding: 0 15px;
	overflow: hidden;
	text-align: left;
	background: #fff;
	margin-bottom: 30px;
}
.Report-Content .title-H {
	line-height: 34px;
	height: 34px;
	font-size: 14px;
	color: #000;
	text-indent: 3px;
}
.Report-chat {
	height: 281px;
	padding: 5px;
	border: 1px solid #d3d7da;
	margin-bottom: 8px;
	-moz-border-radius: 3px;      /* Gecko browsers */
	-webkit-border-radius: 3px;   /* Webkit browsers */
	border-radius: 3px;            /* W3C syntax */
}
.chat-title {
	color: #15a7e8;
	font-size: 15px;
	text-align: center;
	height: 30px;
	line-height: 30px;
}
.chat-title.alL {
	text-align: left;
}
.chat-H {
	height: 230px;
	padding: 10px;
}
.chat-data-R {
	width: 29%;
	height: 250px;
	float: right;
	padding: 0 10px;
	padding-top: 25px;
	border-left: 1px dashed #fb9900;
	color: #4d5864;
	line-height: 24px;
}
.tc-blue-14px {
	font-size: 14px;
	color: #15a7e8;
}
.text-U {
	width: 132px;
	border-bottom: 1px solid #4d5864;
	height: 20px;
}
.chat-L-70p {
	width: 68%;
	float: left;
}
.chat-Content {
	width: 100%;
	height: 242px;
	margin-top: 10px;
}
.chat-Wauto {
	width: 45%;
	float: left;
	padding: 0 2.4%;
	height: 240px;
}
.border-dashed-orange {
	border-right: 1px dashed #fb9900;
}
.chat-Wauto .title-H-border {
	height: 32px;
	line-height: 32px;
	border-bottom: 1px dashed #15a7e8;
	text-align: center;
	font-size: 14px;
}
.chat-Wauto .title-H-border .total-used {
	color: #2ed900;
	font-size: 18px;
}
.chat-Wauto .chat-data {
	height: 200px;
	margin-top: 8px;
}
.curve-chat {
	float: left;
	width: 30%;
	height: 240px;
}
.chat-table {
	float: left;
	width: 35%;
	padding-left: 4.5%;
	height: 240px;
	overflow: auto;
}
.chat-table table {
	background: #fff;
	line-height: 24px;
}
.chat-table th {
	background: #717983;
	font-size: 14px;
	color: #fff;
	text-align: left;
	padding-left: 15px;
}
.chat-table td {
	text-align: left;
	padding-left: 15px;
	background: #e0f8ff;
}
.chat-table td:nth-child(odd) {
	background: #e8f4d0;
}
.chat-detail {
	float: right;
	width: 30%;
	height: 230px;
	vertical-align: bottom;
}
.tips-box {
	height: 120px;
	background: #f5f3f3;
	border: 1px solid #b0b0b0;
	line-height: 25px;
	font-size: 14px;
	width: 100%;
	margin-top: 5px;
	margin-bottom: 8px;
}
.advise-box {
	height: 100px;
	background: #f5f3f3;
	border: 1px solid #b0b0b0;
	line-height: 25px;
	font-size: 14px;
	width: 100%;
	margin-top: 5px;
	margin-bottom: 8px;
}
.answer-info {
	width: 48%;
	height: 30px;
}
.answer-info .txt-box {
	height: 26px;
	line-height: 26px;
	text-indent: 5px;
	background: #f5f3f3;
	border: 1px solid #b0b0b0;
}
/*休息时间*/
.week-choice {
	padding-left: 36px;
	border-bottom: 1px solid #d7dbde;
	height: 34px;
	line-height: 34px;
}
.week-list {
	float: left;
	margin-right: 15px;
}
.holiday-list {
	padding-left: 36px;
	overflow: hidden;
	padding-top: 10px;
}
.holiday-list .holiday-table {
	width: 560px;
	overflow: hidden;
	background: #1487e6;
	margin: 0;
	margin-top: 10px;
	line-height: 36px;
	font-size: 14px;
	text-align: center;
}
.holiday-list .holiday-table th {
	background: #c0d5ee;
}
.holiday-list .holiday-table tr td {
	background: #eef8df;
	text-align: center;
}
.holiday-list .holiday-table tr:nth-child(odd) td {
	background: #d6efb8;
}
.long-btn {
	width: 130px;
	float: left;
	margin-top: 15px;
}
.list-delete {
	width: 18px;
	height: 20px;
	background: url(../Images/list-delete.png) no-repeat;
	border: 0;
}
/*越限参数设置*/
.setting-icon {
	border-bottom: 1px solid #d3d7da;
	text-align: left;
	padding-left: 40px;
	line-height: 34px;
	height: 34px;
	background: url(../Images/setting.png) 15px center no-repeat;
	background-color: #f1f1f1;
}
.ue-table th, .ue-table .table-panel.setting-table td {
	background: #f6f6f6;
}
.table-panel.setting-table tr:nth-child(even) td {
	background: #e3edfa;
}
.percent-input {
	width: 96px;
	height: 18px;
	border: 1px solid #b0b0b0;
	background: #f5f3f3;
}
.plus-icon {
	width: 20px;
	height: 20px;
	background: url(../Images/plus-icon.png) 0 center no-repeat;
	padding: 5px 10px;
	margin: 0 5px;
}
.plus-icon:hover {
	background: url(../Images/plus-icon-on.png) 0 center no-repeat;
}
.minus-icon {
	width: 20px;
	height: 20px;
	background: url(../Images/minus-icon.png) 0 center no-repeat;
	padding: 5px 10px;
}
.minus-icon:hover {
	background: url(../Images/minus-icon-on.png) 0 center no-repeat;
}
/*风格自定义*/

.theme_edit {
	width: 822px;
	overflow: hidden;
	line-height: 40px;
	font-size: 14px;
	text-align: left;
}
.theme_edit-save {
	width: 722px;
	margin-left: 110px;
	border: 0;
	text-align: center;
	margin-top: 10px;
	padding-top: 10px;
	border-top: 1px solid #b0b0b0;
}
.picLogo-text {
	height: 25px;
	background: #f5f3f3;
	border: 1px solid #b0b0b0;
	line-height: 25px;
	font-size: 14px;
	width: 380px;
	margin-top: 5px;
	margin-right: 8px;
}
.picLogo-file {
	height: 29px;
	background: url(../Images/upload-file.png) 0 center no-repeat;
	width: 84px;
}
#file {
	width: 0;
	height: 20px;
	margin-left: -154px;
*margin-left:-3px;
	filter: alpha(opacity=0);
	-moz-opacity: .0;
	opacity: 0.0;
	cursor: pointer;
}
.file-tips {
	line-height: 40px;
	font-size: 12px;
}
.theme-color-blue {
	width: 13px;
	height: 13px;
	background: #1487e6;
	display: inline-table;
	margin: 0 5px;
}
.theme-color-red {
	width: 13px;
	height: 13px;
	background: #00ae81;
	display: inline-table;
	margin: 0 5px;
}
.theme-color-orange {
	width: 13px;
	height: 13px;
	background: #ffcb00;
	display: inline-table;
	margin: 0 5px;
}
.theme-color-purple {
	width: 13px;
	height: 13px;
	background: #02bcd2;
	display: inline-table;
	margin: 0 5px;
}
.theme-color-green {
	width: 13px;
	height: 13px;
	background: #9d4efe;
	display: inline-table;
	margin: 0 5px;
}
.bg-theme0 {
	background-color: #1487e6;
}
.bg-theme1 {
	background-color: #c40b06;
}
.bg-theme2 {
	background-color: #ffcb00;
}
.bg-theme3 {
	background-color: #742574;
}
.header.bg-theme4 {
	background-color: #00ae81;
}
#oList li {
	float: left;
	margin-left: 20px;
}
.item_del {
	width: 20px;
	height: 20px;
	background: url(../Images/clear.png) 0px 2px no-repeat;
	display: inline-block;
}
.energy-type {
	padding: 0 7px;
	float: left;
	background: #f0f7fb;
	height: 27px;
	line-height: 27px;
	font-size: 14px;
	color: #4b4b4b;
	cursor: pointer;
	border: 1px solid #cae9fb;
}
.energy-type.L_radius {
	border-right: 0;
}
.energy-type.R_radius {
	border-left: 0;
}
.energy-type.on {
	background: #16a0e8;
	color: #fff
}
.auto-LopTap-reault {
	overflow: auto;
	width: 100%;
	height: 500px;
}
.auto-LopTap-reault2 {
	overflow: auto;
	width: 100%;
	height: 500px;
}
/*报表分页*/

.report-page-ul {
	height: 28px;
	padding: 0 10px;
	border-bottom: 1px solid #439feb;
}
.report-page-ul li {
	width: 128px;
	height: 27px;
	float: left;
	margin-left: 3px;
	border: 1px solid #439feb;
	border-bottom: 0;
	line-height: 28px;
	background: #fff;
	color: #439feb;
	-moz-border-radius: 3px 3px 0 0;      /* Gecko browsers */
	-webkit-border-radius: 3px 3px 0 0;   /* Webkit browsers */
	border-radius: 3px 3px 0 0;            /* W3C syntax */
	cursor: pointer;
}
.report-page-ul li.on {
	background: #439feb;
	color: #fff;
}
.report-main {
	padding: 0 24px;
	overflow: hidden;
}
.report-table {
	background: #fff;
	border: 3px solid #fff;
	overflow: hidden;
}
.next-page {
	position: absolute;
	right: 0;
	top: 48%;
}
.pre-page {
	position: absolute;
	left: 0;
	top: 48%;
}
#reportTable th {
	white-space: nowrap;
}
#reportTable td {
	white-space: nowrap;
}
/*蒙版*/
.show-on {
	z-index: 9999;
	position: relative;
}
.bg-black {
	filter: alpha(opacity=80);
	-moz-opacity: 0.8;
	-khtml-opacity: 0.8;
	opacity: 0.8;
	background: #000;
	width: 100%;
	height: 100%;
	position: absolute;
	z-index: 9999;
}
.masking-tips {
	position: absolute;
	display: none;
	z-index: 99999;
}
.masking-tips.company-tips-01 {
	right: 40px;
	top: 10px;/*display:inherit;*/
}
.company-index {
	width: 100%;
	height: 100%;
	position: relative;
	z-index: 9999;
}
.masking-tips.company-tips-02 {
	left: 15px;
	top: 80px;
}
.masking-tips.company-tips-03 {
	left: 335px;
	top: 140px;
}
.masking-tips.company-tips-04 {
	left: 590px;
	top: 60px;
}
.masking-tips.company-tips-05 {
	left: 880px;
	top: 55px;
}
.masking-tips.company-tips-06 {
	left: 15px;
	top: 350px;
}
.masking-tips.company-tips-07 {
	left: 305px;
	top: 350px;
}
.masking-tips.company-tips-08 {
	left: 590px;
	top: 350px;
}
.masking-tips.company-tips-09 {
	left: 880px;
	top: 380px;
}
.masking-tips.login-tips-01 {
	right: 50px;
	top: 245px;
}
.masking-tips.login-tips-02 {
	right: 250px;
	top: 260px;
}
.masking-tips.tree-tips {
	left: 130px;
	top: 380px;
}
.masking-tips.tree-tips02 {
	left: 150px;
	top: 90px;
}
.masking-tips.electrician_tips_01 {
	left: 130px;
	top: 380px;
}
.masking-tips.electrician_tips_02 {
	left: 130px;
	top: 130px;
}
.masking-tips.electrician_tips_03 {
	left: 530px;
	top: 300px;
}
.masking-tips.electrician_tips_04 {
	left: 750px;
	top: 250px;
}
.masking-tips.electrician_tips_05 {
	right: 200px;
	top: 520px;
}
.masking-tips.electrician_tips_06 {
	right: 30px;
	top: 610px;
}
.masking-tips.electrician_tips_07 {
	right: 520px;
	top: 120px;
}
.masking-tips.energy_tips_01 {
	right: 300px;
	top: 80px;
}
.masking-tips.energy_tips_02 {
	left: 80px;
	top: 90px;
}
.masking-tips.energy_tips_03 {
	left: 400px;
	top: 200px;
}
.masking-tips.energy_tips_04 {
	left: 700px;
	top: 380px;
}
.masking-tips.energy_tips_05 {
	left: 957px;
	top: 178px;
	width: 550px;
}
.masking-tips.energy_tips_05-click {
	left: 50%;
	top: 50%;
	margin-left: -188px;
	margin-top: -148px;
	width: 485px;
	height: 155px;
}
.masking-tips.energy_tips_05-click-next {
	left: 386px;
	top: 119px;
}
.masking-tips.electrician-index_tips_01 {
	left: 50px;
	top: 200px;/*display:inherit;*/
}
.masking-tips.electrician-index_tips_02 {
	right: 10px;
	top: 30px;
}
.masking-tips.electrician-index_tips_03 {
	right: 200px;
	top: 550px;
}
.masking-tips.user_change-tips {
	right: 350px;
	top: 90px;
}
.masking-tips.paper_tips_01 {
	left: 50%;
	top: 50%;
	margin-left: -450px;
	margin-top: -350px;
}
.masking-tips.paper_tips_bg {
	left: 50%;
	top: 50%;
	margin-left: -450px;
	margin-top: -350px;
	background: url(../Images/paper_tips_bg.png) no-repeat;
	width: 900px;
	height: 700px;
}
.top_left {
	position: absolute;
	top: 0;
	left: 50px;
}
.mid_left01 {
	position: absolute;
	top: 305px;
	left: 60px;
}
.mid_left02 {
	position: absolute;
	top: 305px;
	left: 290px;
}
.mid_left03 {
	position: absolute;
	top: 305px;
	left: 550px;
}
.mid_left04 {
	position: absolute;
	top: 305px;
	left: 180px;
}
.masking-tips.paper_tips_bg01 {
	left: 50%;
	top: 50%;
	margin-left: -450px;
	margin-top: -350px;
	background: url(../Images/paper_tips_bg02.png) no-repeat;
	width: 900px;
	height: 700px;
}
.cursur-point {
	position: absolute;
	top: 60px;
	right: 430px;
	z-index: 99999;
	display: none;
}
.cursur-point-index4 {
	right: 0;
	top: -50px;
	display: inherit;
}
.cursur-point-index2 {
	right: 170px;
	top: 65px;
}
.cursur-point-index3 {
	right: 0;
	top: -10px;
	display: inherit;
}
.login-tips-02 .cursur-point {
	position: absolute;
	top: 65px;
	right: -40px;
	z-index: 99999;
	display: inherit;
}
.masking-tips.user_change-tips .cursur-point {
	position: absolute;
	top: -10px;
	right: -10px;
	z-index: 99999;
}
.aaa {
	border: 5px solid #ff0000;
	position: absolute;
	top: 50px;
	right: 380px;
	width: 100px;
	height: 30px;
	background: none;
	z-index: 99999;
}
.company-index-datalist-top {
	border-bottom: 1px solid #edeff1;
	padding-bottom: 10px;
	padding-top: 71px;
	background: url(../Images/company-icon1.png) no-repeat center 10px;
	font-size: 14px;
	text-align: center;
}
.company-index-datalist-top .green-text {
	color: #2ed900;
	font-size: 20px;
}
.company-index-datalist-ul {
	width: 100%;
	height: 100px;
	font-size: 12px;
	text-align: center;
}
.company-index-datalist-ul li {
	float: left;
	width: 85px;
	height: 42px;
	border-right: 1px solid #edeff1;
	padding-top: 58px;
	line-height: 20px;
}
.company-index-datalist-ul li.last-border0 {
	border: 0;
}
.company-index-datalist-ul li.icon01 {
	background: url(../Images/company-icon2.png) no-repeat center 15px;
}
.company-index-datalist-ul li.icon02 {
	background: url(../Images/company-icon3.png) no-repeat center 15px;
}
.company-index-datalist-ul li.icon03 {
	background: url(../Images/company-icon4.png) no-repeat center 15px;
}
.company-index-datalist-ul li .green-text {
	color: #2ed900;
	font-size: 14px;
}
/*新页面班制、产品配置*/
.body_bgwhite {
	background: #fff;
}
.btn_add_icon {
	background: url(../Images/btn_add_icon.png) no-repeat 10px center;
}
.btn_delete_icon {
	background: url(../Images/btn_delete_icon.png) no-repeat 10px center;
}
.btn_common {
	height: 29px;
	padding: 0 10px;
	background-color: #1487e6;
	-moz-border-radius: 3px;      /* Gecko browsers */
	-webkit-border-radius: 3px;   /* Webkit browsers */
	border-radius: 3px;            /* W3C syntax */
	border: 0;
	color: #fff;
	font-family: "微软雅黑";
	padding-left: 35px;
	margin: 0 10px;
	cursor: pointer;
}
.btn_common.ml_0{ margin-left:0;}
.btn_common.add_list{}
.btn_common:hover {
	background-color: #0a4373;
}
.btn_noClick {
    background-color: gray !important;
}
.input5 {
	color: #4b4b4b;
	height: 27px;
	padding: 0;
	text-align: left;
	font-size: 14px;
	width: 150px;
	border: 1px solid #b0b0b0;
	padding-left: 4px;
	background: #f5f3f3;
	font-family: "微软雅黑";
}
.input5.w180 {
	width: 180px;
}
.search_condition .alR {
	text-align: right;
}
.p_h24_p43 {
	font-family: "微软雅黑";
	font-size: 14px;
	padding-left: 43px;
	height: 35px;
	line-height: 35px;
	text-align: left;
}

.team_table {
	width: 800px;
	background: #b0b0b0;
	font-family: "微软雅黑";
	font-size: 14px;
	color: #4b4b4b;
	margin-left: 30px;
	margin-top: 10px;
}
.team_table.w300{ width:300px; margin-top:26px;}
.team_table th {
	background: #f0f7fb;
	height: 34px;
	line-height: 34px;
	color: #4b4b4b;
	padding: 0 5px;
}
.team_table td {
	background: #fff;
	height: 40px;
	line-height: 40px;
	padding: 0 5px;
}
.blue_num {
	color: #1487e6;
	padding: 0 10px;
	font-size: 14px;
	font-weight: bold;
}
.blue_num.font_L{ float:left; width:25px; text-align:right;padding-right:5px;padding-left:0;}
.save_R_harf {
	width: 830px;
	text-align: right;
	margin-top: 10px;
}
.diff_second .save_R_harf{ width:920px;}
.table_L.w800 {
	width: 800px;
}
.point_choice_box {
	border: 1px solid #b0b0b0;
	padding: 0 4px;
	background: #f5f3f3;
	height: 162px;
    overflow-y: auto;
	width: 380px;
	padding: 9px 10px;
}
.rede_tips {
	color: #bf0000;
	font-size: 14px;
	font-weight: bold;
}
.point_list {
	background: #dbebf8;
	border: 1px solid #c2dff7;
	height: 20px;
	color: #4b4b4b;
	line-height: 20px;
	padding: 0 3px;
	float: left;
	margin: 0 5px;
}
.tips_blue_bg {
	padding: 10px;
	background: #eff5fb;
	margin-top: 10px;
	text-align: left;
	line-height: 20px;
}
.tips_icon {
	width: 16px;
	height: 16px;
	background: #fa9800;
	color: #fff;
	line-height: 16px;
	-moz-border-radius: 3px;      /* Gecko browsers */
	-webkit-border-radius: 3px;   /* Webkit browsers */
	border-radius: 3px;            /* W3C syntax */
	text-align:center;
}
.tcblue {
	color: #1487e6;
}
.lightGray{
    color: rgb(169, 166, 166);
}
.search_condition.border_bottom_top {
	border-bottom: 1px solid #d3d7da;
	border-top: 1px solid #d3d7da;
	padding: 10px 0;
	margin: 10px 0;
}
/*指标关联页面*/
.curveType_list {
	margin-left: 5px;
}
.R_list li{ text-align:left; padding-left:10px;}
.menuContent_position{ position:absolute; top:31px; left:3px;}
.menuContent_position.on{ border:1px solid #fa9800;}
.workshop_list{ background:#fffcf7;}
.workshop_list li:nth-child(even){ background:#fff6e7;}

/*日期前后查询*/
.prev_date{ float:left; width:19px; height:28px; background:url(../Images/prev_icon.png) no-repeat; margin-right:2px;}
.prev_date.disabled{background:url(../Images/prev_icon-disable.png) no-repeat;}
.prev_date.disabled2{background:url(../Images/prev_icon-disable.png) no-repeat;}
.next_date{ float:left; width:19px; height:28px; background:url(../Images/next_icon.png) no-repeat; margin-left:2px;}
.next_date.disabled{background:url(../Images/next_icon-disable.png) no-repeat;}
.next_date.disabled2{background:url(../Images/next_icon-disable.png) no-repeat;}
