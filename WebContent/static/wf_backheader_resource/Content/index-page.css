@charset "utf-8";
/* CSS Document */
body {
	background: #d1dbdd;
	height: 100%;
}
.chart_line {
	/*background: url(../Images/repeat_x_bg_index.png) bottom repeat-x;
	overflow: hidden;*/
	display: -webkit-flex;
	display: flex;
}
.chart_line .w50_percent {
	width: auto;
	float: left;
}
.chart_line .w50_percent.repeat_y_r {
	background: url(../Images/repeat_y_chat_bg_index.png) repeat-y right;
}
.chart_line .w50_percent.repeat_y_l {
	background: url(../Images/repeat_y_chat_bg_index.png) repeat-y left;
}
.h_title {
	-webkit-flex:1;
	flex:1;
	padding-left: 25px;
	font-weight: 100;
	font-size: 18px;
	color: #647581;
	line-height: 30px;
	height: 30px;
}
.chat_box {
	-webkit-flex:1;
	flex:1;
	min-width: 200px;
	height:250px;
	padding: 27px 20px 0px 20px;
	background: url(../Images/chat_bg_index.png);
	background-size: 100% 100%;
}
@media (min-width: 1366px){
	.t-bar{
		margin-top:50px;
	}
}
.t-bar{
	width: 100%;
	height: 30px;
	display:flex
}
.chat_box .chat_area {
	width: 100%;
	height: 185px;
}
.chat_box .chat_area1 {
	height: 160px;
}
.chat_box .chat_area2 {
	height: 155px;
	margin-top: 10px;
}
.span_no {
	font-size: 20px;
	font-weight: 400;
}
.progressbar_wapper {
	font-size: 16px;
	color: #7C828A;
}
.pl20 {
	padding-left: 20px;
}
.mt-4 {
	margin-top: -4px;
}
.mt5 {
	margin-top: 5px;
}
.ml8 {
	margin-left: 8px;
}
.f20px-green {
	font-size: 20px;
	color: #2ed900;
}
.fs {
	font-family: "Microsoft YaHei", "helvetica neue", arial, sans-serif;
}
.chat_box .chat-content {
	width: 240px;
	margin: 0 auto;
	overflow: hidden;
}
.type-energy-data {
	margin-left: 35px;
	height: 24px;
	line-height: 24px;
	color: #7C828A;
}
.type-energy-data2 {
	margin-left: 8px;
	height: 18px;
	line-height: 18px;
	color: #7C828A;
}
.type-energy-data-month {
	margin-left: 8px;
	overflow: hidden;
	line-height: 35px;
	color: #7C828A;
}
.h24 {
	height: 24px;
	line-height: 24px;
}
.tc-green {
	color: #2ed900;
	font-size: 14px;
}
.type-bg {
	width: 24px;
	height: 16px;
	border-radius: 5px;
	background-color: #d3dbdd;
	color: #647581;
	border-radius: 15px;
	text-align: center;
	line-height: 16px;
}
.p1 {
	width: 100%;
	height: 60px;
	border-radius: 5px;
	background-color: #d3dbdd;
}
.p2 {
	width:100%;
	height: 28px;
	margin-bottom: 25px;
	margin-top: 5px;
	border-radius: 15px;
	background-color: #d3dbdd;
}
.p3 {
	width: 205px;
	height: 16px;
	margin-left: 10px;
	border-radius: 15px;
	background-color: #d3dbdd;
}
#progressbar1 .ui-widget-header2 {
	background-color: #2ed900;
	max-width: 100%;
}
#progressbar2 .ui-widget-header2 {
	background-color: #14a7e8;
}
#progressbar3 .ui-widget-header2 {
	background-color: #2ed900;
	border-radius: 15px;
	max-width: 100%;
}
#progressbar4 .ui-widget-header2 {
	background-color: #14a7e8;
	border-radius: 15px;
}
.bg-green .ui-widget-header2 {
	background-color: #8dc913;
}
.bg-blue .ui-widget-header2 {
	background-color: #14a7e8;
}
.bg-blue2 .ui-widget-header2 {
	background-color: #0005cd;
}
.bg-green2 .ui-widget-header2 {
	background-color: #f7ae10;
}
.ui-widget-header2 {
	border-radius: 15px;
	height: 16px;
	max-width: 100%;
}
.t1 {
	color: #7c828a;
	margin-left: 10px;
	font-size: 14px;
}
.center {
	text-align: center;
}
.subtext {
	color: #14a7e8;
	margin-left: 20px;
	height: 15px;
}
#assessmentWrapper {
	width: 90px;
	position: absolute;
	right: 0px;
	cursor: pointer;
	margin-top: 33px;
	margin-right: 9px;
}
#assessmentWrapper .assessment_score {
	position: absolute;
	top: 28px;
	right: 32px;
	font-size: 29px;
}
/* 首页地图--样式 begin */
div.mapWrap {
	height: 564px;
}
.mapWrap .mapWrap_1 {
	position: relative;
	float: left;
	width: 400px;
	height: 100%;
}
.mapWrap .mapWrap_1 #allmap {
	width: 100%;
	height: 100%;
}
.mapWrap .mapWrap_1 #addXy {
	position: absolute;
	bottom: 45px;
	left: 15px;
	cursor: pointer;
	width: 151px;
	height: 52px;
	background: url(../Images/addmappoint_1.png);
}
.mapWrap .mapWrap_1 .map_search {
	width: 350px;
	position: absolute;
	top: 11px;
	left: 90px;
}
.mapWrap .mapWrap_1 .map_search .auto-input {
	height: 19px;
	padding: 5px 0px 5px 5px;
	font-size: 14px;
	float: left;
	border-left: 0px;
}
.mapWrap .mapWrap_1 .map_search #search-map {
	background: url(../Images/searchbox_f175577.png) no-repeat -4px -79px #3385ff;
	width: 50px;
	height: 31px;
	float: left;
	margin-top: -28px;
	border: 0;
	cursor: pointer;
	-moz-border-radius: 0 4px 4px 0;      /* Gecko browsers */
	-webkit-border-radius: 0 4px 4px 0;   /* Webkit browsers */
	border-radius: 0 4px 4px 0;            /* W3C syntax */
	box-shadow: 1px 2px 1px rgba(0,0,0,0.15);
}
.mapWrap .mapWrap_1 .map_search #searchModel {
	width: 40px;
	float: left;
	font-size: 14px;
	height: 31px;
	-moz-border-radius: 4px 0px 0px 4px;      /* Gecko browsers */
	-webkit-border-radius: 4px 0px 0px 4px;   /* Webkit browsers */
	border-radius: 4px 0px 0px 4px;            /* W3C syntax */
}
.mapWrap .mapWrap_1 .message_select {
	font-size: 14px;
	background-color: rgb(249, 253, 255);
	padding: 3px 5px 3px 5px;
	-moz-border-radius: 4px;      /* Gecko browsers */
	-webkit-border-radius: 4px;   /* Webkit browsers */
	border-radius: 4px;            /* W3C syntax */
	box-shadow: 3px 3px 4px rgba(0,0,0,0.15);
	position: absolute;
	top: 11px;
	right: 125px;
}
.mapWrap .mapWrap_1 .opacity, div.BMap_noprint {
	filter: alpha(Opacity=85);
	-moz-opacity: 0.85;
	opacity: 0.85;
}
.mapWrap .mapWrap_1 div.anchorTR {
}
.mapWrap .mapWrap_1 div.anchorTR div div {
	width: 30px;
	text-align: right !important;
	padding: 4px 4px 3px 4px !important;
	font-size: 14px !important;
}
.mapWrap .mapWrap_1 .sou_fan {
	cursor: pointer;
	position: absolute;
	right: 0;
	top: 45%;
	width: 18px;
	height: 50px;
	font-size: 20px;
	line-height: 47px;
	background-color: #b4b5bf;
	color: #666666;
	-moz-border-radius: 4px 0px 0px 4px;      /* Gecko browsers */
	-webkit-border-radius: 4px 0px 0px 4px;   /* Webkit browsers */
	border-radius: 4px 0px 0px 4px;            /* W3C syntax */
	box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.15);
}
.mapWrap .mapWrap_2 {
	float: left;
	width: 215px;
	height: 100%;
	background: #fff;
}
.mapWrap .mapWrap_2 .mapWarp_right {
	width: 210px;
	padding: 2px;
	overflow: hidden;
}
.mapWrap .mapWrap_2 .mapWarp_right .total_title {
	width: 210px;
	color: #1487e6;
	font-size: 14px;
	text-align: center;
	line-height: 32px;
	height: 32px;
	background: #abd9ff;
}
.map_numlist {
	width: 200px;
	padding: 5px;
	padding-top: 10px;
	margin: 0 auto;
	border-top: 1px solid #dbdada;
	margin-top: 1px;
	overflow: hidden;
	font-weight: bold;
}
.map_numlist li {
	border-bottom: 1px solid #e5e6e9;
	overflow: hidden;
	padding-bottom: 5px;
}
.aa {
	font-family: LED;
	font-size: 30px;
}
.LED-text-bg {
	width:198px;
	height: 36px;
	color: #45515c;
	-moz-border-radius: 3px;      /* Gecko browsers */
	-webkit-border-radius: 3px;   /* Webkit browsers */
	border-radius: 3px;            /* W3C syntax */
	font-size: 34px;
    line-height: 36px; letter-spacing:1px; font-weight:bold;
	color: #89caff; font-family:Arial, Helvetica, sans-serif;
	background: #4d5a67; border:1px solid #3e3e47;
    padding-right:2px;
    text-align: right;
}
.LED-text-bg-short {
	width:98px;
	height: 36px;
	color: #45515c;
	-moz-border-radius: 3px;      /* Gecko browsers */
	-webkit-border-radius: 3px;   /* Webkit browsers */
	border-radius: 3px;
	font-size: 34px;font-family:Arial, Helvetica, sans-serif;
	line-height: 36px;letter-spacing:1px;
	color: #89caff;font-weight:bold;
	background: #4d5a67; border:1px solid #3e3e47;
    padding-right:2px;
    text-align: right;
}
.icon-energy li {
	padding: 7px 0;
	height: 24px;
	line-height: 24px;
	font-size: 14px;
	color: #666666;
	text-align: left;
}
.icon-energy-water {
	padding-left: 35px;
	background: url(../Images/icon-energy.png) 5px 0 no-repeat;
}
.icon-energy-gas {
	padding-left: 35px;
	background: url(../Images/icon-energy.png) 5px -48px no-repeat;
}
.icon-energy-hot {
	padding-left: 35px;
	background: url(../Images/icon-energy.png) 5px -95px no-repeat;
}
.icon-energy li .num-green {
	color: #2ed900;
	font-size: 16px;
	padding-right: 10px;
}
.icon-energy li .num-unit {
	font-size: 12px;
}
/* 首页地图--样式 end */
