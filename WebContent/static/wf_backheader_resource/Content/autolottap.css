@charset "utf-8";
/* CSS Document */
.autoLotTap{width:242px;}
.autoLotTap2{height:auto; width:auto; margin:4px;}


/*联动下拉组件选择显示区域*/
.autoLotTap-show{padding:0px;width:140px; height:28px;text-align: left;}
.autoLotTap-show2{width: auto; height:auto; border: 1px solid #292e33}

.autoLotTap-show p{
	margin-bottom:1px;
	padding:1px 2px;
	height:18px; 
	color:#006b94;
	border:1px solid #d6ebff; 
	background:#f4faff; 
}

.autoLotTap-show p .close{
	width:12px;
	height:12px;
	margin-top:3px;
	cursor:pointer;
	background:url(../Images/delete_12x12.png) no-repeat;
}

.autoLotTap-show p .close:hover{
	background:url(../Images/delete_hover_12x12.png) no-repeat;
}

.autoLotTap-show input{
	width:130px; 
	height:26px;
	line-height:26px;
	padding-left:5px;
	border:1px solid #b0b0b0;
}
.autoLotTap-show .tree-input{background: #4d5a67; border: 1px solid #6a7580; width: 166px; height:24px; padding-left:25px; line-height:25px; color:#8a9096;}
	
/*数据显示区域*/
.autoLotTap-main{
	display:none;
	position:absolute;
	padding:0px 0px 0px 0px;
	width:135px;
	border:1px solid #c1d3dc;
	border-top:0;
	background:#fff; font-weight:100; 
	font-size:12px !important; z-index:100000;
}
.tree-autoLotTap .autoLotTap-main{ width:193px; text-align:left;} 
.autoLotTap-main .autoLotTap-main-choose{ overflow-y:auto; height:120px;overflow-x: hidden;}
.autoLotTap-main li{
	height:24px;
	padding:0 5px;
	line-height:24px;
	cursor:default;
	border:1px solid #fff; white-space:nowrap;
}

.autoLotTap-main li:hover{
	color:#00afff;
	border:1px solid #d6ebff;
	background:#f4faff;
}

/*分页区域*/
.autoLotTap-main-paging{
	bottom:0px;
	left:0px;
	padding:3px;
	width:129px;
	color:#000;
	border-top:1px solid #d6f7ff;
}

.autoLotTap-main-paging .prev,.autoLotTap-main-paging .next{
	display:inline-block;
	width:15px;
	height:13px;
	margin:3px 5px;
	cursor:pointer;
	background:url(../Images/prev_15x13.png) no-repeat left top;
}

.autoLotTap-main-paging .next{
	background:url(../Images/next_15x13.png) no-repeat left top;
}
