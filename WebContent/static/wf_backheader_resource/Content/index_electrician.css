@charset "utf-8";
/* CSS Document */

body {
	background:#f7f7f7;
	height:100%;
}
p {
	margin:0 auto;
	padding:0 auto;
}
ul, li {
	padding:0;
	margin:0;
	list-style:none;
}
.fn-text-overflow {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
a:hover {
	color: #e66e1e;
	text-decoration: none;
}
a {
	color: #495760;
	text-decoration:none;
}
.hidden {
	display:none;
}
.iconfont {
	cursor: default;
	font-family: "rei";
	font-style: normal;
	font-weight: normal;
}
.top_main_bg {
	height:400px;
	background:#3a4e5f;
	padding:20px 10px;
}
.top_main_bg .chart_event {
	width:1240px;
	margin:0 auto;
	height:400px;
}
.top_main_bg .chart_event .chart_L {
	width:870px;
	height:400px;
	background:#fff;
/*	border-radius:3px;*/
	float:left;
}
.chart_ul {
	width:60px;
	height:400px;
	background:#dfe9f2;
	border-left:4px solid #4ea5ec;
	float:right;
/*	border-top-right-radius:3px;
	border-bottom-right-radius:3px;*/
}
.chart_ul li {
	height:99px;
	border-bottom:1px solid #8c9ba8;
	display: block;
	text-align:center;
	cursor:default;
}
.chart_ul li.on {
	background:#4ea5ec;
}
.chart_ul li:nth-child(1) {
/*border-top-right-radius:3px;*/
}
.chart_ul li:nth-child(4) {
height:100px;
 border-bottom:0;
/*border-bottom-right-radius:3px;*/
}
.chart_ul li p {
	display: inline-block;
	vertical-align: middle;
	color:#495760;
	font-size:16px;
	background:url(../Images/chat_icon01.png) no-repeat top center;
	padding-top:25px;
	margin-top:28px;
}
.chart_ul li p.list_icon1 {
	font-size:12px;
}
.chart_ul li p.list_icon2 {
	background:url(../Images/chat_icon02.png) no-repeat top center;
	font-size:12px;
}
.chart_ul li p.list_icon3 {
	background:url(../Images/chat_icon03.png) no-repeat top center;
	padding-top:28px;
	font-size:12px;
}
.chart_ul li p.list_icon4 {
	background:url(../Images/chat_icon04.png) no-repeat top center;
	font-size:12px;
}
.chart_ul li.on p {
	background:url(../Images/chat_icon01-on.png) no-repeat top center;
	color:#fff;
}
.chart_ul li.on p.list_icon2 {
	background:url(../Images/chat_icon02-on.png) no-repeat top center;
}
.chart_ul li.on p.list_icon3 {
	background:url(../Images/chat_icon03-on.png) no-repeat top center;
}
.chart_ul li.on p.list_icon4 {
	background:url(../Images/chat_icon04-on.png) no-repeat top center;
	font-size:12px;
}
.chart_list {
	overflow:hidden;
	float:left;
	width:800px;
	height:400px;
}
.chart_list .real-time_data {
	height:38px;
	padding-left:15px;
	color:#666;
	line-height:38px;
}
.chart_list .real-time_data-chart {
	width:794px;
	margin-left:6px;
	height:354px;
}
.top_main_bg .chart_event .event_list {
	width:350px;
	float:right;
	background:#f4f4f4;
	height:400px;
	/*border-radius:3px;*/
}
.issue-cat-list-wrap {
	width:1240px;
	height:200px;
	margin:0 auto;
	margin-top:10px;
	position: relative;
}
.issue-cat-list-wrap .issue-cat-list {
	position: relative;
	width: 1275px;
}
.issue-cat-list-wrap .issue-cat-list::before, .issue-cat-list-wrap .issue-cat-list::after {
 content: " ";
 display: table;
}
.issue-cat-list-wrap .issue-cat-list::after {
 clear: both;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item {
	background-color: #ffffff;
	border: 2px solid #7fc5ad;
	display: block;
	float: left;
	height: 110px;
	margin-bottom: 10px;
	margin-right: 20px;
	width: 400px;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item.issue-cat-blue {
	border-color:#8cc3e6;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item.issue-cat-red {
	border-color:#e17f80;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item.issue-cat-violet {
	border-color:#d58bdf;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item.issue-cat-orange {
	border-color:#f89465;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item.issue-cat-green {
	border-color:#6ac1c6;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item .headline {
	background-color: #7fc5ad;
	color: #ffffff;
	display: block;
	float: left;
	font-size: 16px;
	height: 110px;
	line-height: 110px;
	text-align: center;
	width: 70px;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item .headline p {
	display: inline-block;
	line-height: 20px;
	margin:0;
	padding:0;
	vertical-align: middle;
	margin-top:22px;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item.issue-cat-blue .headline {
	background-color: #8cc3e6;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item.issue-cat-red .headline {
	background-color: #e17f80;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item.issue-cat-violet .headline {
	background-color: #d58bdf;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item.issue-cat-orange .headline {
	background-color: #f89465;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item.issue-cat-green .headline {
	background-color: #6ac1c6;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item .headline p img {
	margin-bottom:3px;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item .cat {
	position:relative;
	width:330px;
	float:left;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item .cat ul {
	padding-top:10px;
	font-size:14px;
	list-style:none;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item .cat ul.more_data {
	height:135px;
	background:#fff;
	position:absolute;
	top:0;
	left:-2px;
	z-index:1000;
	border:2px solid #e17f80;
	border-top:0;
	width:330px;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item .cat ul li {
	float:left;
	padding-left:25px;
	width:140px;
	margin-top:17px;
	background:url(../Images/list_bg_l.png) 15px center no-repeat;
	text-align:left;
}
.issue-cat-list-wrap .issue-cat-list .issue-cat-item .cat ul li:hover {
	background:url(../Images/list_bg_l-on.png) 15px center no-repeat;
}
.issue-cat-item .cat ul li .iconfont {
	font-size: 12px;
	margin-right: 5px;
}
.event-title {
	height:38px;
	background:#f4f4f4;/*
	border-top-left-radius:3px;
	border-top-right-radius:3px;*/
	line-height:38px;
	padding-left:15px;
	color:#666;
}
.event-list-table {
	width:100%;
	overflow:hidden;
	font-family:"宋体";
	font-size:12px;
}
.ue-table .table-panel .event-list-table thead tr th {font-family:"宋体";
	line-height:38px; text-align:center;
	background:#ebeff8;
	color:#666666;
	font-weight:100;
	border-top:1px solid #d4d4d4;
}
.event-list-table tbody tr td {
	line-height:39px;
	border-top:1px solid #e8e8e9;
	background:#fff;
	text-align:center;
}
.event-list-table tbody tr:nth-child(even) td {
background:#f4f4f4;
}


.errorPanel-event{ height:331px; text-align:center;
/*	border-bottom-left-radius:3px;
	border-bottom-right-radius:3px;*/}
.errorPanel-event .noData{ text-align:center;}
.errorPanel-event .noData .event-tips{ margin-top:100px;}


/*本月能耗总览*/
.month-energy-data{ width:1220px; height:90px; padding:5px 10px; background:#fff; margin:0 auto; margin-top:-14px;}
.title_h-month{ font-size:16px; color:#495760; font-weight:100; text-indent:5px;}
.month-data-list{ height:61px; margin-top:5px;}
.month-data-list li{ width:237px; height:55px; float:left; background:url(../Images/month-energy-data-bg.png) no-repeat; padding-top:6px; margin-right:7px;}
.month-data-list li .data-content{ width:85px; height:34px; border-right:1px solid #8cc3e6; float:left; padding:5px 0; padding-left:45px; line-height:18px;}
.month-data-list li .data-content.icon1{ background:url(../Images/data-icon01.png) 8px center no-repeat;}
.month-data-list li .data-content.icon2{ background:url(../Images/data-icon04.png) 8px center no-repeat;}
.month-data-list li .data-content.icon3{ background:url(../Images/data-icon02.png) 8px center no-repeat;}
.month-data-list li .data-content.icon4{ background:url(../Images/data-icon03.png) 8px center no-repeat;}
.month-data-list li .data-content.icon5{ background:url(../Images/data-icon05.png) 8px center no-repeat;}
.month-data-list li .num-title{ font-size:12px;}
.month-data-list li .num-title .f12px{ font-size:11px;}
.data-up{ height:38px; padding-left:30px; background:url(../Images/data-up.png) no-repeat; color:#e17f80; line-height:38px; font-weight:bold; font-size:14px; margin-right:8px; margin-top:3px;}
.data-down{ height:38px; padding-left:30px; background:url(../Images/data-down.png) no-repeat; color:#6ac1c6; line-height:38px; font-weight:bold; font-size:14px; margin-right:8px; margin-top:3px;}