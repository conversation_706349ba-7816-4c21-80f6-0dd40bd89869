/*****瞬间提示*****/
/*瞬间消失的提醒提示*/
.ue-minute-warn{
	background-color:#fff;
}
.ue-minute-warn .tipL {
	float:left;
	width:3px;
	height:60px;
	background: url(../Images/warn_l.png);
}
.ue-minute-warn .tipC {
	float: left;
	height: 60px;
	line-height:60px;
	font-size:16px;
	text-align:center;
	font-weight:bold;
	color:#fff;
	padding:0 50px 0 74px;
	background:url(../Images/warn.png) #f99800 30px center no-repeat;
}
.ue-minute-warn .tipR {
	float:right;
	width:3px;
	height:60px;
	background: url(../Images/warn_r.png);
}

/*瞬间消失的成功提示*/
.ue-minute-success{
	background-color:#fff;
}
.ue-minute-success .tipL{
	float:left;
	width:3px;
	height:60px;
	background: url(../Images/ok_l.png);
}
.ue-minute-success .tipC{
	float: left;
	min-width: 170px;
	height: 60px;
	line-height:60px;
	font-size:16px;
	text-align:center;
	font-weight:bold;
	color:#fff;
	padding:0 50px 0 80px;
	background:url(../Images/ok.png) #23c608 30px center no-repeat;
}
.ue-minute-success .tipR{
	float:right;
	width:3px;
	height:60px;
	background: url(../Images/ok_r.png);
}



/*瞬间消失的提醒提示*/
.ue-minute-error{
	background-color:#fff;
}
.ue-minute-error .tipL{
	float:left;
	width:3px;
	height:60px;
	background: url(../Images/error_l.png);
}
.ue-minute-error .tipC{
	float: left;
	min-width: 170px;
	height: 60px;
	line-height:60px;
	font-size:16px;
	text-align:center;
	font-weight:bold;
	color:#fff;
	padding:0 50px 0 80px;
	background:url(../Images/ok.png) #fa392a 30px center no-repeat;
}
.ue-minute-error .tipR{
	float:right;
	width:3px;
	height:60px;
	background: url(../Images/error_r.png);
}