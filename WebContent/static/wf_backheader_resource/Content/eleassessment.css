@charset "utf-8";
/* CSS Document */

/* 进入用电评估弹出样式 */
.assessment_page .assessment_up{
    height: 280px;
    background-color: #1487e6;
}
.assessment_page .assessment_down{
    height: 120px;
    background-color: #106ebb;
}
.up_left{
    position: absolute;
    width:336px;
    float: left;
}
.up_left .total_star{
    padding-top: 30px;
}
.up_left .total_score{
    margin-top: 12px;
}
.up_left .total_score .num_bg{
    position: absolute;
    left: 82px;
}
.up_left .total_score .score_num{
    font-family: Myriad;
    color: #026f9d;
    text-align: center;
    font-size: 70px;
    position: absolute;
    top: 100px;
    left: 125px;
}
.up_left .total_score .score_num #score_value{
    float: left;
}
.up_left .total_score .score_num .score_fen{
    font-size: 25px;
    float: left;
    margin-top: 45px;
}
.up_right{
    position: absolute;
    right: 0px;
    width: 350px;
    float: left;
    padding-top: 90px;
}
.up_right .last_use{
    height: 30px;
    font-weight: bold;
    font-size: 17px;
    color: #ffffff;
}
.up_right .nowUseWrap{
    cursor: pointer;
    position: absolute;
    right: 50px;
}
.up_right .click_use{
    margin-top: 15px;
}
.up_right .now_use {
    font-size: 25px;
	font-weight: bold;
	color: #ffffff;
	position: absolute;
	top: 30px;
	right: 56px;
}
.up_bottom{
    position: absolute;
    top: 247px;
    display: none;
}
.up_bottom .scan_data{
    position: relative;
    bottom: 9px;
    right: -160px;
}
.assessment_down .part_1, .assessment_down .part_2, .assessment_down .part_3, .assessment_down .part_4{
    width: 152px;
    float: left;
    margin-top: 10px;
}
.assessment_down .part_word{
    font-size: 13px;
    color: #ffffff;
}

/* 用电评估--查看详情样式 */
.assessment_detail .detail_total {
    width: 650px;
    height: 88px;
    background-color: #1487e6;
}
.assessment_detail .detail_wrap {
    width: 650px;
    height: 312px;
    background-color: #d7e3f1;
}
.detail_total .total_1{
    width: 0px;
    float: left;
}
.total_1 .total_img{
    cursor: pointer;
}
.total_2 span{
    color: #ffffff;
}
.total_2 .detail_word{
    font-size: 24px;
}
.total_2 #dTotalScore{
    font-size: 40px;
}
.total_2 .detail_unit{
    font-size: 20px;
}
.detail_total .total_3{
    float: left;
    margin-left: 10px;
    margin-top: 3px;
}
.total_3 .detail_explain{
    font-size: 14px;
    color: #ffffff;
}
.detail_wrap .section1, .detail_wrap .section2, .detail_wrap .section3, .detail_wrap .section4{
    width: 311px;
    height: 158px;
    float: left;
}
.detail_wrap .section1{
    border-right:1px dashed rgb(166,166,166);
    border-bottom:1px dashed rgb(166,166,166);
}
.detail_wrap .section2{
    border-bottom:1px dashed rgb(166,166,166);
}
.detail_wrap .section3{
    border-right:1px dashed rgb(166,166,166);
}
.section_str{
    width: 110px;
    padding-right: 3px;
    margin-top: 30px;
    margin-left: 5px;
    float: left;
    background:url(../Images/line.png) right no-repeat;
}
.section_word{
    float: left;
    text-align: initial;
    width: 180px;
    margin-top: 30px;
    margin-left: 10px;
}
.section_word div{
    color: #000000;
    font-size: 14px;
    text-align: left;
}
.section_word div .section_name{
    height: 18px;
    width: 100px;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.section_word div .section_score{
    font-size: 18px;
    font-style:italic;
}
.section_word div .section_link{
    color: #0f01be;
}
.section_word div .section_fen{
    color:#1487e6;
}
.section_str .part_name{
    font-size: 15px;
}
a.section_link{
    display: none;
}