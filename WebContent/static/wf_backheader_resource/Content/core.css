@charset "utf-8";
@import url("table.css");
@import url("pagination.css");
@import url("ui.css");
@import url("tip.css");
/*css reset*/
/*html, body, div, span, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, code, del, dfn, em,q, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {
	margin:0;
	padding:0;
	border:0;
	font-weight:inherit;
	font-style:inherit;
	font-size:100%;
	font-family:inherit;
	vertical-align:baseline;
}*/
body {line-height:1.5;}
/*table {border-collapse: collapse;border-spacing:0;}*/
/*caption, th, td {text-align:left;font-weight:normal;}
table, td, th {vertical-align:middle;}	*/
blockquote:before, blockquote:after, q:before, q:after {content:"";}
blockquote, q {quotes:"" "";}
a img {border:none;}


body { font: 12px/1.5 'Microsoft YaHei';color:#3C3C3C;}
h1, h2, h3, h4, h5, h6 {color:#111; }
a {text-decoration:none;cursor:pointer;}
dl, dt, dd, ol, ul, li{ list-style:none;}

/*some common class*/
.ue-hidden{ position: absolute; left: -99999999px; }
.left{float:left;}
.right{float:right;}

/*clear*/
.ue-clear:after{content: ".";display:block;height:0;clear:both;visibility:hidden;}
.ue-clear{display:inline-block;}
*html .ue-clear{height:1%;}
.ue-clear{display:block;}

/*
.ue-font-normal{font-size:14px;}*/
/*通用a的样式*/
a{color:#1487e6;}
a:hover{text-decoration:underline;}

.ue-button{height:22px;*padding-top:1px;text-align:center;line-height:22px !important;font-size:12px !important;color:#0079d7;cursor:pointer;border:0;background:url(../Images/buttons.png);}
.ue-button:hover,.ue-button.hover{color:#428eca;background:url(../Images/buttons_hover.png);}
.ue-button.active{color:#428eca;background-image:url(../Images/buttons_active.png);}
.ue-button.long2{width:44px;background-position:0 0;}
.ue-button.long3{width:57px;background-position:-44px 0;}
.ue-button.long4{width:70px;background-position:-101px 0;}
.ue-button.long5{width:83px;background-position:-171px 0;}
.ue-button.long6{width:96px;background-position:-254px 0;}


.ue-button-normal{width:85px;height:33px;text-align:center;line-height:33px !important;font-size:14px !important;color:#0079d7;cursor:pointer;border:0;background:url(../Images/buttons_normal.png) -55px 0;}
.ue-state-hover.ue-button-normal,.ue-button-normal:hover{color:#428ECA;background:url(../Images/buttons_normal_hover.png) -55px 0;}
.ue-button-normal.active{color:#428ECA;background:url(../Images/buttons_normal_active.png) -55px 0;}

.ue-button-normal.long2{width:55px;background-position:0 0;}
.ue-button-normal.long3{width:85px;background-position:-55px 0;}
.ue-button-normal.long4{width:115px;background-position:-140px 0;}


.ue-button-ico{display:inline-block;height:24px;font-size:12px !important;cursor:pointer;}
.ue-button-ico:hover,.ue-button-ico.hover{
    background:#a1cff5;
    -moz-border-radius: 8px;      /* Gecko browsers */
    -webkit-border-radius: 8px;   /* Webkit browsers */
    border-radius:8px;            /* W3C syntax */
}
.ue-ico-on{
    background:#fdd699;
    -moz-border-radius: 8px;      /* Gecko browsers */
    -webkit-border-radius: 8px;   /* Webkit browsers */
    border-radius:8px;            /* W3C syntax */
}
.ue-button-ico.long2{width:60px;}
.ue-button-ico.long3{width:67px;}
.ue-button-ico.long4{width:79px;}
.ue-button-ico.long5{width:91px;}
.ue-button-ico.long6{width:103px;}

.ue-button-ico.long2down{width:60px;}
.ue-button-ico.long3down{width:84px;}
.ue-button-ico.long4down{width:96px;}
.ue-button-ico.long5down{width:108px;}

.ue-button-ico>span{float:left; line-height:24px;*line-height:24px;}
.ue-button-ico>span.table-icon{margin:1px 0 0 5px;}
.ue-button-ico>span.table-icon.arrow{margin:4px 0 0 6px;}


.ue-icos-panel{height:20px;background:#6CA1CC;}
.ue-icos-panel a{height:12px;padding:0 5px;margin-top:4px;overflow:visible;border-left:1px solid #82AFD4;border-right:1px solid #5C89AE;}
.ue-icos-panel a.first{border-left:0;}
.ue-icos-panel a.last{border-right:0;}
.ue-icos-panel a span{float:left;margin-top:-2px;cursor:pointer;}


 