@charset "utf-8";
/* CSS Document */
html,body{background-color:#fff;font:12px "微软雅黑"; height:100%;margin: 0; padding: 0;}
img{ border:0;}
.login_page{ width:100%; min-height: 100%;height: auto !important;height: 100%;}
.login_logo{ float:left;margin-left:86px; width:475px; height:60px;padding-top:50px;}
*{ margin:0; padding:0;}.L{ float:left}.R{ float:right;}
.login_bg{ height:410px; padding-bottom:90px;width:100%;margin-top:13px; float:left; background:url(../Images/login_bg_02.png) center center no-repeat;}
.login_main{ width:1015px;height:465px; margin:0 auto; position:relative; }
.login_main .login_topic{ width:586px; height:464px; background:url(../Images/login_tv_bg.png) no-repeat; float:left; position:relative;margin-top:35px;}
.topic_title{ margin-top:53px;width:382px; margin-left:150px; color:#6d6d6d; font-size:24px; font-family:"微软雅黑"; text-align:center; }
.topic_content{ height:180px; width:382px; margin-left:150px; margin-top:10px;  color:#444443;font-size:15px; line-height:28px; font-family:"微软雅黑"; display:none;text-align: left;}
.topic_white_bg{ width:274px; height:296px; position:absolute; right:22px; top:24px; background:url(../Images/white_bg_tv.png) no-repeat;}
.login{ width:289px; height:310px; float:right; background:#fff; -moz-border-radius: 3px;      /* Gecko browsers */
    -webkit-border-radius:3px;   border-radius:3px; border:1px solid #fff; margin-top:55px; padding:18px;}
.login_title{ height:50px; line-height:50px; font-size:24px; font-family:"黑体"; font-weight:bold; color:#16a0e8; border-bottom:1px solid #dfdfdf; text-align:center;}
.input_box{ width:246px; overflow:hidden; margin:0 auto;}
.input_name{ padding-left:49px; margin:0 auto; margin-top:2px; width:195px; height:33px; line-height:33px; background:url(../Images/input_name_bg.png) center center no-repeat; border:1px solid #ced6dc; color:#ced6dc;font-family:"微软雅黑"; }
.input_pass{ padding-left:49px; margin:0 auto; margin-top:17px; width:195px; height:33px; line-height:33px; background:url(../Images/input_password_bg.png) center center no-repeat; border:1px solid #ced6dc; color:#ced6dc;font-family:"微软雅黑"; }
.login_sel_list{ margin:0 auto; margin-top:17px; width:245px; line-height:33px;border:1px solid #ced6dc;font-family:"微软雅黑";font-size: 14px;}
.login_sel_list .tag_options{width:245px}
.onfocus_text{ color:#333; border:1px solid #a2abb3; }
.login_btn{ width:120px; height:42px; line-height:42px; background:url(../Images/login_button.png) no-repeat; color:#fff; font-family:"微软雅黑"; font-size:16px; border:0; letter-spacing:3px; margin-top:20px;cursor:pointer;}
.checkbox{height:13px; line-height:13px; vertical-align:middle; margin-right:3px;  }
.auto_login{ width:256px; height:23px; border-top:1px solid #dfdfdf; color:#545351; text-align:left;padding-left:30px;font-family:"微软雅黑"; margin-top:17px; line-height:30px;}
.forget_pass{ width:102px;height:23px; border-top:1px solid #dfdfdf; color:#545351; text-align:center; text-decoration:underline;font-family:"微软雅黑"; margin-top:17px; line-height:30px;}
.errortxt{ padding-left:18px; height:16px; line-height:16px; color:#666563; text-align:left; margin-top:3px; margin-left:20px;font-family:"微软雅黑";}
.errortxt.bg_show{background:url(../Images/error_bg.png) no-repeat; }

.footer { position: relative; margin-top: -90px;height:70px;clear:both;padding:10px 0px;}
.footer_wrap{width:1000px; margin: 0px auto; height: 100%;}
.footer_nav{float:left;padding-top: 20px; color:#666666;}
.footer_nav ul li{list-style: none outside none;height: 20px;line-height: 20px;}
.footer_nav ul li a{color:#666666;text-decoration: none;}
.footer_nav ul li a:hover{color:#008dd2;}
.app_warp {float:right;width: 275px;}
.app_l{float:left;width:200px;line-height:24px;color: #666666;}
.app_r{float:right;}


.copyright_info{ width:100%; height:50px; line-height:50px; text-align:center; float:left;}
/*第三方中性首页样式*/
.login_bg_neuter{ background:url(../Images/login_bg_02-02.png) center center no-repeat;}
.login_main .login_topic_neuter{ width:586px; height:464px; background:url(../Images/login_tv_bg-02.png) no-repeat; float:left; position:relative;margin-top:35px;}
.app_warp_neuter{ float:right; margin-top:50px; margin-right:150px;}
.open_button{width:120px; height:42px; line-height:42px; background:url(../Images/login_button.png) no-repeat; color:#fff; font-family:"微软雅黑"; font-size:16px; border:0; letter-spacing:3px; margin-top:20px;cursor:pointer; position:absolute; right:100px; top:-85px;}