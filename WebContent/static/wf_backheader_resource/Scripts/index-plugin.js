/**
 * @Description 首页
 * <AUTHOR>
 * @date 2013-12-31
 */
 
 var IndexPlugin = {
 	
 	/* 查询系统事件 */
 	querySystemEvent: function(params) {
 		var _this = this;
		$.ajax({
			type: "post",
			url:  contextPath + "/index/querySysEventInfo.htm",
			data: "paramInfo=" + JSON.stringify(params), 
			success: function(msg){
				if (! $.isEmptyObject(msg.dataInfo)) {
					var html = _this.populateHtmlContent(msg.dataInfo);
					// 追加dom元素
					$("#tHead").nextAll().empty().end().after(html);
				}
				else {
					$("#tHead").nextAll().empty().end()
							   .after("<tr><td colspan=\"" + $("#tHead th").length + "\" style=\"text-align:center\">" + noDataTip + "</td></tr>");
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				alertTip({message: failedTip});
			}
		});
 	},
 	
 	/* 获取企业描述 */
 	getEnterpriseDesc: function() {
 		$.ajax({
			type: "post",
			url:  contextPath + "/index/getEnterpriseDesc.htm",
			data: "paramInfo=", 
			success: function(msg){
				var remark = msg.remark ;
				if (remark == null) {
					remark = "";
				}
				$("#company_intro").empty().append(remark);
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				alertTip({message: failedTip});
			}
		});
 	},
 	
 	/* 查询计量点统计信息 */
 	queryMeterCountInfo: function(param) {
 		$.ajax({
			type: "post",
			url:  contextPath + "/index/queryMeterCountInfo.htm",
			data: "meterStatus=" + param.meterStatus, 
			success: function(msg){
				if (! $.isEmptyObject(msg)) {
					$.each(msg, function() {
						// 电
						if (this.METER_TYPE == 1) {
							$("#elecStat").text(this.TOTAL_COUNT);
						}
						// 水
						else if (this.METER_TYPE == 2) {
							$("#waterStat").text(this.TOTAL_COUNT);
						}
						// 气
						else if (this.METER_TYPE == 3) {
							$("#gasStat").text(this.TOTAL_COUNT);
						}
						// 热
						else if (this.METER_TYPE == 4) {
							$("#otherStat").text(this.TOTAL_COUNT);
						}
					});
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				alertTip({message: failedTip});
			}
		});
 	},
 	
 	/* 查询电费信息 */
 	queryElecBillInfo: function() {
 		$.ajax({
			type: "post",
			url:  contextPath + "/index/queryElecBillInfo.htm",
			data: "paramInfo=", 
			success: function(msg){
				if (! $.isEmptyObject(msg)) {
					var flag = false;
					var flag2 = false;//本月和上月中是否有一个为空
					var text = "您本月当前电费";
					$.each(msg, function(key, value) {
						// 本月
						if (key == "currentDate") {
							$("#currElec").text(value);
						}
						// 上月
						else if (key == "lastDate") {
							$("#lastElec").text(value);
							if (parseInt(value) != 0) flag = true;
						}
						// 本月当前电费和上月同期比较(环比)
						else if (key == "overLast") {
							flag2=true;
							text += value > 0 ? "超过" : (value == 0 ? "等于" : "少于"); 
							$("#overLast label").text(text + "上月同期");
							if(value==0) {
								$("#overLast .tc_orange").text("");
							} else {
								$("#overLast .tc_orange").text(Math.abs(value)+"%");
							}
						}
					});
					if (flag&&flag2) {
						// 展示环比数据
						$("#overLast").show();
					}
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				alertTip({message: failedTip});
			}
		});
 	},
 	
 	/* 查询实时曲线统计数据(电水汽)当前用户所有计量点 */
 	queryRealCurveInfo: function(param) {
 		var _this = this;
 		var xData = [];
 		var yData = [];
 		var name = "";
 		curveChart.showLoading({text:'读取数据中...'});
 		$.ajax({
			type: "post",
			url:  contextPath + "/index/queryRealCurveInfo.htm",
			data: "curveType=" + param.curveType, 
			success: function(msg){
				// 隐藏加载话术
				curveChart.hideLoading();
				curveChart.clear();
				// 曲线数据
				if (! $.isEmptyObject(msg.curveStat)) {
					$.each(msg.curveStat, function() {
						xData.push(this.statDate);
						yData.push(_this.roundValue2(this.dataValue));		
					});
					curveOption.xAxis = [{
			            type : 'category',
			            axisLabel : {textStyle : {color:'#fff'}},
	            		splitLine : {show : false},
			            boundaryGap : false,
			            data : xData
					}];
					if (param.curveType  == "1")
						curveOption.yAxis[0].name = "kWh";
					else if (param.curveType == "4")
						curveOption.yAxis[0].name = "kgce";
					else
						curveOption.yAxis[0].name = "m³";
					curveOption.series = [{
			            name: _this.getUnit(param.curveType),
			            type:'line',
			            symbol:'none',
			        	smooth:true,
			            itemStyle: {
			                normal: {
			                    lineStyle: {
			                        shadowColor : 'rgba(0,0,0,0.4)'
			                    }
			                }
			            },
			            data:yData
			        }];
			         // 重新加载图表
				    curveChart.setOption(curveOption, true);
				}
				else {
					showNoDataTip(curveChart);
				}
				// 本日最高
				if (msg.todayPeak > 0) {
					$("#todayPeak").text(msg.todayPeak);
				}
				// 本月最高
				if (msg.monthPeak > 0) {
					$("#monthPeak").text(msg.monthPeak);
				}
				// 本年最高
				if (msg.yearPeak > 0) {
					$("#yearPeak").text(msg.yearPeak);
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				curveChart.hideLoading();
				alertTip({message: failedTip});
			}
		});
 	},
 	
 	/* 查询能耗排名(计量点类型 0:综合, 1:电, 2:水, 3:气, 4:热) */
 	queryUseEnergyRanking: function(param) {
 		var _this = this;
 		var seriesData = [];
 		var yData = [];
 		var tmpValue = "";
 		rankChart.showLoading({text: '读取数据中...'});
 		$.ajax({
			type: "post",
			url:  contextPath + "/index/queryUseEnergyRanking.htm",
			data: "meterType=" + param.meterType, 
			success: function(msg){
				// 隐藏加载话术
				rankChart.hideLoading();
				rankChart.clear();
				if (! $.isEmptyObject(msg)) {
					$.each(msg, function() {
						yData.push(this.LEDGER_NAME);
						tmpValue = _this.getItemValue(this, param.meterType);
						seriesData.push(Math.round(tmpValue * 100)/100);
					});
					rankOption.yAxis = [{
			            type : 'category',
			            axisLabel : {textStyle : {color:'#fff'}},
	            		splitLine : {show : false},
			            data : yData
				    }];
				    rankOption.series = [{
				            name: _this.getUnit(param.meterType),
				            type:'bar',
				            barWidth : 20,
				            itemStyle: {
							    normal: {
							        label: {
							            show: true,
							            formatter : "{b} {c}",
							            position : 'inside'
							        },
							        labelLine: {
							            show: false
							        }
							    },
							    emphasis: {
							        label: {
							            show: false
							        },
							        labelLine: {
							            show: false
							        }
							    }
							},
				            data: seriesData
				    }];
				    
				    if (param.meterType == '1')
				    	rankOption.xAxis[0].name = "kWh";
				    else if (param.meterType == '0')
				    	rankOption.xAxis[0].name = "kgce";
				    else
				    	rankOption.xAxis[0].name = "m³";
				    
				    // 重新加载图表
				    rankChart.setOption(rankOption, true);
				}
				else {
					showNoDataTip(rankChart);
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				rankChart.hideLoading();
				alertTip({message: failedTip});
			}
		});
 	},
 	
 	/* 查询本月用能分布 */
 	queryCurrMonthEnergyDist: function() {
 		var _this = this;
 		var ledgerData = [];
 		var typeData = [];
 		var titleName = "";
 		elecChart.showLoading({text: '读取数据中...'});
 		$.ajax({
			type: "post",
			url:  contextPath + "/index/queryCurrMonthEnergyDist.htm",
			data: "paramInfo=", 
			success: function(msg){
				// 隐藏加载话术
				elecChart.hideLoading();
				elecChart.clear();
				if (! $.isEmptyObject(msg)) {
					// 按计量点类型统计
					if ($.isEmptyObject(msg.totalByType) && $.isEmptyObject(msg.totalByLedger)) {
						showNoDataTip(elecChart);
						return;
					}
					// 按分户统计
					if (! $.isEmptyObject(msg.totalByLedger)) {
						$.each(msg.totalByLedger, function() {
							ledgerData.push({name: this.LEDGER_NAME, value: _this.roundValue(this.SUM_VALUE)});
						});
					}
					if (! $.isEmptyObject(msg.totalByType)) {
						var count = 0;
						$.each(msg.totalByType, function(i, n) {
							// 电
							if (this.Q != null) {
								count = _this.roundValue(this.Q);
							}
							typeData.push({name: "电", value: count});
							
							// 水
							count = 0;
							if (this.WATER_FLOW != null) {
								count = _this.roundValue(this.WATER_FLOW);
							}
							typeData.push({name: "水", value: count});
							
							// 气
							count = 0;
							if (this.GAS_FLOW != null) {
								count =  _this.roundValue(this.GAS_FLOW);
							}
							typeData.push({name: "气", value: count});
						});
					}
					elecOption.series = [{
			            type:'pie',
			            selectedMode: 'single',
			            radius : [0, 40],
			            itemStyle : {
			                normal : {
			                    label : {
			                        position : 'inner'
			                    },
			                    labelLine : {
			                        show : false
			                    }
			                }
			            },
			            data: typeData
			        },
			        {
			            type:'pie',
			            radius : [70, 120],
			            itemStyle : {
			                normal : {
			                    label : {
			                        position : 'inner'
			                    },
			                    labelLine : {
			                        show : false
			                    }
			                }
			            },
			            data: ledgerData
			        }];
			        // 重新加载图表
					elecChart.setOption(elecOption, true);
				}
				else {
					showNoDataTip(elecChart);
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				elecChart.hideLoading();
				alertTip({message: failedTip});
			}
		});
 	},
 	
 	/* 拼接html */
 	populateHtmlContent: function(data) {
 		var item = [];
 		var nameTmp = "";
 		var eventTmp = "";
 		$.each(data, function() {
 			nameTmp = this.objectName;
 			eventTmp = this.eventName;
 			nameTmp = (nameTmp.length > 4 ? nameTmp.substring(0,4) + "..." : nameTmp);
 			eventTmp = (eventTmp.length > 4 ? eventTmp.substring(0,4) + "..." : eventTmp);
 			item.push("<tr>");
 			item.push("<td title=\""+this.eventStartTime+"\">"); 
 			item.push(this.eventStartTime.substring(10,16)); 
 			item.push("</td>");
 			item.push("<td title=\"" + this.eventName + "\">"); 
 			item.push(eventTmp);            
 			item.push("</td>");
 			item.push("<td title=\"" + this.objectName + "\">"); 
 			item.push(nameTmp);             
 			item.push("</td>");
 			item.push("</tr>");
 		});
 		return item.join("");
 	},
 	
 	/* 获取图表单位 */
 	getUnit: function(type) {
 		var name = "";
 		if (type == 0) {
 			name = "标准煤";
 		}
 		else if (type == 1) {
			name = "电量";
		}
		else if (type == 2) {
			name = "水量";
		}
		else if (type == 3) {
			name = "气量";
		}
		return name;
 	}, 
 	
 	/* 获取统计值 */
 	getItemValue: function(obj, type) {
 		var tmpValue = "";
 		switch (type)
 		{
 		case 0: 
 			tmpValue = obj.SUM_VALUE;
 			break;
 		case 1:
 			tmpValue = obj.Q;
 			break;
 		case 2:
 			tmpValue = obj.WATER_FLOW;
 			break;
 		case 3:
 			tmpValue = obj.GAS_FLOW;
 			break;
 		}
 		return tmpValue;
 	},
 	
 	/* 保留两位小数 */
 	roundValue: function(value) {
 		return Math.round(value * 100)/100;
 	},
 	
 	/* 保留整数 */
 	roundValue2: function(value) {
 		return Math.round(value);
 	}
 }