var IndexManager = function() {};
IndexManager.prototype.url = {};

IndexManager.prototype = {

	getPicInfo : function(legerId) {
		$("#pointDiv").empty();
		var _this = this;
		$.ajax({
			type: "POST",
			url: contextPath+"/indexChart/showPicInfo.htm",
			data: "legerId="+legerId,
			success: function(msg){
				if(msg != null && $(msg).length>0){
					$(msg).each(function(){
						var type = Common.IsUndefined(this.TYPE);
						var x = Common.IsUndefined(this.X);
						var y = Common.IsUndefined(this.Y);
						if(x=="" || y=="")
							return ;
						var html=[];
						if(type==1){//分户
							var ledgerId =  Common.IsUndefined(this.LEDGER_ID);
							var ledgerName =  Common.IsUndefined(this.LEDGER_NAME);
							var q = Common.IsUndefined(this.Q);
							var waterFlow = Common.IsUndefined(this.WATER_FLOW);
							var gasFlow = Common.IsUndefined(this.GAS_FLOW);
							html.push("<div class=\"point\"  style=\"top:"+x+"px;left:"+y+"px;\">"); 
								html.push("<img class=\"close_icon\" src=\""+imagePath+"/index/radius_close_icon.png\" type=\""+type+"\" delId=\""+ledgerId+"\" />"); 
								html.push("<img class=\"hand L ledger\"  src=\""+imagePath+"/index/map_position.png\" ledgerId=\""+ledgerId+"\"/>"); 
								html.push("<div  class=\"point_detail hide\">");
								html.push("<p><span class=\"txt_title\">能管对象的名称：</span><span class=\"txt_d\">"+ledgerName+"</span></p>");
								html.push("<p><span class=\"txt_title\">当日电能耗：</span><span class=\"txt_d\">"+q+"</span></p>");
								html.push("<p><span class=\"txt_title\">当日水能耗：</span><span class=\"txt_d\">"+waterFlow+"</span></p>");
								html.push("<p><span class=\"txt_title\">当日气能耗：</span><span class=\"txt_d\">"+gasFlow+"</span></p>");
							html.push("</div></div>");
						}else if(type==2){//计量点
							var meterId = Common.IsUndefined(this.METER_ID);
							var meterName = Common.IsUndefined(this.METER_NAME);
							var meterType = Common.IsUndefined(this.METER_TYPE);
							var faqValue = Common.IsUndefined(this.FAQ_VALUE);
							html.push("<div class=\"point\" style=\"top:"+x+"px;left:"+y+"px;\">");
								html.push("<img class=\"close_icon\" src=\""+imagePath+"/index/radius_close_icon.png\" type=\""+type+"\" delId=\""+meterId+"\" />");
								html.push("<img class=\"hand L\"  src=\""+imagePath+"/index/"+getMeterImg(meterType)+"\"/>");
								html.push("<div class=\"point_detail hide\">");
								html.push("<p><span class=\"txt_title\">采集点名称：</span><span class=\"txt_d\">"+meterName+"</span></p>");
								html.push("<p><span class=\"txt_title\">当日"+getMeterName(meterType)+"能耗：</span><span class=\"txt_d\">"+faqValue+"</span></p>");
							html.push("</div></div>");
						}
						$("#pointDiv").append(html.join(""));
					});
				}else{
					//Common.Xtip({msg:"您还未绑定分户或计量点"});
				}
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){
			
			} 
		});
	},
	
	updatePointPosition:function(){
		var _this = this;
		var positionBean={
			type:$("#dataInfo").data("type"),
			id:$("#dataInfo").data("pointId"),
			pointX:$("#dataInfo").data("pointX"),
			pointY:$("#dataInfo").data("pointY"),
			picStr:Common.IsUndefined(picStr)
		};
		if(_this.isBunded(positionBean)){
			Common.Xtip({msg:"已绑定请先删除"});
			setTimeout(function(){
				$("#pointDiv #dataDiv").remove();
				clearCondition();
			},1500);
			return ;
		}
		
		$.ajax({
			type: "POST",
			url: contextPath+"/indexChart/updatePointPosition.htm",
			data: positionBean,
			success: function(msg){
				if(msg.isSuccess){
					Common.Xtip({type:"ok",msg:successTip});
					var html=[];
					var name =$(".pointId").val();
					if(positionBean.type==1){
						var url="";
						var q = Common.IsUndefined(msg.Q);
						var waterFlow = Common.IsUndefined(msg.WATER_FLOW);
						var gasFlow = Common.IsUndefined(msg.GAS_FLOW);
						html.push("<div class=\"point\"  style=\"top:"+positionBean.pointX+"px;left:"+positionBean.pointY+"px;\">"); 
								html.push("<img class=\"close_icon\" src=\""+imagePath+"/index/radius_close_icon.png\" type=\""+positionBean.type+"\" delId=\""+positionBean.id+"\" />"); 
								html.push("<img class=\"hand L ledger\"  src=\""+imagePath+"/index/map_position.png\" ledgerId=\""+positionBean.id+"\" url=\""+url+"\" />"); 
								html.push("<div class=\"point_detail hide\">");
								html.push("<p><span class=\"txt_title\">能管对象的名称：</span><span class=\"txt_d\">"+name+"</span></p>");
								html.push("<p><span class=\"txt_title\">当日电能耗：</span><span class=\"txt_d\">"+q+"</span></p>");
								html.push("<p><span class=\"txt_title\">当日水能耗：</span><span class=\"txt_d\">"+waterFlow+"</span></p>");
								html.push("<p><span class=\"txt_title\">当日气能耗：</span><span class=\"txt_d\">"+gasFlow+"</span></p>");
						html.push("</div></div>");
					}else{
						var faqValue = Common.IsUndefined(msg.faqValue);
						var meterType = $("#dataInfo").data("meterType");
						html.push("<div class=\"point\" style=\"top:"+positionBean.pointX+"px;left:"+positionBean.pointY+"px;\">");
								html.push("<img class=\"close_icon\" src=\""+imagePath+"/index/radius_close_icon.png\"  type=\""+positionBean.type+"\" delId=\""+positionBean.id+"\"/>");
								html.push("<img class=\"hand L\"  src=\""+imagePath+"/index/"+getMeterImg(meterType)+"\"/>");
								html.push("<div class=\"point_detail hide\">");
								html.push("<p><span class=\"txt_title\">采集点名称：</span><span class=\"txt_d\">"+name+"</span></p>");
								html.push("<p><span class=\"txt_title\">当日"+getMeterName(meterType)+"能耗：</span><span class=\"txt_d\">"+faqValue+"</span></p>");
						html.push("</div></div>");		
					}
					$("#pointDiv #dataDiv").remove();
					$("#pointDiv").append(html.join(""));
				}else{
					Common.Xtip({msg:failedTip});
				}
				clearCondition();
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){
			
			} 
		});
	},
	
	isBunded:function(positionBean){
		var flag = false;
		$.ajax({
			type: "POST",
			url: contextPath+"/indexChart/isBunded.htm",
			async: false,
			data: positionBean,
			success: function(msg){
				flag=msg;
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){
			
			} 
		});
		return flag;
	},
	
	deletePoint:function(positionBean,index){
		$.ajax({
			type: "POST",
			url: contextPath+"/indexChart/updatePointPosition.htm",
			data: positionBean,
			success: function(msg){
				if(msg.isSuccess){
					$("#pointDiv .point").eq(index).remove();
					Common.Xtip({type:"ok",msg:successTip});
				}else{
					Common.Xtip({msg:failedTip});
				}
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){
			
			} 
		});
	},
	
	getStat:function(legerId){
		$.ajax({
			type: "POST",
			url: contextPath+"/indexChart/getStat.htm",
			data: "legerId="+legerId+"&topN=5",
			success: function(msg){
				if(msg != null){
					$.each(msg,function(key,value){
						if(key != "list")
							$("#"+key).html(Common.IsUndefined(Math.round(value)));
						else{
							var html=[];
							$(value).each(function(){
								html.push("<tr><td>"+Common.IsUndefined(this.LEDGER_NAME)+"</td><td>"+Common.IsUndefined(Math.round(this.Q))+" kW</td></tr>");
							});
							$("#tbody").html(html.join(""));
						}	
					});
				}
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){
			
			} 
		});
	},
	getFloorMeters:function(strucId){
		$("#pointDiv").empty();
		$.ajax({
			type: "POST",
			url: contextPath+"/indexChart/getFloolMeters.htm",
			data: "legerId="+legerId+"&strucId="+strucId,
			success: function(msg){
				if(msg != null && $(msg).length>0){
					$(msg).each(function(){
						var x = Common.IsUndefined(this.X);
						var y = Common.IsUndefined(this.Y);
						if(x=="" || y=="")
							return ;
						var meterId = Common.IsUndefined(this.METER_ID);
						var meterName = Common.IsUndefined(this.METER_NAME);
						var meterType = Common.IsUndefined(this.METER_TYPE);
						var faqValue = Common.IsUndefined(this.FAQ_VALUE);
						var html=[];
						html.push("<div class=\"point\" style=\"top:"+x+"px;left:"+y+"px;\">");
								html.push("<img class=\"close_icon\" src=\""+imagePath+"/index/radius_close_icon.png\"  type=\"2\" delId=\""+meterId+"\" />");
								html.push("<img class=\"hand L\"  src=\""+imagePath+"/index/"+getMeterImg(meterType)+"\"/>");
								html.push("<div class=\"point_detail hide\">");
								html.push("<p><span class=\"txt_title\">采集点名称：</span><span class=\"txt_d\">"+meterName+"</span></p>");
								html.push("<p><span class=\"txt_title\">当日"+getMeterName(meterType)+"能耗：</span><span class=\"txt_d\">"+faqValue+"</span></p>");
							html.push("</div></div>");
						$("#pointDiv").append(html.join(""));
					});
				}
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){
			
			} 
		});
	
	}
	
}
function getMeterImg(meterType){
	var img = "map_position.png";
	if(meterType ==2)
		img="map_position_2.png";
	if(meterType ==3)
		img="map_position_3.png";
	if(meterType ==4)
		img="map_position_4.png";
	return img;
}
function getMeterName(meterType){
	var img = "电";
	if(meterType ==2)
		img="水";
	if(meterType ==3)
		img="气";
	if(meterType ==4)
		img="热";
	return img;
}