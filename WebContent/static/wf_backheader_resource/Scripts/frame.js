// JavaScript Document
$(document).ready(function(){
	
	function screenSize_frame(){
		if($(".main_body").width()>1280){
			$(".main_body").width('100%');
		}
		var windowH=$(window).height();//窗口高度
		var docH = $(window).height();//文档高度
		var docH_w = $(window).height();//文档高度
		var DocWidth=$(".UI_main").width(); 
		var headerH = $(".header").height()+5;//5是header底部边框宽度
		var bottomH = $(".bottom").height();
		
		$(".UI_main").height(docH-headerH-bottomH);//内容区域高度
		var UI_main_demo_h=docH_w-60;
		$(".main_div").height(docH-headerH-bottomH);
		if(UI_main_demo_h<700)
		{
			$(".main_body_demo").height(UI_main_demo_h);
			//$(".UI_main-demo").css("padding","0");
			UI_main_demo_h=700;
		}
		else
		{
			$(".main_body_demo").height('auto');
		}
		$(".UI_main-demo").height(UI_main_demo_h);
			$(".index-demo .chat_box").height(UI_main_demo_h/2-50);
			$(".index-demo .chat_area").height(UI_main_demo_h/2-50-70);
			
			$(".div1").height($(".UI_main").height()/2-10);
			$(".div3").height($(".div1").height()-110);
			$(".div4").height($(".div1").height()-110);
			if($(".main_div").hasClass("realTimeCheck"))
			{//alert(1);
				$(".div_up").height($(".main_div").height()*0.3);
				$(".div_down").height($(".main_div").height()*0.6);//
//				$(".down_chart").width(500);
//				$(".down_chart").height(200);
			$(".main_div #chart1").height($(".div_up").height() - $(".div_up .h_title").outerHeight(true));
    $(".main_div #chart2").height($(".div_down .down_1").height() - $(".div_down .down_1 .h_title").outerHeight(true));
    $(".main_div #chart3").height($(".div_down .down_2").height() - $(".div_down .down_2 .h_title").outerHeight(true));
    $(".main_div #chart4").height($(".div_down .down_3").height() - $(".div_down .down_3 .h_title").outerHeight(true));
    $(".main_div #chart5").height($(".div_down .down_4").height() - $(".div_down .down_4 .h_title").outerHeight(true));
			}
		$(".Tree_type-tab").height(docH-headerH-bottomH);	//左侧对象选择区域的高度
		var titleH = $(".top_title").height(); //当前选择的对象区域
		$(".Main_c").height(docH-headerH-bottomH-titleH);//图表和表格显示区域
		
		var treeH = $(".Tree_type-tab").height();
		$(".tree_search-list").height(treeH-271-40-40);	//树形结构展示区域
		$(".treemain_c").height(docH-headerH-bottomH);	//左侧分页结构展示区域
		$(".auto-LopTap-reault").height(docH-headerH-bottomH-35);
        if($("#analyType").val() == 102){
            $(".auto-LopTap-reault").height(docH-headerH-bottomH);
        }
        $(".auto-LopTap-reault2").height(docH-headerH-bottomH);
		$(".screen-tree-center").height(docH-headerH-bottomH-78-60);	//左侧分页结构展示区域
	}

	window.onresize = function(){
		screenSize_frame();
	}
	
	window.onload=function(){
		screenSize_frame();
	}
function index_demo(){
	}
	//菜单特效
	$(".menu>li").live("hover",function(){
		$(this).find(".menu_s").toggle();
		$(".menu li").removeClass("hov");
		if(!$(this).hasClass("on")){
			$(this).addClass("hov");
		}
	});
	$(".menu>li").live("click",function(){
		$(this).removeClass("hov");
		$(".menu li").removeClass("on");
		$(this).addClass("on");
	});
	$(".menu>li").live("mouseout",function(){
		$(this).removeClass("hov");
	});
	
	$(".button_c .ss").live("click",function(){
		if($(".select_box").is(':visible')){
			$(this).removeClass("select");
			$(".select_box").hide();
		}else{
			$(this).addClass("select");
			$(".select_box").show();
		}
	});
	$(".header_link li").click(function(){
		$(".header_link li").removeClass("on");
		$(this).addClass("on");
	});
	//树收缩展开	
	$(".bar img").click(function(){
		var DocWidth=$(window).width();
		var srci=$(this).attr("src");
		var s_srci = srci.substring(0,srci.lastIndexOf('/')+1);
		var s_srci2 = srci.substring(srci.lastIndexOf('/')+1,srci.length);
		if(s_srci2=="bar_close.png"){
			$(this).attr("src",s_srci+"bar_open.png");
			$(".UI_main_tree").hide();
			$(".UI_main_content").css("margin-left","0");
			$(".UI_main_content").width(DocWidth-2);
			$(".bar").addClass("on");
		}else{ 
			$(this).attr("src",s_srci+"bar_close.png");
			$(".UI_main_tree").show();
			$(".UI_main_content").width(DocWidth-$(".UI_main_tree").width()-2-5);
			$(".UI_main_content").css("margin-left","5px");
			$(".bar").removeClass("on");
		}
	});
	$(".floor_list li").live('click',function(){
		$(".floor_list li").removeClass("on");
		$(this).addClass("on");
	});
	//对象选择各容器切换
	$(".Tree_type-tab  li").live('click',function(){
		curTabIndex=$(this).index(".Tree_type-tab  li");
		$(".Tree_type-tab  li").removeClass('on');
		$(".Tree_main .treemain_c").hide();
		$(".Tree_main .treemain_c").eq(curTabIndex).show();
		$(this).addClass('on');
		chgTreeTab();
		if($(".UI_main_tree").hide()){
			$(".UI_main_tree").show();
			if (typeof (document.getElementById("main_frame").contentWindow.resize) != "undefined") {
				document.getElementById("main_frame").contentWindow.resize();
			}
		}
	});	
				
	//查询树区域选择
	$(".change_region").click(function(){
		$(this).parent(".region_search").addClass('hover');
		$(this).next('.region_select').show();
	});
	$(".region_select .close").click(function(){
		$(this).parent(".region_select").hide();
		$(this).parent().parent(".region_search").removeClass('hover');
		if($(this).parent().find('.region_p').val()!=''){
			$(this).parent().parent(".region_search").find(".region_checked").text($(this).parent().find('.region_p').val()+' '+$(this).parent().find('.region_c').val()+' '+$(this).parent().find('.region_a').val()+' '+$(this).parent().find('.region_s').val());
			$(this).parent().parent(".region_search").find(".region_checked").attr('title',$(this).parent().find('.region_p').val()+' '+$(this).parent().find('.region_c').val()+' '+$(this).parent().find('.region_a').val()+' '+$(this).parent().find('.region_s').val());
		}else{ 
			$(this).parent().parent(".region_search").find(".region_checked").text('请选择');
			$(this).parent().parent(".region_search").find(".region_checked").attr('title','请选择');
		}
	});
	//树搜索全选
	$(".tree_search_total .checkall").click(function(){
		if ($(this).attr('checked') !='checked') {
			$(".tree_search-list input[name='user_name']").attr("checked",false); 
		}else{
			$(".tree_search-list input[name='user_name']").attr("checked",true); 
		}
	});
	
	$(".tree_search-list input[name='user_name']").click(function(){
		var re=0;
		$("input[name='user_name']").each(function(){
			if ($(this).attr('checked') !='checked') {
				$(".tree_search_total input[name='checkall']").attr("checked",false);
				re=1;
		    }
		});
		if(re==0){
			$(".tree_search_total input[name='checkall']").attr("checked",true);
		}
	});
							  
	$(".user_remove").live('click',function(){
		$(".tree_search-list li.on").remove();
	});
});