(function($) {
$.fn.table=function(method,options){
	var defaults={
		/*	表格基本参数（前两项必填）
			rows:表格总行数
			cols:表格总列数
		*/
		basic:{rows:0,cols:0},
		/*	表格隐藏域对象
			sortName:表格排序隐藏域对象
			sortOrder:排序方式隐藏域对象
			displayField:表格定制时存放显示的列的隐藏域的对象(有自定义表格功能时，填写此属性)
		*/
		inputArguments:{sortName:$("#sortName"),sortOrder:$("#sortOrder"),defaultSortOrder:"asc",displayField:null},
		/*	表格操作条(默认无操作条)
			若表格有操作条，则进行初始化，格式为
			{
				offset:[0,0]调整简单操作板的左右偏移量
			}
			operate:{offset:[0,0],coverObj:$(".ue-opt-plate")}
		*/
		operate:null,
		/*排序的回调函数，若有排序操作，则必须进行初始化*/
		sortCallBack:function(){return null;},
        /*初始化时，是否需要判断有无数据*/
        firstNoData:true
	}
	
	var opts = defaults;
	if(options){
		opts=$.extend(defaults,options);
	}
	
	/*表格初始化定义*/
	var table ={
		/*表格补白函数*/
		filltable:function(rows,columns,tableObj,firstNoData){
			/*
				如果表格无无数据错误，为表格填充空白列。rows为表格的行数，columns为表格的列数
			*/
			var l = tableObj.find(".table-panel tbody tr").size();
			if(l > 0){
                tableObj.removeClass("noDataTable");
				var html = "";
				if(l<rows){
					for(var i=0;i<rows-l;i++){
						html+="<tr class='blank'>";
						for(var j=0;j<columns;j++){
							html += "<td>&nbsp</td>";
						}
						html+="</tr>";
					}
					tableObj.find(".table-panel tbody").append(html);
				}
			}else{
                if(firstNoData){
				    tableObj.addClass("noDataTable");
                }
			}
			if(tableObj.hasClass("basic")){
				tableObj.find("tbody tr:odd").addClass("odd");
			}
		}
	};
	
	return this.each(function(){
		var tableObj = $(this);
		if(method=="init"){
			/*初始化排序*/
			sortTable($(this).find(".table-panel table"),opts.inputArguments.sortName,opts.inputArguments.sortOrder,opts.inputArguments.defaultSortOrder);
			/*****1.行补全*****/
			table.filltable(opts.basic.rows,opts.basic.cols,tableObj,opts.firstNoData);
			/*****2.调整表格没有竖的滚动条*****/
			resizeTable(tableObj);
			/*****3.初始化列全选的JS以及初始化选中行行变色*****/
			if(tableObj.find(".table-panel th input:checkbox").size()>0){
				checkAll(tableObj);
			}
			
			/*****4.控制操作面板显隐事件*****/
			tableObj.find(".ue-button-ico[class*='down']").click(function(){
				showPanel($(this),tableObj);
				if(opts.operate != null){
					locationCover(opts.operate.coverObj,tableObj,opts.operate.offset);
				}
			});
			
			/*自定义表格*/
			if(opts.inputArguments.displayField){
				customeTable(tableObj,opts.inputArguments.displayField,opts.basic.rows);
			}
			/*表格小操作层*/
			if(opts.operate){
				initializeOpt(opts.operate.coverObj,opts.operate.offset,tableObj);
			}
			
			$(window).resize(function(){
				/*调整表格没有竖的滚动条*/
				resizeTable(tableObj);
			});
		}else if(method=="adjust"){
			/*调整表格的显示*/
			table.filltable(opts.basic.rows,opts.basic.cols,tableObj,true);
			resizeTable(tableObj);
		}else if(method=="location"){
			locationCover(opts.operate.coverObj,tableObj,opts.operate.offset);
		}
		
		/*列全选*/
		function checkAll(tableObj){
			/*列全选*/
			tableObj.find(".table-panel th input:checkbox").click(function(){
				
				if($(this).attr("checked")=="checked"){
					tableObj.find(".table-panel tr td input:checkbox").attr("checked",true);
					//tableObj.find(".table-panel tbody tr:not(.blank)").addClass("click");
				}else if($(this).attr("checked")=="undefined" ||$(this).attr("checked")==undefined ){
					tableObj.find(".table-panel tr td input:checkbox").attr("checked",false);
					//tableObj.find(".table-panel tbody tr:not(.blank)").removeClass("click");
				}
			});
			
			tableObj.find(".table-panel tr td:first-child input:checkbox").live("click",function(){
				if(tableObj.find(".table-panel tr td:first-child input[type='checkbox']").size()==tableObj.find(".table-panel tr td:first-child input[type='checkbox']:checked").size()){
					tableObj.find(".table-panel th.ue-checkbox input[type='checkbox']").attr("checked",true);
				}else{
					tableObj.find(".table-panel th.ue-checkbox input[type='checkbox']").attr("checked",false);
				}
			});
			
			/*初始化选中行行变色*/
			tableObj.find(".table-panel tr td:first-child input:checkbox").click(function(event){
				$(this).parents("tr").toggleClass("click");
				event.stopPropagation();
			});
		}
		
		/*调整表格高度*/
		function resizeTable(tableObj){
			/*如果没有无数据错误提示*/
			if(tableObj.find(".errorPanel:visible").size() <= 0){
				/*如果为IE，并且有滚动条*/
				if(!-[1,] && tableObj.find(".table-panel").width() < tableObj.find(".table-panel table").width()){
					tableObj.find(".table-panel").height(tableObj.find(".table-panel table").height() + 17);
				/*如果不是IE，并且有滚动条*/
				}else if(-[1,] && tableObj.find(".table-panel").width() < tableObj.find(".table-panel table").width()){	
					tableObj.find(".table-panel").height(tableObj.find(".table-panel table").height() + 18);
				/*如果不是IE，并且没有滚动条*/
				}else if(-[1,] && tableObj.find(".table-panel").width() > tableObj.find(".table-panel table").width()){
					tableObj.find(".table-panel").height(tableObj.find(".table-panel table").height() + 1);
				}else{
					tableObj.find(".table-panel").height(tableObj.find(".table-panel table").height());
				}
			}else{
				/*如果有无数据的错误提示*/
				var height = tableObj.find(".table-panel thead").height() + tableObj.find(".errorPanel").outerHeight() + 4;
				/*如果为IE，并且有滚动条*/
				if(!-[1,] && tableObj.find(".table-panel").width() < tableObj.find(".table-panel table").width()){
					tableObj.find(".table-panel").height( height + 17);
				/*如果不是IE，并且有滚动条*/
				}else if(-[1,] && tableObj.find(".table-panel").width() < tableObj.find(".table-panel table").width()){
					tableObj.find(".table-panel").height( height + 18);
				/*如果不是IE，并且没有滚动条*/
				}else if(-[1,] && tableObj.find(".table-panel").width() > tableObj.find(".table-panel table").width()){
					tableObj.find(".table-panel").height( height + 1);
				}else{
					tableObj.find(".table-panel").height( height);
				}
			}
		}
	
		/*自定义表格内置函数*/
		function customeTable(tableObj,displayFieldObj,rows){
			tableObj.find(".custom-table td:odd").addClass("odd");
			if(tableObj.find(".custom-table input").size() > 0){
				/*循环遍历表格定制项,选择可控制列*/
				tableObj.find(".custom-table input").each(function(){
					if($(this).attr("checked") == false ||$(this).attr("checked")=="undefined" ||$(this).attr("checked")==undefined){
						var index = tableObj.find(".table-panel th").index(tableObj.find(".table-panel th[name='"+$(this).val()+"']")) + 1;
						tableObj.find(".table-panel tbody>tr>td:nth-child("+index+"),.table-panel thead th:nth-child("+index+")").hide();
					}
				});
				
				/*控制列的隐藏与显示*/
				tableObj.find(".custom-table input").live('click',function(){
					var index = tableObj.find(".table-panel th").index(tableObj.find(".table-panel th[name='"+$(this).val()+"']")) + 1;
					
					//选中，则列显示
					if($(this).attr("checked") == true || $(this).attr("checked")=="checked"){
						tableObj.find(".table-panel tbody>tr>td:nth-child("+index+"),.table-panel  thead>tr>th:nth-child("+index+")").show();
					}else{
						if(tableObj.find(".custom-table input:checked").size() < 1){
							$(this).attr("checked",true);
							return ;
						}
						//未选中，列隐藏
						tableObj.find(".table-panel tbody>tr>td:nth-child("+index+"),.table-panel  thead>tr>th:nth-child("+index+")").hide();
					}
					var displayField = "";
					tableObj.find(".custom-table input").each(function(){
						if($(this).attr("checked") == true){
							displayField = displayField + $(this).val() + ",";
						}
					});
					displayField = displayField.substring(0,displayField.length - 1);
					 displayFieldObj.val(displayField);
					
					tableObj.find("tr.blank").remove();
					table.filltable(rows,tableObj.find(".custom-table input:checked").size()+tableObj.find(".table-panel th.user-define").size()+tableObj.find("th.ue-checkbox").size(),tableObj);
					resizeTable(tableObj);
				});
			
			}
		}
	
		/*表格显隐*/
		function showPanel(btnObj,tableObj){
			var panelObj = $(".panel[attr='"+btnObj.attr("id")+"']");
			if(!panelObj.hasClass("panelShow")){
				/*其他面板隐藏，并且将按钮的向上样式去除*/
				tableObj.find(".panel").removeClass("panelShow").hide();
				tableObj.find(".ue-button-ico .arrow.up").removeClass("up");
				btnObj.find(".arrow").addClass("up");
				panelObj.addClass("panelShow").show().parents(".tool-panel").show();
			}else{
				btnObj.find(".arrow").removeClass("up");
				panelObj.removeClass("panelShow").hide().parents(".tool-panel").hide();
			}
		}
	
		/*表格排序*/
		function sortTable(tableObj,sortName,sortOrder,defaultSortOrder){
			tableObj.find("th").each(function(){
				if($(this).attr("name") == sortName.val()){
					$(this).find("span.sort").addClass(sortOrder.val());
				}
			});
			  
			tableObj.find("th").live("click",function(){
				var obj = $(this).find(".sort");
				var name = sortName.val();
				if(obj && obj.hasClass("sort")){
					//若为相同字段排序
					if($(this).attr("name") == name){
						if(obj.hasClass("asc")){
							obj.removeClass("asc").addClass("desc");
							sortOrder.val("desc");
						}else{
							obj.removeClass("desc").addClass("asc");
							sortOrder.val("asc");
						}
					}else{
						/*不同字段的排序*/
						if(tableObj.find("th").find(".sort").hasClass("asc")){
							tableObj.find("th").find(".sort").removeClass("asc");
						}else{
							tableObj.find("th").find(".sort").removeClass("desc");
						}
                        if(defaultSortOrder == "desc"){
                            obj.addClass("desc");
                            sortOrder.val("desc");
                        }else{
                            obj.addClass("asc");
                            sortOrder.val("asc");
                        }
					}
					sortName.val($(this).attr("name"));
					/*下面可以调用分页方法，带上参数去后台查询数据*/
					opts.sortCallBack();
				}
			
			});
		}
		/*操作层定位函数
		coverObj:为操作层对象
		tableObj:为当前表格对象
		offset:调整操作层的位移显示
		*/
		function locationCover(coverObj,tableObj,offset){
			if(coverObj.size() > 0 && tableObj.find(".cover-current").size() > 0){
				coverObj.css({top:tableObj.find(".cover-current").position().top + offset[0],left:tableObj.find(".cover-current").position().left + offset[1]}).attr("attr",tableObj.find(".cover-current").attr("id")).show();
			}
		}
		
		function initializeOpt(coverObj,offset,tableObj){
			/*操作层绑定事件*/
			tableObj.find(".table-panel tbody>tr:not(.blank)").live("click",function(){
				if($(".briefcaseSwitch").size()==0||!$(".briefcaseSwitch").hasClass("ue-briefcase-focus")){
					if($(this).hasClass("cover-current")){
						$(this).removeClass("cover-current");
						coverObj.hide();
					}else{
						tableObj.find("tbody>tr.cover-current").removeClass("cover-current");
						$(this).addClass("cover-current");
						locationCover(coverObj,tableObj,offset);
					}
				}
			});
		}
	
		
});
}

$.fn.tableLoading=function(method,options){
	var defaults={
		message:JSLocale.message
	}
	var opts = defaults;
	if(options){
		opts=$.extend(defaults,options);
	}
	var html = "<div class='loadingPanel'>"+
                    "<div class='tableLoading ue-clear'>"+
                    "<span class='loadingPic'></span> " +
                    "<div class='loadingPanelText'>"+
                    "<span class='patch'></span>"+
                    "<span class='loadingPanelTextContent'>"+JSLocale.detailMessage+"</span>"+
                    "</div></div></div>" ;
	return this.each(function(){
		var tableObj = $(this);
		if(method == "close"){
			tableObj.removeClass("loadingTable").find(".table-panel .loadingPanel").remove();
		}else{
			tableObj.addClass("loadingTable").find(".table-panel").append(html);
			resizeTable2(tableObj);
			$(window).resize(function(){
				if(tableObj.hasClass("loadingTable")){
					resizeTable2(tableObj);
				}
			});
		}
	});
}

/*调整表格高度*/
function resizeTable2(tableObj){
	var height = tableObj.find(".table-panel thead").height() + tableObj.find(".loadingPanel").outerHeight() + 1;
	/*如果为IE，并且有滚动条*/
	if(!-[1,] && tableObj.find(".table-panel").width() < tableObj.find(".table-panel table").width()){
		tableObj.find(".table-panel").height( height + 17);
	/*如果不是IE，并且有滚动条*/
	}else if(-[1,] && tableObj.find(".table-panel").width() < tableObj.find(".table-panel table").width()){
		tableObj.find(".table-panel").height( height + 18);
	/*如果不是IE，并且没有滚动条*/
	}else if(-[1,] && tableObj.find(".table-panel").width() > tableObj.find(".table-panel table").width()){
		tableObj.find(".table-panel").height( height + 1);
	}else{
		tableObj.find(".table-panel").height( height);
	}
	tableObj.find(".table-panel").height(140);
}
})(jQuery);