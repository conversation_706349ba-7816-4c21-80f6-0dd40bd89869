$.fn.xTip=function(options,value){
	var defaults={
		type:"error",
		tipClose:true,
		width:"300",
		position:"static",
		location:null,
		offset:[0,0],
		autoOpen:false,
		message:"",
		animate:{showTime:1000,fadeOutTime:2000},
		title:null
	};
	var opts=$.extend(defaults,options);
	var styleTip;
	return this.each(function(){
		switch(opts.type){
			case "quickTipSuccess" :{
				if(!opts.location){
					opts.location=[$(window).width()/2-120,$(window).height()/2-30];
				}
				styleTip="success";
				createQuickTip($(this),opts);
				break;
			}

			case "quickTipWarn" :{
				if(!opts.location){
					opts.location=[$(window).width()/2-120,$(window).height()/2-30];
				}
				styleTip="warn"
				createQuickTip($(this),opts);
				break;
			}
			case "quickTipError" :{
				if(!opts.location){
					opts.location=[$(window).width()/2-120,$(window).height()/2-30];
				}
				styleTip="error"
				createQuickTip($(this),opts);
				break;
			}
			default:{
				styleTip="success"
				return;
			}
		}
		
	});
	function createQuickTip(tipObj,opts){
		$(".ue-minute-success").remove();
		$(".ue-minute-warn").remove();
		var html = "";
		if(styleTip=="success"){
			html += "<div id='uedTip' class='ue-minute-success' style='position:absolute;top:"+opts.location[1]+"px;left:"+opts.location[0]+"px;z-index:99999;'>" 
					+"<div class='tipL'></div><div class='tipC'>"+opts.message +"</div><div class='tipR'></div></div>"
		}else if(styleTip=="warn"){
			html += "<div id='uedTip' class='ue-minute-warn' style='position:absolute;top:"+opts.location[1]+"px;left:"+opts.location[0]+"px;z-index:99999;'>"
					+"<div class='tipL'></div><div class='tipC'>"+opts.message +"</div><div class='tipR'></div></div>"
		}else {
			html += "<div id='uedTip' class='ue-minute-error' style='position:absolute;top:"+opts.location[1]+"px;left:"+opts.location[0]+"px;z-index:99999;'>"
					+"<div class='tipL'></div><div class='tipC'>"+opts.message +"</div><div class='tipR'></div></div>"	
		}
		tipObj.append(html);
		setTimeout(function(){
			$("#uedTip").fadeOut(opts.animate.fadeOutTime,function(){
				$(this).remove();
			});
		},opts.animate.showTime);
	};
	
}
