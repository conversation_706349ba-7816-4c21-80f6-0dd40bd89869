var allmap = new BMap.Map("allmap",{mapType:BMAP_HYBRID_MAP});  //地图对象
allmap.centerAndZoom("南京",11);  //用城市名设置地图中心点
allmap.addControl(new BMap.MapTypeControl({mapTypes:[BMAP_NORMAL_MAP,BMAP_SATELLITE_MAP,BMAP_HYBRID_MAP]})); //添加地图类型控件
allmap.addControl(new BMap.NavigationControl());  //添加缩放平移控件
allmap.addControl(new BMap.ScaleControl({anchor: BMAP_ANCHOR_BOTTOM_LEFT}));  //添加比例尺控件
allmap.enableScrollWheelZoom(true);  //开启鼠标滚轮缩放
var markerClusterer = {};  //标记的聚合效果
var initTag = 0;

jQuery(document).ready(function() {

    //事件注册--开关按钮切换
    jQuery("#map-switch").on('switchChange.bootstrapSwitch', function (event, state) {
        if (state == true) { //切换成“开”
            jQuery("div.UI_main").hide();
            jQuery("div.mapWrap").show();
            if (initTag == 0) {
                initTag = 1;
                // 地图高度、宽度自适应
                var bodyHeight = jQuery(window).height();
                var height1 = jQuery("div.header").height();
                var height2 = jQuery("div.bottom").height();
                var h = bodyHeight - ((height1 + 5) + height2);
                jQuery("div.mapWrap").height(h);
                // 滚动字幕里采用了递归，在这里将它停止，不然地图会报堆栈溢出的错
                jQuery("#ticker").children().stop();
                //执行地图展示的点的初始化方法
                initAllMap();
                //间隔0.1秒触发搜索 (解决某些电脑上初始化失败的问题)
                setTimeout(function(){jQuery("#search-map").trigger("click");}, 100);
            }
            //右边的框 宽度自适应
            $(".mapWrap_1").width($(window).width()-215-20);
        }
        else {  //切换成“关”
            jQuery("div.UI_main").show();
            jQuery("div.mapWrap").hide();
        }
    });

    //搜索框自动完成
    jQuery(".autoTapSearch_1").autoLotTap({
        url: contextPath + '/treeSet/getSearchModelDataList.htm?searchModel=1',
        multiple: false,
        callback: function (ledgerId) {
        }
    });
    jQuery(".autoTapSearch_2").autoLotTap({
        url: contextPath + '/treeSet/getSearchModelDataList.htm?searchModel=2',
        multiple: false,
        callback: function (ledgerId) {
        }
    });
    jQuery(".autoTapSearch_3").autoLotTap({
        url: contextPath + '/treeSet/getSearchModelDataList.htm?searchModel=3',
        multiple: false,
        callback: function (ledgerId) {
        }
    });
    jQuery(".autoTapSearch_4").autoLotTap({
        url: contextPath + '/treeSet/getSearchModelDataList.htm?searchModel=4',
        multiple: false,
        callback: function (ledgerId) {
        }
    });

    //事件注册--添加企业坐标
    jQuery("#addXy").click(function () {
        jQuery("#addXy").hide();

        var mkrTool = new BMapLib.MarkerTool(allmap, {autoClose: true, icon: BMapLib.MarkerTool.SYS_ICONS[8], followText: "添加企业坐标"});  //地图标注工具
        mkrTool.open(); //打开标注工具
        mkrTool.addEventListener("markend", function (evt) {
            var mkr = evt.marker;  //标注
            var html = [];
            html.push('<p style="text-align: center;font-size: 14px;font-weight: bold;">添加企业坐标</p>');
            html.push('<table style="font-size: 13px;" border="0" cellpadding="1" cellspacing="1" >');
            html.push('  <tr>');
            html.push('      <td style="padding-left: 15px;" align="left" class="common">经度：</td>');
            html.push('      <td colspan="2"><span id="xy_lng">' + evt.marker.point.lng + '</span></td>');
            html.push('  </tr>');
            html.push('  <tr>');
            html.push('      <td style="padding-left: 15px;" align="left" class="common">纬度：</td>');
            html.push('      <td colspan="2"><span id="xy_lat">' + evt.marker.point.lat + '</span></td>');
            html.push('  </tr>');
            html.push('  <tr>');
            html.push('      <td style="padding-left: 15px;" align="left" class="common">企业：</td>');
            html.push('      <td  colspan="2">' +
                '                 <input type="hidden" id="ledgerIdSelect" name="autoLotTapInput" class="autoLotTapInput" />' +
                '                 <div style="width: 140px;" class="autoLotTap" attr="autoLotTapInput" id="autoLotTapInput">' +
                '                    <div style="width: 140px;" class="autoLotTap-show">' +
                '                       <input type="text" id="ledgerId" name="ledgerId" class="auto-input" border="0" onfocus=\'if(this.value=="可输入文字模糊查询"){this.value="";};\' value="可输入文字模糊查询"/>' +
                '                    </div>' +
                '                 </div>' +
                '            </td>');
            html.push('  </tr>');
            html.push('  <tr>');
            html.push('	     <td  align="center" colspan="3">');
            html.push('	         <div style="height: 172px;"></div>');
            html.push('          <input id="savePosition" type="button" value="确定"/>');
            html.push('	     </td>');
            html.push('  </tr>');
            html.push('</table>');
            var infoWindow = new BMap.InfoWindow(html.join(""), {offset: new BMap.Size(0, -10)});
            infoWindow.addEventListener("open", function (e) {
                jQuery("#savePosition").click(function () {   //事件注册--确定按钮点击
                    saveLedgerPosition(mkr);
                    jQuery("#addXy").show();
                });

                jQuery(".autoLotTapInput").autoLotTap({      //autoLotTap注册
                    url: contextPath + '/treeSet/getNoPositionLedgerList.htm',
                    multiple: false,
                    callback: function (ledgerId) {

                    }
                });
            });
            infoWindow.addEventListener("close", function (e) {
                if (mkr.ledgerId == undefined) {
                    allmap.removeOverlay(mkr);  //删除地图中的标记
                    jQuery("#addXy").show();
                }
            });
            mkr.openInfoWindow(infoWindow);
        });

    });

    //搜索
    jQuery("#search-map").click(function () {
        initAllMap();
    });

    //右边框收、放
    jQuery(".sou_fan").click(function(){
        var text = $(this).attr("type");
        if(text == "1"){
            $(this).attr("type", "2");
            $(this).html("<");

            $(".mapWrap .mapWrap_2").hide();
            $(".mapWrap .mapWrap_1").animate(
                {width: "100%"},
                500
            );
        }
        else{
            $(this).attr("type", "1");
            $(this).html(">");

            $(".mapWrap .mapWrap_1").animate(
                {width: $(window).width()-215-20},
                500,
                function(){$(".mapWrap .mapWrap_2").show();}
            );
        }
    });


});


function initAllMap(){

    var searchModel = jQuery("select[name='searchModel'] option:selected").val();
    var selectIdStr = jQuery.trim(jQuery(".map_search div.lotTap_" + searchModel + " input").val());
    var keyWord = jQuery.trim(jQuery(".map_search div.lotTap_" + searchModel + " .autoLotTap input").val());
    if(keyWord == "输入文字模糊搜索"){
        keyWord = "";
    }

    //地图 清除所有的标记
    allmap.clearOverlays();

    //聚合 清除所有的标记
    if(!jQuery.isEmptyObject(markerClusterer)){
        markerClusterer.clearMarkers();
    }

    //去后台加载需要显示的企业
    jQuery.ajax({
        type: "POST",
        url: contextPath+"/ledgermanager/getMapShowLedgerList.htm",
        data:{
            searchModel: searchModel,
            selectIdStr: selectIdStr,
            keyWord: keyWord
        },
        success: function(msg){
            var list = msg.list;
            var markers = [];  //初始化de聚合标记数组
            var points = [];   //初始化de坐标数组
            if(list != null && list != undefined && list.length > 0){
                for (var i = 0; i < list.length; i ++) {
                    var ledger = list[i];
                    var point = new BMap.Point(ledger.x, ledger.y);
                    var mkr = new BMap.Marker(point);
                    mkr.setIcon(BMapLib.MarkerTool.SYS_ICONS[8]);
                    mkr.ledgerId = ledger.ledgerId;  ///mkr注入ledgerId
                    markerEvent(mkr);  ///mkr注入事件
                    var label = new BMap.Label(ledger.ledgerName,{offset:new BMap.Size(19,-10)});
                    mkr.setLabel(label);
                    mkr.attributes = {myLabel: label};    ///mkr设置文字标签
                    //allmap.addOverlay(mkr);

                    markers.push(mkr);
                    points.push(point);
                }
            }
            markerClusterer = new BMapLib.MarkerClusterer(allmap, {markers: markers});  //聚合标记处理
            var view = allmap.getViewport(points);
            allmap.centerAndZoom(view.center, view.zoom);    //自适应中心点、缩放级别

            //如果keyWord不为空，且list为空: 则搜索百度自己的标注点
            if(jQuery.trim(keyWord) != "" && (list == null || list == undefined || list.length == 0)){
                var local = new BMap.LocalSearch(allmap, {
                    renderOptions:{map: allmap}
                });
                local.search(keyWord);
            }
        },
        error:function(XMLHttpRequest, textStatus, errorThrown){}
    });

    //右下角 登陆的ledger的总体数据
    jQuery.ajax({
        type: "POST",
        url: contextPath+"/ledgermanager/getLedgerUseData.htm",
        data:{},
        success: function(msg){
            var ledgerName = msg.ledger.ledgerName;
            var ele = msg.ele;
            var eleUnit = msg.eleUnit;
            var water = msg.water;
            var waterUnit = msg.waterUnit;
            var gas = msg.gas;
            var gasUnit = msg.gasUnit;
            var hot = msg.hot;
            var hotUnit = msg.hotUnit;
            $("#total_title").html(ledgerName);
            $("#r_ele").html(ele);
            $("#r_eleUnit").html(eleUnit);
            $("#r_water").html(water);
            $("#r_waterUnit").html(waterUnit);
            $("#r_gas").html(gas);
            $("#r_gasUnit").html(gasUnit);
            $("#r_hot").html(hot);
            $("#r_hotUnit").html(hotUnit);
        },
        error:function(XMLHttpRequest, textStatus, errorThrown){}
    });
    jQuery.ajax({
        type: "POST",
        url: contextPath+"/ledgermanager/getLedgerMessageData.htm",
        data:{},
        success: function(msg){
            var companyCount = msg.companyCount;
            var pointCount = msg.pointCount;
            var partnerCount = msg.partnerCount;
            $("#r_companyCount").html(companyCount);
            $("#r_pointCount").html(pointCount);
            $("#r_partnerCount").html(partnerCount);
        },
        error:function(XMLHttpRequest, textStatus, errorThrown){}
    });
    jQuery.ajax({
        type: "POST",
        url: contextPath+"/ledgermanager/getLedgerPowerData.htm",
        data:{},
        success: function(msg){
            var power = msg.power;
            var powerUnit = msg.powerUnit;
            $("#r_power").html(power);
            $("#r_powerUnit").html(powerUnit);
        },
        error:function(XMLHttpRequest, textStatus, errorThrown){}
    });

}

function saveLedgerPosition(mkr){
    var x = jQuery.trim(jQuery("#xy_lng").html());
    var y = jQuery.trim(jQuery("#xy_lat").html());
    var ledgerId = jQuery("#ledgerIdSelect").val();
    if(ledgerId == undefined || ledgerId == ""){
        Common.Xtip({msg:selectCompanyTip});
        return;
    }
    var ledgerName = jQuery("#ledgerId").val();
    jQuery.ajax({
        type: "POST",
        url: contextPath+"/ledgermanager/saveLedgerPosition.htm",
        data:{
            ledgerId: ledgerId,
            x : x,
            y : y
        },
        success: function(msg){
            if(msg.isSuccess){
                Common.Xtip({type:"ok",msg:"设置成功"});  //弹出提示
                allmap.closeInfoWindow();  //关闭窗口

                mkr.ledgerId = ledgerId;  ///将ledgerId注入到mkr中
                markerEvent(mkr);  ///mkr注入事件
                var label = new BMap.Label(ledgerName, {offset:new BMap.Size(19,-10)});
                mkr.setLabel(label);
                mkr.attributes = {myLabel: label};    ///mkr设置文字标签

                markerClusterer.addMarker(mkr);   //聚合标记处理
            }
            else {
                Common.Xtip({type:"error",msg:"设置失败"});  //弹出提示
                allmap.closeInfoWindow();  //关闭窗口
                allmap.removeOverlay(mkr);  //删除mkr标记
            }
        },
        error:function(XMLHttpRequest, textStatus, errorThrown){}
    });
}

// mkr标记de事件注册(使用该方法前确保ledgerId已注入到mkr对象中)
function markerEvent(mkr){
    mkr.addEventListener("click",clickMarker);   //单击

    var markerMenu=new BMap.ContextMenu();
    if(checkIeInfo()){   //IE8 以下不支持右键菜单
        markerMenu.addItem(new BMap.MenuItem('删除',deleteMarker.bind(mkr)));
    }
    mkr.addContextMenu(markerMenu);              //右键删除

    mkr.enableDragging();
    mkr.addEventListener("dragend", dragMarker);  //拖拽
}

function deleteMarker(e,ee,mkr){
    var ledgerId = mkr.ledgerId;
    if(ledgerId != undefined){
        //删除数据库中坐标
        jQuery.ajax({
            type: "POST",
            url: contextPath+"/ledgermanager/deleteLedgerPosition.htm",
            data:{
                ledgerId: ledgerId
            },
            success: function(msg){
                Common.Xtip({type:"ok",msg:"删除成功"});
            },
            error:function(XMLHttpRequest, textStatus, errorThrown){}
        });
    }

    allmap.removeOverlay(mkr);  //删除地图中的标记
    markerClusterer.removeMarker(mkr);  //聚合标记处理
}

function dragMarker(e){
    var ledgerId = e.target.ledgerId;
    if(ledgerId != undefined){
        jQuery.ajax({
            type: "POST",
            url: contextPath+"/ledgermanager/saveLedgerPosition.htm",
            data:{
                ledgerId: ledgerId,
                x : e.point.lng,
                y : e.point.lat
            },
            success: function(msg){
                if(msg.isSuccess){
                    Common.Xtip({type:"ok",msg:"拖拽成功"});
                }
                else {
                    Common.Xtip({type:"error",msg:"拖拽失败"});
                }
            },
            error:function(XMLHttpRequest, textStatus, errorThrown){}
        });
    }
}

function clickMarker(e){
    var mkr = e.currentTarget;
    var ledgerId = e.target.ledgerId;
    if(ledgerId != undefined){
        jQuery.ajax({
            type: "POST",
            url: contextPath+"/ledgermanager/getLedgerUseData.htm",
            data:{
                ledgerId: ledgerId
            },
            success: function(msg){
                var ledgerName = msg.ledger.ledgerName;
                var picUrl = msg.ledger.showPicUrl;
                if(picUrl == undefined || picUrl == ""){
                    picUrl = contextPath+"/energy/images/index-page/defaultMapPic.png";
                }
                else {
                    picUrl = contextPath+'/ledgermanager/readThirdCompanyImg.htm?fileName=' + picUrl;
                }
                var eleStr = msg.ele + " " + msg.eleUnit;
                var waterStr = msg.water + " " + msg.waterUnit;
                var gasStr = msg.gas + " " + msg.gasUnit;
                var hotStr = msg.hot + " " + msg.hotUnit;
                var sContent =
                    "<h3 style='font-size: 16px;margin:0 0 5px 0;text-align: center;'>" + ledgerName + "</h3>" +
                    "<div style='float:left;font-size:13px;width:180px;'>" +
                     "<div style='text-align:left;'>上月能耗数据如下:</div>";
                if(jQuery(".message_select input.ele_box").attr("checked") == "checked"){
                    sContent = sContent + "<div style='text-align:center;float:left;width:45%;'>电</div>" + "<div style='text-align:left;float:left;width:55%;'>" + eleStr + "</div>";
                }
                if(jQuery(".message_select input.water_box").attr("checked") == "checked"){
                    sContent = sContent + "<div style='text-align:center;float:left;width:45%;'>水</div>" + "<div style='text-align:left;float:left;width:55%;'>" + waterStr + "</div>";
                }
                if(jQuery(".message_select input.gas_box").attr("checked") == "checked"){
                    sContent = sContent + "<div style='text-align:center;float:left;width:45%;'>气</div>" + "<div style='text-align:left;float:left;width:55%;'>" + gasStr + "</div>";
                }
                if(jQuery(".message_select input.hot_box").attr("checked") == "checked"){
                    sContent = sContent + "<div style='text-align:center;float:left;width:45%;'>热</div>" + "<div style='text-align:left;float:left;width:55%;'>" + hotStr + "</div>";
                }
                sContent = sContent + "</div>"
                    + "<img style='float:left;' id='ledgerImg' src='" + picUrl + "' width='140' height='100'/>";
                var infoWindow = new BMap.InfoWindow(sContent, {offset: new BMap.Size(0, -10)});  // 创建信息窗口对象
                infoWindow.setWidth(330);
                infoWindow.setHeight(150);
                mkr.openInfoWindow(infoWindow);                  //打开信息窗口
                infoWindow.redraw();                             //重绘

            },
            error:function(XMLHttpRequest, textStatus, errorThrown){}
        });
    }
}

function autoTapSearchSet(){
    var selectType = jQuery("select[name='searchModel'] option:selected").val();
    if(selectType == 1 || selectType == 2 ){
        $(".mapWrap .mapWrap_1 .map_search #searchModel").css("width", "40px");
    }
    else if(selectType == 3){
        $(".mapWrap .mapWrap_1 .map_search #searchModel").css("width", "95px");
    }
    else if(selectType == 4){
        $(".mapWrap .mapWrap_1 .map_search #searchModel").css("width", "54px");
    }
    jQuery(".map_search  .lotTap").hide();
    jQuery(".map_search  .lotTap_" + selectType).show();
}

//如果是IE，判断是否是IE8以下
function checkIeInfo(){
    var result = true;
    var agent = navigator.userAgent.toLowerCase() ;
    //IE
    if(agent.indexOf("msie") > 0)
    {
        var regStr_ie = /msie [\d.]+;/gi ;
        var browser =  agent.match(regStr_ie);
        var version = (browser+"").replace(/[^0-9.]/ig,"");
        if(Number(version) <= 8){
            result = false;
        }
    }
    return result;
}