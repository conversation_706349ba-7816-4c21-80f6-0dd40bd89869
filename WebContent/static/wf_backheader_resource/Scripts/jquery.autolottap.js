(function($){
    //自动完成移开消失
    $(".autoLotTap").live("mouseleave",function(){
        $(this).find(".autoLotTap-main").hide();
        $(this).find(".autoLotTap-show input").blur();
    });

	$.fn.autoLotTap = function(settings) {
		var conf = {
			/*
				设置此下拉框是否有分页
			*/
			ispaging:true,
			paging:{
				pagesize:10
			},
			/*  否支持多选
				默认为false,不支持多选
				true,多选，可设置maxnum,为选择上限
			*/
			multiple:false,
			maxNum:99,
			//初始化数据的URL地址
			url:'',
			data:{},
			//联动下拉点击次数提交路径
			tourl:'',
			//count_url:'',
			/*  json格式
				id为每个选项的ID,默认为"id"
				value为每个选项的值,默认为'value'
				label为每个选项显示的值，默认为'label'
				num为数字搜索,默认为'num'
			*/
			jsonStyle:{
				id:'id',
				value:'value',
				label:'label',
				num:'num'
			},
			relative:false,
			/*只有在单选时回调函数才能被执行*/
			callback:function(){},
			/*是否设置第一个值为初始值*/
			isSetValue:false
		};
		
		// overwrite default config
		if (settings) {
			$.extend(conf, settings);
		}
		this.each(function(){
			//初始化联动下拉框
			//$(this).hide();
			var val= $("input[name='"+$(this).attr("name")+"']").val();
			//保存每个联动下拉点击次数提交的路径
			$("input[name='"+$(this).attr("name")+"']").attr("tourl",settings.tourl);
			initialization($(".autoLotTap[attr='"+$(this).attr("class")+"']"),$(".autoLotTap[id='"+$(this).attr("name")+"']"),conf,val);
			/*控制数据显示区的显隐*/
			$(".auto-input").focus(function(){
				$(".autoLotTap-main").hide();
				if($(this).parent().next().css("display") == "none"){
					$(this).parent().next().show();
					var tabObj = $(this).parent().parent();
				}
			});
			/*
				显示选中数据
				并为相应文本框赋值
			*/
			/*若只能显示一条数据*/
			if( !conf.multiple ){
				$(".autoLotTap[attr='"+$(this).attr("class")+"']").on('click',"li",function(){
					//if($(this).text() != '无对应数据' ){
				    if(!$(this).hasClass("autoLotTpa-error")){	
						var tabObj = $(this).parents('.autoLotTap');
						var text = $(this).attr("title")==null||$(this).attr("title")==""?$(this).text():$(this).attr("title");
						$(this).parents('.autoLotTap').find("input").val(text);
						var obj = $("input[name='"+$(this).parents('.autoLotTap').attr("id")+"']");
						obj.val($(this).attr("id"));
						//加入回调函数
						if(conf.callback !=""){
							conf.callback($(this).attr("id"));	
						}
						$(this).parents('.autoLotTap').find(".autoLotTap-main").hide();			
					}
				});
			/*支持多选*/
			}else{
				$(".autoLotTap[attr='"+$(this).attr("class")+"']").on('click',"li",function(){
					if($(this).hasClass("autoLotTpa-error")) {
						return;
					}
	
					var li_id=$(this).attr("id");
					var flag = true;
					var tabObj = $(this).parents('.autoLotTap');
					//不添加重复元素
					tabObj.find("p").each(function(){
						if($(this).attr("attr") == li_id){
							flag = false;
						}
					});
					if(flag){	
						var showData;
						//截取字符长度
						var maxSize = Math.floor(($(".autoLotTap-main").css("width").replace("px","")-15)/$(".autoLotTap-main ul").css("font-size").replace("px",""));
						var text;
						if($(this).attr("title").length>=parseInt(maxSize)-4){
							text = $(this).attr("title").substring(0,parseInt(maxSize)-4)+"...";
						}else if($(this).attr("title")==null||$(this).attr("title")==""){
							text = $(this).text();
						}else{
							text=$(this).attr("title")	
						}
						if($(this).attr("title")!=null ||$(this).attr("title") !=""){
							showData = "<p class='ue-clear' attr='"+$(this).attr("id")
							 +"'><span class='left text' title='"+$(this).attr("title")+"'>"+ text
							 + "</span><span class='right close pt6'></span></p>";
						}else{
							showData = "<p class='ue-clear' attr='"+$(this).attr("id")
							 +"'><span class='left text' title='"+$(this).attr("title")+"'>"+ text
							 + "</span><span class='right close pt6'></span></p>";
						}				
	
						tabObj.find("input").before(showData);
						var obj = $("input[name='"+$(this).parents('.autoLotTap').attr("id")+"']");
						var value=(obj.val()==null||obj.val()=="")?obj.val():obj.val()+",";
						obj.val(value+ $(this).attr("id") );
					}
					/*重新刷新数据缓存*/
					tabObj.data("city").cityPart = null;
					tabObj.data("city").paging.pageno = 1;
					tabObj.data("city").paging.total = tabObj.data("city").cityAll.length;
					createData(0,tabObj,conf);
					if(conf.ispaging){
						/*初始化分页*/
						setPage(tabObj);
					}
	
					//清空输入框中的内容
					tabObj.find("input").val("");
					tabObj.find(".autoLotTap-main").hide();
				});
			}
			
			/*	
				根据输入框的输入内容的变化，改变下拉列表
				当输入框为空时，重新初始化下拉列表，为空的情况有两种（第一次聚焦输入框或者对输入框的内容删除为空）
			*/
			$(".autoLotTap input").unbind('keyup').keyup(function(){
				var trigger = $(this).parent().parent();
				/*
					若输入框中的内容为空并不为第一次聚焦时,重新加载后台所有数据
				*/
				if($(this).val() == "" && trigger.find("li").size() <= 0){
					createData(0,trigger,conf);
					trigger.data("city").cityPart = null;
	
				}else{
					//如果是单选时，将内容全部清除的话id也要全部清除
					$("input[name='"+trigger.attr("id")+"']").val("");
					/*若输入框中有输入内容*/
					trigger.find("ul").empty();
					var cityPart = new Array();
					var cityAll = trigger.data("city").cityAll;
					for(var i = 0; i < cityAll.length ; i++){
						if(cityAll[i].label.indexOf($(this).val())>= 0 ||cityAll[i].value.indexOf($(this).val().toUpperCase())>= 0 ||(cityAll[i].num && cityAll[i].num.indexOf($(this).val())>=0)){
							cityPart.push(cityAll[i]);
						}
					}
					
					/*刷新缓存数据*/
					trigger.data("city").cityPart = cityPart;
					trigger.data("city").paging.pageno = 1;
					trigger.data("city").paging.total = cityPart.length;
					createData(0,trigger,conf);
					if(conf.ispaging){
						/*初始化分页*/
						setPage(trigger);
					}
				}
			});
			
			/*上一页*/
			$(".autoLotTap-main-paging .prev").unbind('click').click(function(){
				var trigger = $(this).parents(".autoLotTap");
				var paging = trigger.data("city").paging;
				/*隐藏数据集合*/
				if(paging.pageno != 1){
					/*清空原有数据结合*/
					$(this).parents('.autoLotTap').find("ul").empty();
					paging.pageno = paging.pageno - 1;
					$(this).parents('.autoLotTap').find(".pageno").text(paging.pageno);
					/*生成新的数据集合*/
					createData((paging.pageno - 1)*paging.pagesize,trigger,conf);
				}
				/*刷新缓存中的数据*/
				trigger.data("city").paging = paging;
			});
			
			/*下一页*/
			$(".autoLotTap-main-paging .next").unbind('click').click(function(){
				var trigger = $(this).parents(".autoLotTap");
				var paging = trigger.data("city").paging;
				/*隐藏数据集合*/
				if(paging.pageno != Math.ceil(paging.total / paging.pagesize)){
					/*清空原有数据结合*/
					$(this).parents('.autoLotTap').find("ul").empty();
					paging.pageno = paging.pageno + 1;
					$(this).parents('.autoLotTap').find(".pageno").text(paging.pageno);
					/*生成新的数据集合*/
					createData((paging.pageno - 1)*paging.pagesize,trigger,conf);
				}
				/*刷新缓存中的数据*/
				trigger.data("city").paging = paging;
			});
		});
		//form表单提交前的下拉列表点击次数的统计，此处只提交最后选中的有效点击
		$("input:submit,button:submit").unbind("click").click(function(){
			var tourl = null;
				var data={};
				var KV= new Array();
				$(".autoLotTapInput").each(function(){
					tourl=$(this).attr("tourl");
					if(tourl!=undefined && tourl !=null && tourl!=""){
						var tmp={};
						tmp.name=$(this).attr("name");
						tmp.value=$(this).val();					
						$(this).val()!=null&&$(this).val()!=""?KV.push(tmp):"";	
					}
				});
				data.sortid=KV;	    
				$.ajax({
					 async:false,
					 url:tourl,
					 data:data,
					complete:function(result){}
				 }); 
			$(this).submit;
		});
		
		return this;		
	};
	
	//初始化联动下拉菜单数据
	function initialization(trigger,input_obj,conf,val){
		var cities = new Array();
		var cityPart = null; 
		//生成下拉选择框，同时需要兼容老版
		if(trigger.find(".autoLotTap-main").html()==null){
			var html= "";
			if(conf.ispaging){
				html="<div class='autoLotTap-main'><div class='autoLotTap-main-choose'><ul></ul></div>"
					+"<div class='autoLotTap-main-paging ue-clear'><span class='left text'>总共:<span class='total'>"
					+"</span>条 &emsp;<span class='pageno'></span>/<span class='totalpages'></span></span>"
					+"<span class='right pt6'><span class='prev'></span><span class='next'></span></span></div></div>";	
			}else {
				html="<div class='autoLotTap-main'><div class='autoLotTap-main-choose'><ul></ul></div>"
					+"</div>";	
			}
			
			trigger.append(html);
		}
		//当此class类的数据未被加载过，则加载
		if(trigger.data("city")==null || trigger.data("city") == undefined){
			$.ajax({
				async:false,
				url:conf.url,
				data:conf.data,
				dataType:"json",
				type:'post',
				success:function(result){
					var jsondata = eval(result);
					$.each(jsondata,function(index,content){
						//赋初始值，把取到的第一值设为初始值
						if(conf.isSetValue && index == 0 ){
							val = content[conf.jsonStyle.id];
						}
						var city ={};
						city.id=content[conf.jsonStyle.id];
						city.label=content[conf.jsonStyle.label];
						city.value=content[conf.jsonStyle.value];
						city.num = content[conf.jsonStyle.num] || null;
						cities.push(city);		            	
					});
					//当最开始input有初始化id时
				    intV_input(trigger,input_obj,cities,val,conf)
					var paging = new Object();
			
					/*存储数据
					  cityAll:初始化时的所有数据
					  cityPart:用户输入时，提示的数据集合
					  paging:组件分页情况
					*/
					var callBack = new function(){};
					trigger.data(
						"city",{
							"cityAll":cities,
							"cityPart":cityPart,
							"paging":paging,
							"multiple":conf.multiple,
							callBack:conf.callBack
						}
					);
					if(conf.ispaging){
						/*
							分页数据
							pagesize:每页数据条数
							pageno:当前页码数
							totalpages:总页数
							total:数据总量
						*/
						trigger.data("city").paging = {
							"pagesize":conf.paging.pagesize,
							"pageno":1,
							"total":cities.length
						};
						/*初始化分页*/
						setPage(trigger);
					}
					/*初始化数据*/
					createData(0,trigger,conf);
			  	},
			  	error:function(XMLHttpRequest, textStatus, errorThrown){
			  		
			  	}
			});		
		}else{//当此class类的数据未被加载过，则加载
			//当最开始input有初始化id时
			intV_input(trigger,input_obj,trigger.data("city").cityAll,val,conf)
		}
		
	}
	
	/*
		为下拉列表填数据
		start:开始显示的数据索引
		trigger:下拉菜单对象
	*/
	function createData(start,trigger,conf){
		var paging = trigger.data("city").paging;
		var cityAll = trigger.data("city").cityAll;
		var cityPart = trigger.data("city").cityPart;
		
		var array = (cityPart == null) ? cityAll : cityPart;
		trigger.find("ul").empty().show();
		if(array.length <= 0){
			trigger.find("ul").append("<li class='autoLotTpa-error'>无对应数据</li>");
			trigger.find(".autoLotTap-main-paging>.right").hide();
		}else{
			trigger.find(".autoLotTap-main-paging>.right").show();
			var maxSize = Math.floor(($(".autoLotTap-main").css("width").replace("px","")-15)/$(".autoLotTap-main ul").css("font-size").replace("px",""));	
			if(conf.ispaging){
				for(var i=0;i<paging.pagesize;i++){
					if(array[start+i]){
						//判断显示的文本内容超过,显示内容的自动截取				 		 
						if(array[start+i].label.length>=parseInt(maxSize)-1){
							trigger.find("ul").append("<li id='"+array[start+i].id+"' title='"+array[start+i].label+"'>"+ array[start+i].label.substring(0,parseInt(maxSize)-1) + "...</li>");
						}else{
							trigger.find("ul").append("<li id='"+array[start+i].id+"' title='"+array[start+i].label+"'>"+ array[start+i].label + "</li>");
						}
					}
				}
			}else {
				for(var i=0;i<array.length;i++){
					//判断显示的文本内容超过,显示内容的自动截取				 		 
					if(array[i].label.length>=parseInt(maxSize)-1){
						trigger.find("ul").append("<li id='"+array[i].id+"' title='"+array[i].label+"'>"+ array[i].label.substring(0,parseInt(maxSize)-1) + "...</li>");
					}else{
						trigger.find("ul").append("<li id='"+array[i].id+"' title='"+array[i].label+"'>"+ array[i].label + "</li>");
					}
				}
			}
			
		}
	}
	/*设置分页数据*/
	function setPage(trigger){
		var paging = trigger.data("city").paging;
		trigger.find(".total").text(paging.total);
		trigger.find(".pageno").text(paging.pageno);
		trigger.find(".totalpages").text(Math.ceil(paging.total / paging.pagesize));
	}
	//当最开始input有初始化id时
	function intV_input(trigger,input_obj,cities,val,conf){			
		if(val !=null && val!=""){
			$("input[name='"+trigger.attr("id")+"']").val(val);
			/*删除相应数据*/
			trigger.find(".autoLotTap-show p").on('click',".close",function(){
				var obj1 = $("input[name='"+$(this).parents('.autoLotTap').attr("id")+"']");
				obj1.val().substring(obj1.val().length-1)==","?"":obj1.val(obj1.val()+",");
				obj1.val((obj1.val()).replace($(this).parent().attr("attr")+"," , ""));
				$(this).parent().remove();
				obj1.val().substring(obj1.val().length-1)==","?obj1.val(obj1.val().substring(0,obj1.val().length-1)):"";
			});
			var n=0;
			for(var i=0;i<cities.length;i++){
				//多选
				if(conf.multiple){
			  		 var ids =new Array();
			  		 ids = val.split(",");
			   		 for(var j=0;j<ids.length;j++){
						if(cities[i].id==ids[j]){
							var showData;
							var maxSize = Math.floor(($(".autoLotTap-main").css("width").replace("px","")-15)/$(".autoLotTap-main ul").css("font-size").replace("px",""));
							if(cities[i].label.length>=parseInt(maxSize)-4){
								showData = "<p class='ue-clear' attr='"+ids[j]
								+"'><span class='left text' title='"+cities[i].label+"'>"+ cities[i].label.substring(0,parseInt(maxSize)-4)
								+ "...</span><span class='right close pt6'></span></p>";
							}else{
								showData = "<p class='ue-clear' attr='"+ids[j]
								+"'><span class='left text' title='"+cities[i].label+"'>"+ cities[i].label
							    + "</span><span class='right close pt6'></span></p>";
							}	
							input_obj.find(".auto-input").before(showData);
							n++;
							break;
						}
			  		 }
			  	 	 if(n==ids.length)break;
				 }else{
					 //单选
					 if(cities[i].id==val){
						 input_obj.find(".auto-input").val(cities[i].label);
						 break;
					 }
				  }
			}
		}	
	}
})(jQuery);