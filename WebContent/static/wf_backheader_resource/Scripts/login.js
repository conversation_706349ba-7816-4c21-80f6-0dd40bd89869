//if(top!=self){//跳出框架
//    if(top.location != self.location)
//         top.location=self.location; 
// }
$(document).ready(function(){
	//显示下拉框
	if(Common.IsUndefined(language) == "")
		language="zh_CN";
	if(!isBrowerValidate()){
		//验证浏览器不通过处理
		$("#loginArea").hide();
		$("#download").show();
	}else{
		//自动检测浏览器语言
		getSupportLanguage(language);
	}
        if(Common.Cookie.get("user")){
            var userInfo = Common.Cookie.get("user").split("-");
            if(userInfo.length == 3){
                $("#username").val(userInfo[0]);
                $("#password").val("");
                $("#roleType").val(userInfo[2]);
            }
        }
        $("select[name='select']").selectCss();
});

$("#download").click(function(){
	window.location.href = contextPath+"/energy/framework/brower.zip";	
});
  	
$(".language").live("click",function(){
	var lang = $(this).attr("language");
	changeLanguage(lang);
});

$("#reset").click(function(){
	$("#username").val("");
  	$("#password").val("");
});
$("#tips_link").click(function(){alert(contextPath);
	window.location.href = contextPath+"/energy_tips/login.html";	
	});
function getSupportLanguage(language){
	$.ajax({
		type: "POST",
		url: contextPath+"/commonController/getDataCodeDataList.htm",
		data: "dataCode=dynamic_language",
		success: function(msg){
			if($(msg).length > 0){
				var html =[];
				var length = $(msg).length-1;
				var count = 0;
				var chinaHtml="";
				$(msg).each(function(){
					count++;
					var id = Common.IsUndefined(this.ID);
					var value = Common.IsUndefined(this.VALUE);
					if(id =="zh_CN")
						chinaHtml="<span class=\"language\" language=\""+id+"\" >"+value+"</span><span>&nbsp;|&nbsp;</span>";
					else{	
						if(length != count)
							html.push("<span class=\"language\" language=\""+id+"\" >"+value+"</span><span>&nbsp;|&nbsp;</span>");
						else
							html.push("<span class=\"language\" language=\""+id+"\" >"+value+"</span>");
					}
				});
				$("#languageDiv").append(chinaHtml+html.join(""));
			}
		},
		error:function(XMLHttpRequest, textStatus, errorThrown){
		
		} 
	});

}
			
			
function changeLanguage(language){
	$.ajax({
		type: "POST",
		url: contextPath+"/commonController/setDynamicLanguage.htm",
		data: "language="+language,
		success: function(msg){
			self.location.href=contextPath+"/frameController/showLoginPage.htm?language="+language;
		},
		error:function(XMLHttpRequest, textStatus, errorThrown){
		
		} 
	});
}
			
 function isBrowerValidate(){
	var isie = true;
	if ( $.browser.msie ){
		if( $.browser.version =="6.0" || $.browser.version =="7.0"){
			isie = false;
		}
		}else{	//非IE内核浏览器,暂时不处理，默认可以
			isie = true;
		}
	return isie;
}
function fireFoxHandler(evt){
	if(evt.keyCode==13){
		login();
	}
}
	
function ieHandler(evt){
	if(evt.keyCode==13){
		login();
	}
}
	
$("#login").click(function(){
	login();
});

function guestEnter(){
    $("#showPwd").addClass("onfocus_text");
    $("#password").addClass("onfocus_text");
    $("#password").show();
    $("#showPwd").hide();
    $("#username").val("guest");
    $("#password").val("guest");
}

function login(){
	var username = $.trim($("#username").val());
	var password = $.trim($("#password").val());
        var roleType = $("#roleType").val();
	if(username == ""){
		$("#loginErrorMsg").addClass("bg_show");
		$("#loginErrorMsg").html(usernameEmpty);
		document.getElementById("username").focus();
		return;
	}
	if(password == ""){
		$("#loginErrorMsg").addClass("bg_show");
		$("#loginErrorMsg").html(passwordEmpty);
		//document.getElementById("password").focus();
		return;
	}	
	$("#loginErrorMsg").removeClass("bg_show");
	$("#loginErrorMsg").html("");
	//验证自己写
	$.ajax({
		type: "POST",
		url: contextPath+"/frameController/login.htm",
		data: "username="+username+"&password="+password+"&roleType="+roleType+"&loginPath="+loginPath,
		success: function(msg){
			if(msg.errorCode == 4){//登录成功
                //设置cookies保存用户信息
                var options = {
                    name:"user",
                    value:msg.user,
                    expireDays:"36500"
                }
                Common.Cookie.put(options);
				//设置cookies用来自动登录
				if($("#autoLogin").attr("checked") == "checked"){
	                var options = {
	                    name:"auto_login",
	                    value:1,
	                    expireDays:"14"
	                }
	                Common.Cookie.put(options);
				}
				
				var opts = {
					name:"isFirst",
					value:msg.isFirst
				}
				Common.Cookie.put(opts);
				// 设置登陆方式
				var loginOptions = {
					name:"loginPath",
					value:loginPath,
					expireDays:"36500"
				}
				Common.Cookie.put(loginOptions);
				
				if (window.name == "能效平台") {
					window.location.href = contextPath+"/index/showIndexPage.htm";
				} else {
					var flag = window.open(url,"","");  
		            if(flag == null) {  
		            	//alert("您的浏览器启用弹出窗口过滤功能！\n请暂时先关闭此功能！");
		            	window.location.href = contextPath+"/index/showIndexPage.htm";
		            	return;
		            } else {
		            	flag.opener=null;
		            	flag.close();
		            }
					var url = contextPath+"/index/showIndexPage.htm";
					//window.open(url,"能效平台","scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no,fullscreen=yes");
//					window.open(url,"能效平台","height="+screen.height+",width="+screen.width+",top=0,left=0,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no");
//					window.opener=null;
//					window.open('','_self');
//					window.close();
					window.location.href = url;
				}
			}else{
				var errorMsg ;
				//登录失败提示
				if(msg.errorCode == 1){//用户不存在
					errorMsg = usernameError;
				}else if(msg.errorCode == 2){//密码错误
					errorMsg = passwordError;
				}else if(msg.errorCode == 3){//被禁用或者删除
					errorMsg = disableError;
				}
				$("#loginErrorMsg").addClass("bg_show");
				$("#loginErrorMsg").html(errorMsg);
			}
		},
		error:function(XMLHttpRequest, textStatus, errorThrown){
		
		} 
	});

}
