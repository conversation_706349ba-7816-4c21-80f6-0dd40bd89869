/*
@desc:公用请求
@author:Cherry
@date:2013-12-3
*/

var Common={
	
	/*
	静态数据
	*/
	StaticData:{
		cityName:"南京",
		//间隔时间
		INTERVALTIME:3000,
		//轮询次数
		CYCLECOUNT:10,
		//是否已经打开
		IS_OPENED:false
	},
	/*
	加载下拉框
	*/
	AjaxSelect:function(options){
		var _this = this;
		var defaults={
			//选择器 jQuery选择器
			expr:"",
			//请求url
			url:"commonController/getDataCodeDataList.htm",
			//请求参数
			data:"",
			//请求方式
			type:"GET",
			//默认值 select的默认选中项
			defaultValue:null,
			//是否需要"请选择"
			isNeedBlank:true,
			//是否异步
			isAsync:true,
			//列名
			column:{
				//ID
				id:"ID",
				//值
				value:"VALUE"
			},
			//显示信息
			show:function(msg){
				var _this=this;
				
				_this.callBack(msg);
				//下拉列表样式
				//$(settings.expr).iSelect();
				_this.afterCallBack(msg);
			},
			//fn(msg) 数据显示回调函数 msg ajax返回的数据
			callBack:function(msg){
				$(settings.expr).empty().append(function(){
				var html="";
							
				if(settings.isNeedBlank)
					//html+="<option value=\"\" selected=\"selected\">---请&nbsp;选&nbsp;择---</option>";
					//html+="<option value=\"\"></option>";		
				$(msg).each(function(){
					if(this[settings.column.id]==settings.defaultValue)
						html+="<option selected=\"selected\" value=\""+this[settings.column.id]+"\" selected=\"selected\">"+this[settings.column.value]+"</option>";
					else
						html+="<option value=\""+this[settings.column.id]+"\">"+this[settings.column.value]+"</option>";
				});
				
				return html;
				
				});
			},
			//数据显示完回调函数
			afterCallBack:function(msg){$("select").selectCss(); }
		};
			
		var settings=$.extend(defaults,options);
		settings.url = _this.getRootPath()+"/"+settings.url;
		$.ajax({
			type: settings.type,
			url: settings.url,
			data: settings.data,
			async: settings.isAsync,
			success: function(msg){
				//显示数据
	   			settings.show(msg);
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){
				$(settings.expr).empty().append("");
			} 
		});
	},
	/*
	调用分页控件
	*/
	Page:{
		/*
		显示分页控件
		*/
		show:function(){
			$("#pagination").show();
		},
		/*
		调用分页控件接口函数
		*/
		page:function(page,callBack,options){
			//分页控件默认配置参数
			var defaults={
				//是否显示分页信息
				display_msg:true,
				//分页控件Id
				id:"pagination"
			}
			
			var settings=$.extend(defaults,options);
			
			$("#"+settings.id).pagination(page.rowCount, {
				num_edge_entries:2,
				current_page:page.pageIndex-1,
				num_display_entries:3,// 中间显示页码的个数
				num_edge_entries : 1, // 末尾显示页码的个数
				items_per_page:page.pageSize,
				display_msg:settings.display_msg,
				callback:function(){callBack();return false;}
			});
		},
		/*
		隐藏分页控件
		*/
		hide:function(){
			$("#pagination").hide();
		}
	}, 
	/*
	判断str是否未定义 如果为"undefined" 返回空字符串
	如果不为"undefined" 则返回str的值
	*/
	IsUndefined:function(str){
		if(str == null){
			return "";
		}
		if(str == undefined){
			return "";
		}
		if(str == "undefined"){
			return "";
		}
		return typeof(str)=="undefined"?"":str;
	},
	
/*
	信息提示
	*/
	Msg:function(options){
		var defaults={
			//选择器
			expr:"",
			//一般提示信息容器为隐藏
			//是否需要强制显示 使用.show()方法
			show:true,
			//信息
			msg:"",
			//显示后是否需要延迟清除
			lazyClean:false,
			//延迟清除时间，在cleanTime后清除提示信息
			cleanTime:2000,
			//清除
			clean:false,
			//回调函数
			callBack:function(){}
		}
		
		var settings=$.extend(defaults,options);
		
		if(settings.clean)
				$(settings.expr).empty();
		else
		{	
			if(settings.show)
				$(settings.expr).show().empty().append(settings.msg);
			else
				$(settings.expr).empty().append(settings.msg);
			if(settings.lazyClean)
				setTimeout(function(){$(settings.expr).empty();settings.callBack()},settings.cleanTime);
			else
				settings.callBack();
		}
	},
	
	/**
		将数据库中存储的long IP地址转换为点分格式的IP地址
	*/
	FormatIP:function(options){
		var defaults={
			//要转换的值
			value:""
		}
		var settings=$.extend(defaults,options);
		
		var output = false;
		var longip = settings.value;
		if(!isNaN(longip)){
			 output = "0x"+parseInt(longip).toString(16);		
			 output = ((longip>>24)&0xff)+"."+((longip>>16)&0xff)+"."+((longip>>8)&0xff)+"."+(longip&0xff);
			}
		return output;
	
	},
	
	/*
	将数据库中存储的long时间转换为时间格式
	*/
	FormatDate:function(options){
	
		var defaults={
			//要转换的值
			value:"",
			//格式化pattern
			//short 短日期格式 2009-10-10
			//long 长日期格式 2010-11-04 09:29:34
			pattern:"short",
			//分割符号
			separator:"-"
		}
		
		var settings=$.extend(defaults,options);
		
		var date=new Date();
		date.setTime(settings.value*1000);
		
		if(settings.value>0)
		{
			var rValue="";
			
			switch(settings.pattern)
			{
				case "short":
							rValue=date.getFullYear()+settings.separator+
							((date.getMonth()+1)<10?"0"+(date.getMonth()+1):(date.getMonth()+1))+settings.separator+
							(date.getDate()<10?"0"+date.getDate():date.getDate());
							break;
				case "long":
							rValue=date.getFullYear()+settings.separator+
							((date.getMonth()+1)<10?"0"+(date.getMonth()+1):(date.getMonth()+1))+settings.separator+
							(date.getDate()<10?"0"+date.getDate():date.getDate())+" "+
							(date.getHours()<10?"0"+date.getHours():date.getHours())+":"+
							(date.getMinutes()<10?"0"+date.getMinutes():date.getMinutes())+":"+
							(date.getSeconds()<10?"0"+date.getSeconds():date.getSeconds());
							break;
			}
				
			return rValue;
		}
		else
			return settings.value;
	},
	/*
	将日期转换为Long 日期格式如2010-01-01 或 2010-11-04 09:29:34
	*/
	FormatDateToLong:function(options){
		var defaults={
			//要转换的值
			value:"",
			//分割符号
			separator:"-"
		}
		
		var settings=$.extend(defaults,options);
		
		if(settings.value.length==0)
			return "";
		if(settings.value.length<8)
			return "";
		
		//去除日期的separator 年月日部分
		var reg=new RegExp(settings.separator,"g");
		
		if(!reg.test(settings.value))
			return "";
			
		var dataString=settings.value.replace(reg,"");
		var date=new Date();
		//设置年月日
		//date.setDate(new Number(dataString.substring(6,8)));
		date.setMonth(new Number(dataString.substring(4,6))-1,new Number(dataString.substring(6,8)));
		//date.setMonth(new Number(dataString.substring(4,6))-1);
		date.setYear(new Number(dataString.substring(0,4)));
		//设置时 分 秒 毫秒
		var hour=0;
		var minutes=0;
		var second=0;			
		if(dataString.length>8)
		{
			//去除日期时分秒中的空格和:
			dataString=dataString.replace(/[\s,\:]/g,"");
						
			hour=new Number(dataString.substring(8,10));
			minutes=new Number(dataString.substring(10,12));
			second=new Number(dataString.substring(12,14));	
		}
		date.setMilliseconds(0);
		date.setSeconds(second);
		date.setMinutes(minutes);
		date.setHours(hour);
		return (date.getTime()/1000).toFixed(0);
	},
	/*
	得到一段时间
	数据格式为 yyyy-MM-dd 月份和日如果不足两位会自动补零
	*/
	GetPartDate:function(options){
		var defaults={
			//yesterday 昨天
			//week 近一周
			//month 近一月
			//year 近一年
			section:"yesterday",
			//格式化pattern
			//short 短日期格式 2009-10-10
			//long 长日期格式 2010-11-04 09:29:34
			pattern:"short",
			//分割符号
			separator:"-"
		}
		
		var settings=$.extend(defaults,options);
		
		var date=new Date();
		
		switch(settings.section)
		{
			//今天
			case "today":
						break;
			//昨天
			case "yesterday":
						date.setDate(date.getDate()-1);
						break;
			//前天
			case "twodaysago":
						date.setDate(date.getDate()-2);
						break;
			//大前天
			case "threedaysago":
						date.setDate(date.getDate()-3);
						break;
			//近一周
			case "week":
						date.setDate(date.getDate()-7);
						break;
			//近一月
			case "month":
						date.setMonth(date.getMonth()-1);
						break;
			//近一年
			case "year":
						date.setFullYear(date.getFullYear()-1);
						break;
			//全部
			case "all":
						return {beginDate:"",endDate:""};
						break;
		}
		
		var now=new Date();
		
		switch(settings.pattern)
		{
			case "short":
						return {
							beginDate:date.getFullYear()+settings.separator+
									((date.getMonth()+1)<10?"0"+(date.getMonth()+1):(date.getMonth()+1))+settings.separator+
									(date.getDate()<10?"0"+date.getDate():date.getDate()),
							endDate:now.getFullYear()+settings.separator+
									((now.getMonth()+1)<10?"0"+(now.getMonth()+1):(now.getMonth()+1))+settings.separator+
									((now.getDate()-1)<10?"0"+(now.getDate()-1):(now.getDate()-1))
						};
			case "long":
						return {
							beginDate:date.getFullYear()+settings.separator+
									((date.getMonth()+1)<10?"0"+(date.getMonth()+1):(date.getMonth()+1))+settings.separator+
									(date.getDate()<10?"0"+date.getDate():date.getDate())+
									" 00:00:00",
							endDate:now.getFullYear()+settings.separator+
									((now.getMonth()+1)<10?"0"+(now.getMonth()+1):(now.getMonth()+1))+settings.separator+
									(now.getDate()<10?"0"+now.getDate():now.getDate())+" "+
									(now.getHours()<10?"0"+now.getHours():now.getHours())+":"+
									(now.getMinutes()<10?"0"+now.getMinutes():now.getMinutes())+":"+
									(now.getSeconds()<10?"0"+now.getSeconds():now.getSeconds())
						};
		}
	},
	/*
	得到未来一段时间
	数据格式为 yyyy-MM-dd 月份和日如果不足两位会自动补零
	*/
	GetPartFutureDate:function(options){
		var defaults={
			//today 今天
			//tomorrow 明天
			//week 一周
			//month 一月
			//year 一年
			section:"tomorrow",
			//格式化pattern
			//short 短日期格式 2009-10-10
			//long 长日期格式 2010-11-04 09:29:34
			pattern:"short",
			//分割符号
			separator:"-"
		}
		
		var settings=$.extend(defaults,options);
		
		var date=new Date();
		
		switch(settings.section)
		{
			//今天
			case "today":
						break;
			//明天
			case "tomorrow":
							date.setDate(date.getDate()+1);
							break;
			//一周
			case "week":
						date.setDate(date.getDate()+6);
						break;
			//一月
			case "month":
						date.setMonth(date.getMonth()+1);
						break;
			//一年
			case "year":
						date.setFullYear(date.getFullYear()+1);
						break;
		}
		
		var now=new Date();
		
		switch(settings.pattern)
		{
			case "short":
						return {
							endDate:date.getFullYear()+settings.separator+
									((date.getMonth()+1)<10?"0"+(date.getMonth()+1):(date.getMonth()+1))+settings.separator+
									(date.getDate()<10?"0"+date.getDate():date.getDate()),
							beginDate:now.getFullYear()+settings.separator+
									((now.getMonth()+1)<10?"0"+(now.getMonth()+1):(now.getMonth()+1))+settings.separator+
									(now.getDate()<10?"0"+now.getDate():now.getDate())
						};
			case "long":
						return {
							endDate:date.getFullYear()+settings.separator+
									((date.getMonth()+1)<10?"0"+(date.getMonth()+1):(date.getMonth()+1))+settings.separator+
									(date.getDate()<10?"0"+date.getDate():date.getDate())+
									" 00:00:00",
							beginDate:now.getFullYear()+settings.separator+
									((now.getMonth()+1)<10?"0"+(now.getMonth()+1):(now.getMonth()+1))+settings.separator+
									(now.getDate()<10?"0"+now.getDate():now.getDate())+" "+
									(now.getHours()<10?"0"+now.getHours():now.getHours())+":"+
									(now.getMinutes()<10?"0"+now.getMinutes():now.getMinutes())+":"+
									(now.getSeconds()<10?"0"+now.getSeconds():now.getSeconds())
						};
		}
	},
	getBeweenTimeMonth:function(options){
		var defaults={
			beginTime:"",
			endTime:"",
			//分割符号
			separator:"-"
		}
		var settings=$.extend(defaults,options);
		if(settings.beginTime =="" || settings.endTime =="")
			return [];
		 var arrA = settings.beginTime.split(settings.separator),
		        arrB = settings.endTime.split(settings.separator),
		        yearA = arrA[0],
		        yearB = arrB[0],
		        monthA = +arrA[1],
		        monthB = (yearB-(+yearA))*12+parseInt(arrB[1]),
		        returnArray = []
		    do{
		        do{
		            returnArray.push(yearA+""+(monthA > 9 ? monthA : "0"+monthA));
		            if(monthA == 12){
		                monthA=1;
		                monthB -= 12;
		                break;
		            }
		        }while(monthB > monthA++)
		    }while(yearB > yearA++)
		  return returnArray;
	},
	/*
	显示xTip
	options 参数 object
	*/
	Xtip:function(options){
		var defaults={
			//默认容器jquery expr
			expr:"body",
			//xTip 类型 warn警告 成功ok
			type:"warn",
			//msg 要显示的信息
			msg:""
		}
			
		var settings=$.extend(defaults,options);
		
		var type="";
		switch(settings.type)
		{
			case "warn":
				type="quickTipWarn";
				break;
			case "ok":
				type="quickTipSuccess";
				break;
			case "error"://错误
				type="quickTipError";
				break;
		}
		
		$(settings.expr).xTip({type:type,message:settings.msg});
	},
	
	getRootPath: function(){
		 //获取当前路径
	    var curPath = window.document.location.href;
	    //获取主机地址之后的目录
	    var pathName = window.document.location.pathname;
	    var pos = curPath.indexOf(pathName);
	    //获取主机地址
	    var localhostPath = curPath.substring(0,pos);
	    //获取带"/"的项目名
	    var projectName = pathName.substring(0,pathName.substr(1).indexOf('/')+1);
	    
	    return (localhostPath + projectName);
	},
	
	/*
	cookie 操作
	*/
	Cookie:{
		/*
		获取cookie
		name cookie名称
		*/
		get:function(name){
			if(document.cookie.length>0)
			{
				cStart=document.cookie.indexOf(name+"=");
				if(cStart!=-1)
				{ 
					cStart=cStart+name.length+1; 
			  		cEnd=document.cookie.indexOf(";",cStart);
			  		if(cEnd==-1) 
			  			cEnd=document.cookie.length;
			  	
			  		return unescape(document.cookie.substring(cStart,cEnd))
			  	} 
			}
			 
			return ""
		},
		/*
		设置cookie
		*/
		put:function(options){
			var defaults={
				//cookie 名称 
				name:"",
				//值
				value:"",
				//保存天数
				expireDays:14
			}
			var settings=$.extend(defaults,options);
		
			var exdate=new Date();
			exdate.setTime(exdate.getTime()+ (settings.expireDays*1000*60*60*24) );
			document.cookie=settings.name+"="+escape(settings.value)+
				((settings.expireDays==null)?"":";expires="+exdate.toGMTString()) + ";path=/";
		},
		/*
		是否存在 name名称的cookie
		*/
		contains:function(name){
			return this.get(name)!=null&&this.get(name)!="";
		},
		
		/**
		 * 删除cookie
		 */
		del:function(name) {
			var exp = new Date();
			exp.setTime(exp.getTime() - 1);
			document.cookie = name + "="+escape(Common.Cookie.get(name)) +";expires=" + exp.toGMTString()+ ";path=/";
		}
	},
	
	//时间转换
	switchTime:function(time){
		if (!time)
			return "";
		var timeAry = time.split(":");
		var timeM = Math.round(timeAry[1]*100/60);
		if(timeM < 10) {
			timeM = "0" + timeM;
		}
		return timeAry[0] + "." + timeM;
	}
};

