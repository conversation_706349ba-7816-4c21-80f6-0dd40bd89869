var buildChart = {
	
	oType: 1,
	
	hasLight : '0',//企业是否有光伏
	
    //生成图表1
	getChart1Data:function(){
		$.ajax({
			type: "POST",
			url: contextPath+"/index/getChart1Data.htm",
			success: function(msg){
                $( "#co2" ).html(msg.co2);
                $( "#standard" ).html(msg.standard);
                var total = msg.standard + msg.co2;
                var co2 = msg.co2 * 100/total;
                var standard = msg.standard * 100/total;
                $( "#progressbar1 .ui-progressbar-value" ).css("width",co2 + "%");
                $( "#progressbar2 .ui-progressbar-value" ).css("width",standard + "%");
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){} 
		});
	},
    
    //生成图表2
	getChart2Data:function(){
		$.ajax({
			type: "POST",
			url: contextPath+"/index/getChart2Data.htm",
			success: function(msg){
				if(msg.length == 0){
					showNoDataTip(chart2);
					return;
				}
				var total = 0;
				var ele1 = 0;
				var ele2 = 0;
				var ele3 = 0;
				var ele4 = 0;
				$(msg).each(function(index,value){
					var ele = this.Q == undefined?0:this.Q;
					total = Math.round((total + ele)*100)/100;
					if(this.RATENO == 1){
						ele1 = Math.round((ele1 + Number(ele))*100)/100;
					}else if(this.RATENO == 2){
						ele2 = Math.round((ele2 + Number(ele))*100)/100;
					}else if(this.RATENO == 3){
						ele3 = Math.round((ele3 + Number(ele))*100)/100;
					}else if(this.RATENO == 4){
						ele4 = Math.round((ele4 + Number(ele))*100)/100;
					}
				});
				
				var unit = "kWh";
				if(total > 10000){//单位转换kWh->MWh
					unit = "MWh";
					$("#eleUnit").html(unit);
					total = (total/1000).toFixed(2);
					ele1 = (ele1/1000).toFixed(2);
					ele2 = (ele2/1000).toFixed(2);
					ele3 = (ele3/1000).toFixed(2);
					ele4 = (ele4/1000).toFixed(2);
				}
				option2.series[0].data.push({value:ele1,name:'尖'});
				option2.series[0].data.push({value:ele2,name:'峰'});
				option2.series[0].data.push({value:ele3,name:'平'});
				option2.series[0].data.push({value:ele4,name:'谷'});
				
				$("#ele").html(total);
				$("#tickEle").html("耗电量:"+total + "" +unit);
				Common.Cookie.put({name:'ele',value:"耗电量:"+total + "" +unit});
				chart2.setOption(option2,true);	
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){
				showNoDataTip(chart2);
			} 
		});
	},
    
	//生成图表2、1
	getChart2DataNew:function(){
		$.ajax({
			type: "POST",
			url: contextPath+"/index/getChart2DataNew.htm",
			success: function(msg){
                //图表2
				var list = msg.list;
                var co2 = msg.co2;
                var standard = msg.standard;
				$(list).each(function(index,value){
                    option2New.series[0].data.push({name:this.name, value:this.value});
				});
                chart2.setOption(option2New,true);
                $("#total_co2").html(co2);
                $("#total_standard").html(standard);
                //图表1
                var chart1Y = [];
                var chart1Series = [];
                for(var i = list.length - 1; i >=0; i--){
                    chart1Y.push("");
                    var color = "#f7ae10";
                    if(list[i].name == "电"){
                        color = "#8dc913";
                    }
                    else if(list[i].name == "水"){
                        color = "#10a6e7";
                        $("#chart1_water").html(list[i].value);
                    }
                    else if(list[i].name == "气"){
                        color = "#02d0ee";
                        $("#chart1_gas").html(list[i].value);
                    }
                    else if(list[i].name == "热"){
                        color = "#f7ae10";
                        $("#chart1_hot").html(list[i].value);
                    }
                    var serie = {
                        itemStyle: {
                            normal: {
                                color: color
                            }
                        },
                        value: list[i].value
                    };
                    chart1Series.push(serie);
                }
                option1New.yAxis[0].data = chart1Y;
                option1New.series[0].data = chart1Series;
                chart1.setOption(option1New,true);

                var total_ele = msg.total_ele;
                var total_eleUnit = "kWh";
                if(total_ele > 10000){
                    total_eleUnit = "MWh";
                    total_ele = total_ele/1000;
                }
                $("#chart1_ele").html(total_ele.toFixed(1));
                $("#chart1_eleUnit").html(total_eleUnit);
                var total_water = msg.total_water;
                var total_waterUnit = "m³";
                if(total_water > 10000){
                    total_waterUnit = "km³";
                    total_water = total_water/1000;
                }
                $("#chart1_water").html(total_water.toFixed(1));
                $("#chart1_waterUnit").html(total_waterUnit);
                var total_gas = msg.total_gas;
                var total_gasUnit = "m³";
                if(total_gas > 10000){
                    total_gasUnit = "km³";
                    total_gas = total_gas/1000;
                }
                $("#chart1_gas").html(total_gas.toFixed(1));
                $("#chart1_gasUnit").html(total_gasUnit);
                var total_hot = msg.total_hot;
                var total_hotUnit = "kWh";
                if(total_hot > 10000){
                    total_hotUnit = "MWh";
                    total_hot = total_hot/1000;
                }
                $("#chart1_hot").html(total_hot.toFixed(1));
                $("#chart1_hotUnit").html(total_hotUnit);

                //滚动字幕--耗电量、CO2、标准煤
                var total_ele = msg.total_ele;
                var unit = "kWh";
				if(total_ele > 10000){
					unit = "MWh";
                    total_ele = (total_ele/1000).toFixed(2);
				}
				$("#tickEle").html("耗电量:"+total_ele + "" +unit);
				Common.Cookie.put({name:'ele',value:"耗电量:"+total_ele + "" +unit});
                $("#tickCO2").html("CO2排放:" + msg.co2+"吨");
                Common.Cookie.put({name:'co2',value:"CO2排放:" + msg.co2+"吨"});
                $("#tickStandard").html("标准煤:"+msg.standard+"吨");
                Common.Cookie.put({name:'standard',value:"标准煤:"+msg.standard+"吨"});
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){
				showNoDataTip(chart2);
			} 
		});
	},
    //生成图表3
	getChart3Data:function(){
		var analyType = $("#analyType").val();
		if(analyType == 102){//只计算企业的数据
			$.ajax({
				type: "POST",
				url: contextPath+"/index/getChart3Data.htm",
				success: function(msg){
					var total = msg.preMonFee + msg.currMonFee;
					var preMonFee = msg.preMonFee * 100/total; 
					var currMonFee = msg.currMonFee * 100/total; 
                    $( "#progressbar3 .ui-progressbar-value" ).css("width",preMonFee + "%");
                    $( "#progressbar4 .ui-progressbar-value" ).css("width",currMonFee + "%");
					var preFee = msg.preMonFee;
					if(msg.preMonFee > 100000){
						$("#preUnit").html("万元");
						preFee = (msg.preMonFee/10000).toFixed(2);
					}
					$("#preMonFee").html(preFee);
					
					var currFee = msg.currMonFee;
					if(msg.currMonFee>100000){
						$("#currUnit").html("万元");
						currFee = (msg.currMonFee/10000).toFixed(2);
					}
					$("#currMonFee").html(currFee);
				},
				error:function(XMLHttpRequest, textStatus, errorThrown){} 
			});
		}else{//赋默认值
			$("#preMonFee").html(0);
			$("#currMonFee").html(0);
			$( "#progressbar3" ).progressbar({
				value: 0
		    });
			$( "#progressbar4" ).progressbar({
				value: 0
		    });
		}
	},
	//生成图表3
	getChart3DataNew:function(){
		var analyType = $("#analyType").val();
		if(analyType == 102){//只计算企业的数据
			$.ajax({
				type: "POST",
				url: contextPath+"/index/getChart3DataNew.htm",
				success: function(msg){
                    //图表3
                    option3.series[0].data.push(msg.ele_1);
                    option3.series[0].data.push(msg.water_1);
                    option3.series[0].data.push(msg.gas_1);
                    option3.series[0].data.push(msg.hot_1);
                    option3.series[1].data.push(msg.ele_2);
                    option3.series[1].data.push(msg.water_2);
                    option3.series[1].data.push(msg.gas_2);
                    option3.series[1].data.push(msg.hot_2);
                    chart3.setOption(option3,true);
                    //本月 总能耗成本
                    if(Math.abs(msg.thisMonFee) > 100000){
                        $("#energy_cost").html((msg.thisMonFee/10000).toFixed(2));
                        $("#energy_cost_unit").html("万元");
                    }
                    else{
                        $("#energy_cost").html((msg.thisMonFee).toFixed(2));
                    }
				},
				error:function(XMLHttpRequest, textStatus, errorThrown){
                    showNoDataTip(chart3);
                }
			});
		}else if(analyType == 105){
            $.ajax({
				type: "POST",
				url: contextPath+"/index/getChart3DataPartner.htm",
				success: function(msg){
                    if(msg.companyCount){
                        $("#companyCount").html(msg.companyCount);
                    }
                    if(msg.pointCount){
                        $("#pointCount").html(msg.pointCount);
                    }
                    if(msg.provinceCount){
                        $("#provinceCount").html(msg.provinceCount);
                    }
                    if(msg.cityCount){
                        $("#cityCount").html(msg.cityCount);
                    }
                    if(msg.partnerCount){
                        $("#partnerCount").html(msg.partnerCount);
                    }
                },
				error:function(XMLHttpRequest, textStatus, errorThrown){
                }
            });
        }else{
		    //赋默认值
            option3.series[0].data = [0, 0, 0, 0];
            option3.series[1].data = [0, 0, 0, 0];
            chart3.setOption(option3,true);
		}
	},
	//生成图表4
	getChart4Data:function(){
		$.ajax({
			type: "POST",
			url: contextPath+"/index/getChart4Data.htm",
			success: function(msg){
				if(msg.currMonPwr.length == 0 && msg.lastMonPwr.length == 0){
					showNoDataTip(chart4);
					return;
				}
				var currArray = [];
				var lastArray = [];
				for(var i = 0;i<31;i++){
					currArray[i] = '-';
					lastArray[i] = '-';
				}
				var currMax=0;
				var lastMax=0;
				var max = 0;
				$.each(msg.currMonPwr,function(key,value){
					if(currMax < this.AP){
						currMax = this.AP;
					}
					if(max<this.AP){
						max = this.AP;
					}
					var index = Number(this.DAY)-1;
					currArray[index] = this.AP;
				})
				
				$.each(msg.lastMonPwr,function(key,value){
					if(max<this.AP){
						max = this.AP;
					}
					if(lastMax < this.AP){
						lastMax = this.AP;
					}
					var index = Number(this.DAY)-1;
					lastArray[index] = this.AP;
				})
				var lastDate = msg.lastMonPwrDate;
				var currDate = msg.currMonPwrDate;
				option4.legend.formatter = function(v){
					if(v =='上月最大需量'){
						if(lastDate!= null){
							return '上月:'+lastMax+' kW('+lastDate.substr(8,2)+'日'+lastDate.substr(10,9)+')';
						}
						return v;
					}else {
						if(currDate!=null){
							return '本月:'+currMax+' kW('+currDate.substr(8,2)+'日'+currDate.substr(10,9)+')';
						}
						return v;
					}
				}
				option4.series[0].data = lastArray;
				option4.series[1].data = currArray;
				if(max > 0){
					option4.grid.x = (Math.round(max)+"").length * 10;//根据最大值的长度来设置x的距离
				}
				chart4.setOption(option4,true);	
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){} 
		});
	},
	//生成图表5---7天电量
	getChart5Data:function(){
		$.ajax({
			type: "POST",
			url: contextPath+"/index/getChart5Data.htm",
			success: function(msg){
				if(msg.list.length == 0){
					showNoDataTip(chart5);
					return;
				}
				var max = 0;
				if(msg.max != null && msg.max != 0){
					max = msg.max;
				}
				
				var dataAry = [];
				var data1 = [];
				var data2 = [];
				var data3 = [];
				var data4 = [];
				var dateAry = [];
				$(msg.list).each(function(){
					var rate = this.RATE_NUMBER;
					var value = this.ELE_VALUE;
					if(max > 10000){
						value = (this.ELE_VALUE/1000).toFixed(2);
					}
					if(rate == 1){
						dateAry.push(this.TIME_FIELD.substr(5,5));
						data1.push(value);
					}else if (rate == 2){
						data2.push(value);
					}else if (rate == 3){
						data3.push(value);
					}else if (rate == 4){
						data4.push(value);
					}
				});
				if(max > 10000){
					option5.yAxis[0].name="MWh";
					max = max/1000;
				}
				dataAry.push(data1);
				dataAry.push(data2);
				dataAry.push(data3);
				dataAry.push(data4);
				var seriesArray = [];
				$(dataAry).each(function(index){
					var name = "";
					if(index == 0){
						name = "尖";
					}else if(index == 1){
						name = "峰";
					}else if(index == 2){
						name = "平";
					}else if(index == 3){
						name = "谷";
					}
					
					seriesArray.push({
			            name:name,
			            type:'bar',
			            barCategoryGap:'38%',//类目间柱形距离
			            symbol:'none',
			        	smooth:true,
			        	tooltip : {trigger: 'item',formatter: "{a} <br/>{b}<br/>{c}"},
		           		stack: 'aa',
		           		itemStyle: {
						    normal: {
						        label: { show: false},
						        labelLine: { show: false}
						    },
						    emphasis: {
						        label: {show: false},
						        labelLine: {show: false}
						    },
						},
			            data:this
					});
				});
				
				option5.xAxis[0].data = dateAry;
				option5.series = seriesArray;
				chart5.setOption(option5,true);	
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){} 
		});
	},
	//生成图表6---实时功率
	getChart6Data:function(){
		$.ajax({
			type: "POST",
			url: contextPath+"/index/getChart6Data.htm",
			success: function(msg){
				if(msg.length ==0) {
					showNoDataTip(chart6);
					return;
				}
				var dataAry = [];
				var dateAry = [];
				$(msg).each(function(){
					var value = this.AP;
					dataAry.push(value);
					dateAry.push(this.FREEZETIME.substr(11,5));
				});
				option6.xAxis[0].data=dateAry;
				option6.series[0].data=dataAry;
				chart6.setOption(option6,true);
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){} 
		});
	},
	//生成图表7-30天电量
	getChart7Data:function(){
		$.ajax({
			type: "POST",
			url: contextPath+"/index/getChart7DataNew.htm",
			success: function(msg){
				if(msg.list1.length == 0){
					showNoDataTip(chart7);
					return;
				}
                var dateAry = [];
				var dataAry_1 = [];
				var dataAry_2 = [];
				var max = 0;
				if(msg.max != null && msg.max != 0){
					max = msg.max;
				}
				$(msg.list1).each(function(){
					var value = this.ELE_VALUE;
					if(max > 10000){
						value = (this.ELE_VALUE/1000).toFixed(2);
					}
                    dataAry_1.push(value);
					dateAry.push(this.TIME_FIELD.substr(5,5));
				});
                $(msg.list2).each(function(){
                    var value = this.ELE_VALUE;
                    if(max > 10000){
                        value = (this.ELE_VALUE/1000).toFixed(2);
                    }
                    dataAry_2.push(value);
                });
				if(max > 10000){
					option7.yAxis[0].name="MWh";
					max = max/1000;
				}
				option7.yAxis[0].max=max*1.1;
				option7.xAxis[0].data=dateAry;
				option7.series[0].data=dataAry_1;
				option7.series[1].data=dataAry_2;
				chart7.setOption(option7,true);
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){} 
		});
	},
	//生成图表8
	getChart8Data:function(){
		$.ajax({
			type: "POST",
			url: contextPath+"/index/getChart8Data.htm",
			success: function(msg){
				if(msg.maxI==null){
					showNoDataTip(chart8);
					return;
				}
				var occuredTime = msg.occuredTime==null?"":"("+msg.occuredTime+")";
				option8.title.text=occuredTime;
				var maxPwr = msg.maxPwr==null?0:msg.maxPwr;
				option8.series[0].data = [{value:maxPwr,name:'最大负荷'}];
				var ratio = msg.ratio==null?maxPwr:msg.ratio;
				if(ratio == 0){
					ratio = 100;
				}
				option8.series[0].max = ratio * 1.1;//额定功率
				chart8.setOption(option8,true);	
			},
			error:function(XMLHttpRequest, textStatus, errorThrown){} 
		});
	},
	
	/* 收集查询参数 */
	collectParam: function (curveType) {
		var param = {
			curveType: curveType,
			isLedger: false,
			objectType : buildChart.oType,
		}
		return param;
	},
	
	//生成电工首页的图表
	/* 无数据提示 */
	noDataHtml: "<tr><td colspan=\"" + $("#tHead th").length + "\" style=\"text-align:center\">" + noDataTip + "</td></tr>",
	
	/* 查询电量图表数据 */
	getMyChartData1: function(params){
		
		
		myChart1.showLoading({text: loadingTip});
		$.ajax({
			type: "post",
			url:  contextPath + "/index/getMyChartData.htm",
			data: "paramInfo=" + JSON.stringify(params), 
			success: function(msg){
				// 隐藏加载话术	
				myChart1.hideLoading();
				myChart1.clear();
				if (msg.hasData) {
					//buildChart.hasData = true;
					var curveType = Number(params.curveType);//曲线类型
					var resultMap = buildChart.buildOption(msg,curveType);
					option.xAxis[0].data = resultMap.xAxisData;
					option.yAxis[0].name = resultMap.yName;
					option.legend.data=resultMap.legendArray;
					option.series = resultMap.seriesData;
					if (curveType == 5) {
						option.yAxis[0].scale=false;
						option.yAxis[0].min=resultMap.minPF;
						option.yAxis[0].max=1;
						option.yAxis[0].precision=2;
					} else {
						option.yAxis[0].scale=true;
						option.yAxis[0].max=undefined;
						option.yAxis[0].min=undefined;
						option.yAxis[0].precision=0;
					}
					
					
					myChart1.setOption(option, true);
				}else {
					showNoDataTip(myChart1);
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				myChart1.hideLoading();
				alertTip({message: failedTip});
			}
		});
	},
	
	/* 查询有功功率图表数据 */
	getMyChartData2: function(params){
		
		
		myChart2.showLoading({text: loadingTip});
		$.ajax({
			type: "post",
			url:  contextPath + "/index/getMyChartData.htm",
			data: "paramInfo=" + JSON.stringify(params), 
			success: function(msg){
				// 隐藏加载话术	
				myChart2.hideLoading();
				myChart2.clear();
				if (msg.hasData) {
					//buildChart.hasData = true;
					var curveType = Number(params.curveType);//曲线类型
					var resultMap = buildChart.buildOption(msg,curveType);
					option.xAxis[0].data = resultMap.xAxisData;
					option.yAxis[0].name = resultMap.yName;
					option.legend.data=resultMap.legendArray;
					option.series = resultMap.seriesData;
					if (curveType == 5) {
						option.yAxis[0].scale=false;
						option.yAxis[0].min=resultMap.minPF;
						option.yAxis[0].max=1;
						option.yAxis[0].precision=2;
					} else {
						option.yAxis[0].scale=true;
						option.yAxis[0].max=undefined;
						option.yAxis[0].min=undefined;
						option.yAxis[0].precision=0;
					}
					
					
					myChart2.setOption(option, true);
				}else {
					showNoDataTip(myChart2);
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				myChart2.hideLoading();
				alertTip({message: failedTip});
			}
		});
	},
	
	/* 查询无功功率图表数据 */
	getMyChartData3: function(params){
		
		
		myChart3.showLoading({text: loadingTip});
		$.ajax({
			type: "post",
			url:  contextPath + "/index/getMyChartData.htm",
			data: "paramInfo=" + JSON.stringify(params), 
			success: function(msg){
				// 隐藏加载话术	
				myChart3.hideLoading();
				myChart3.clear();
				if (msg.hasData) {
                    buildChart.hasLight = msg.hasLight; //是否是光伏企业，“无功功率”换成“光伏”
					var curveType = Number(params.curveType);//曲线类型
					var resultMap = buildChart.buildOption(msg,curveType);
					option.xAxis[0].data = resultMap.xAxisData;
					option.yAxis[0].name = resultMap.yName;
					option.legend.data=resultMap.legendArray;
					option.series = resultMap.seriesData;
					if (curveType == 5) {
						option.yAxis[0].scale=false;
						option.yAxis[0].min=resultMap.minPF;
						option.yAxis[0].max=1;
						option.yAxis[0].precision=2;
					} else {
                        option.yAxis[0].scale=true;
                        option.yAxis[0].max=undefined;
                        option.yAxis[0].min=undefined;
                        option.yAxis[0].precision=0;
					}
					
					myChart3.setOption(option, true);
				}else {
					showNoDataTip(myChart3);
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				myChart3.hideLoading();
				alertTip({message: failedTip});
			}
		});
	},
	
	/* 查询功率因数图表数据 */
	getMyChartData4: function(params){
		
		
		myChart4.showLoading({text: loadingTip});
		$.ajax({
			type: "post",
			url:  contextPath + "/index/getMyChartData.htm",
			data: "paramInfo=" + JSON.stringify(params), 
			success: function(msg){
				// 隐藏加载话术	
				myChart4.hideLoading();
				myChart4.clear();
				if (msg.hasData) {
					//buildChart.hasData = true;
					var curveType = Number(params.curveType);//曲线类型
					var resultMap = buildChart.buildOption(msg,curveType);
					option.xAxis[0].data = resultMap.xAxisData;
					option.yAxis[0].name = resultMap.yName;
					option.legend.data=resultMap.legendArray;
					option.series = resultMap.seriesData;
					if (curveType == 5) {
						option.yAxis[0].scale=false;
						option.yAxis[0].min=resultMap.minPF;
						option.yAxis[0].max=1;
						option.yAxis[0].precision=2;
					} else {
						option.yAxis[0].scale=true;
						option.yAxis[0].max=undefined;
						option.yAxis[0].min=undefined;
						option.yAxis[0].precision=0;
					}
					
					
					myChart4.setOption(option, true);
				}else {
					showNoDataTip(myChart4);
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				myChart4.hideLoading();
				alertTip({message: failedTip});
			}
		});
	},
	
	/**
	 * 生成OPTION
	 */
	buildOption: function(msg,curveType){
		var resultMap = {};
		var legendArray=[];
		var xAxisData = [];	 //冻结时间数组
		var seriesDataA = [];//数据数组---曲线A
		var seriesDataB = [];//数据数组---曲线B
		var seriesDataC = [];//数据数组---曲线C
		var seriesDataD = [];//数据数组---曲线D
		var seriesData = [];
		var upData = [];		//电压上限
		var downData = [];		//电压下限
		var nameTmp = "";	//数据类型名称
		var html = "";
		var hasData = false;
		var timetmp = "";
		var hourtmp = 0;
		var minPF = 1;//最小功率因数
		
		// 曲线数据
		if (! $.isEmptyObject(msg.chartData)) {
			hasData = true;
			$.each(msg.chartData, function(key, value) {
				timetmp = this.freezeTime.substring(5,16);
				if (curveType == 6) {//电量
					hourtmp = parseInt(timetmp.substring(6,8),10)+1;
					xAxisData.push(timetmp.substring(0,6)+hourtmp+":00");
				} else{
					xAxisData.push(timetmp);
				}
					
				if (curveType == 1) {//电压
					//3项4线，要乘以根号3；3项3线，不能乘以根号3;
					//1,三相三线;2,三相四线;3:单相表
					var commMode = msg.commMode;
					seriesDataA.push(VoltageCurrent.volData(this.a,commMode));
					seriesDataB.push(VoltageCurrent.volData(this.b,commMode));
					seriesDataC.push(VoltageCurrent.volData(this.c,commMode));
				} else if (curveType == 5) {//功率因数
					seriesDataD.push(this.d==null?"":(this.d).toFixed(3) );
					if (minPF>this.d)
						minPF=this.d;
				} else {
					seriesDataA.push(this.a==null?"":this.a);
					seriesDataB.push(this.b==null?"":this.b);
					seriesDataC.push(this.c==null?"":this.c);
					seriesDataD.push(this.d==null?"":this.d);
				}
			});

			// 时间轴数据
			resultMap.xAxisData = xAxisData;
            if(curveType == 1){
                nameTmp = "电压";
                resultMap.yName = "V";
            }
            else if(curveType == 2){
                nameTmp = "电流";
                resultMap.yName = "A";
            }
            else if(curveType == 3){
                nameTmp = "有功功率";
                resultMap.yName = "kW";
            }
            else if(curveType == 4){
                nameTmp = "无功功率";
                resultMap.yName = "kVar";
                if(buildChart.hasLight == '1'){
                    nameTmp = "光伏功率曲线";
                    resultMap.yName = "kW";
                }
            }
            else if(curveType == 5){
                nameTmp = "功率因数";
                resultMap.yName = "";
            }
            else if(curveType == 6){
                nameTmp = "曲线电量";
                resultMap.yName = "kWh";
            }

			if (curveType == 1 || curveType == 2 || curveType == 6) {
				seriesData.push({
					name: curveType == 6 ? '正向有功总电能量' : 'A相' + nameTmp,
		            type: 'line',
		            symbol:'none',
	    			smooth:true,
	    			itemStyle:{
	    				normal:{
	    					lineStyle:{
	    						width:2
	    					}
	    				}
	    			},
		            data: seriesDataA
				});
				legendArray.push(curveType == 6 ? '正向有功总电能量' : 'A相' + nameTmp);
				seriesData.push({
					name: curveType == 6 ? '正向无功总电能量' : 'B相' + nameTmp,
		            type: 'line',
		            symbol:'none',
	    			smooth:true,
	    			itemStyle:{
	    				normal:{
	    					lineStyle:{
	    						width:2
	    					}
	    				}
	    			},
		            data: seriesDataB
				});
				legendArray.push(curveType == 6 ? '正向无功总电能量' : 'B相' + nameTmp);
				if(curveType != 6) {
					seriesData.push({
						name: 'C相' + nameTmp,
			            type: 'line',
			            symbol:'none',
	        			smooth:true,
	        			itemStyle:{
	        				normal:{
	        					lineStyle:{
	        						width:2
	        					}
	        				}
	        			},
			            data: seriesDataC
					});
					legendArray.push('C相' + nameTmp);
				}
			}
			if (curveType == 3 || curveType == 4 || curveType == 5) {
				var titleName = "";
                if(curveType == 3){
                    titleName = "有功功率曲线";
                }
                else if(curveType == 4){
                    titleName = "无功功率曲线";
                    if(buildChart.hasLight == '1'){
                        titleName = "光伏功率曲线";
                        $(".top_main_bg .chart_ul .list_icon3").text("光伏功率");
                    }
                }
                else if(curveType == 5){
                    titleName = "总功率因数";
                }
				seriesData.push({
					name: titleName,
		            type: 'line',
		            symbol:'none',
					smooth:true,
					itemStyle:{
						normal:{
							lineStyle:{
								width:2
							}
						}
					},
		            data: seriesDataD
				});
				legendArray.push(titleName);
			}
		}
		
		
		// 直线数据(排除无功功率和功率因数)
		if (! $.isEmptyObject(msg.lineData) && xAxisData.length > 0) {
			if (msg.lineData.VOLT_VALUE != "" ) {
				// 电压多出电压下限直线
				if (curveType == 1) {
					for (var i=0; i<xAxisData.length; i++) {
						downData.push(msg.lineData.VOLT_VALUE);
					}
					seriesData.push({
						name: '电压下限',
			            type: 'line',
			            symbol:'none',
	        			smooth:true,
	        			itemStyle:{
	        				normal:{
	        					lineStyle:{
	        						width:2
	        					}
	        				}
	        			},
			            data: downData
					});
					legendArray.push('电压下限');
				}
			}
			if (msg.lineData.THRESHOLD_VALUE != "") {
				// 电压、电流等额定直线
				for (var i=0; i<xAxisData.length; i++) {
					upData.push(msg.lineData.THRESHOLD_VALUE);
				}
				switch (curveType) {
					case 1:
						nameTmp = "电压上限";
						break;
					case 2:
						nameTmp = "额定电流";
						break;
					case 3:
						nameTmp = "额定功率";
						break;
					case 5:
						nameTmp = "标准功率因数";
						if (minPF>msg.lineData.THRESHOLD_VALUE){
							minPF=msg.lineData.THRESHOLD_VALUE;
						}
						break;
				}
				seriesData.push({
					name: nameTmp,
		            type: 'line',
		            symbol:'none',
	        		smooth:true,
	        		itemStyle:{
	    				normal:{
	    					lineStyle:{
	    						width:2
	    					}
	    				}
	    			},
		            data: upData
				});
				legendArray.push(nameTmp);
			}
		}
		
		// 如果有数据
		if (hasData) {
			resultMap.legendArray=legendArray;
			resultMap.seriesData = seriesData;
			resultMap.minPF=minPF;
		}
		return resultMap;
	}
}