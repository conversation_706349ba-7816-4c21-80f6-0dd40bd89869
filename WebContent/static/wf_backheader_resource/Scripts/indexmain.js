/*
 @desc
 */

function chgIndexpage(){
    window.location.href = contextPath+"/index/showIndexPage.htm";
}


function chgMainpage(url,position){
    var src = contextPath + url;
    var arr = position.split("_");
    var html="<a href=\"#\" >"+arr[0]+"></a><i></i><a href=\"#\" class=\"on_page\">"+arr[1]+"</a>";
    $("#position").html(html);
    $("#main_frame").attr("src",src);
}

function chgTabpage(moduleId,position){
    var src= contextPath+"/frameController/showSearchTabPage.htm?parentModuleId="+moduleId;
    var html="<a href=\"#\" >"+position+"</a>";
    $("#position").html(html);
    $("#main_frame").attr("src",src);
}

function chgMainpage2(url,position){
    var src = contextPath + url;
    var srcInfo={
        src:src,
        position:encodeURIComponent(position)
    }
    window.location.href = contextPath+"/frameController/showMainPage.htm?srcInfo="+JSON.stringify(srcInfo);
}

function chgTabpage2(moduleId,position){
    var src= contextPath+"/frameController/showSearchTabPage.htm?parentModuleId="+moduleId;
    var srcInfo={
        src:src,
        position:encodeURIComponent(position)
    }
    window.location.href = contextPath+"/frameController/showMainPage.htm?srcInfo="+JSON.stringify(srcInfo);
}

function chgTabpage3(moduleId,position){
    var src= contextPath+"/frameController/showSearchTabPage.htm?parentModuleId="+moduleId;
    var html="<a href=\"#\" >"+position+"</a>";
    $("#position").html(html);
    $("#main_frame").attr("src",src);
}


/**
 * 间隔时间
 */
var warningInterval = 1;//告警事件间隔时间(小时)
var setWarning = false;//是否初始化告警信息
var warningNum = 5;//允许存放的告警事件记录数
//var event; //告警事件定时任务
var audioInterval;//声音告警定时任务
/**
 * 事件告警
 */
function eventWarning(){
    $.ajax({
        type:'post',
        url:contextPath + '/eventquery/gotoEventWarning.htm',
        success:function(msg){
            if(!setWarning){//设置告警间隔时间和告警数目
                warningNum = msg.warningNum;
                var event = self.setInterval('eventWarning()',1000*60*60*warningInterval);//告警事件定时任务
                setWarning = true;
            }
            if(msg.list != null && msg.list.length > 0){
                var showPop = false;
                var index = 0;//告警事件序列
                $.each(msg.list,function(i,item){
                    //用Cookie里的存值判断是否已阅
                    var cookieName = "recId_" + item.eventRecId;
                    var cookieValue = Common.Cookie.get(cookieName);
                    if(cookieValue == undefined || cookieValue == ""){
                        showPop = true;
                        if(index < warningNum){
                            $("#"+index+"_content").attr("cookieName",cookieName);
                            $("#"+index+"_content").html(item.eventStartTimeStr + " " + item.eventContent);
                            index++;
                        }
                    }
                });

                if(showPop){
                    //底部小窗口
                    $(".pop-up").show();
                    audioInterval = setInterval("$('#pop_audio')[0].play()",1000);
                    $(".pop-up").animate({height:"300px"},"slow",function(){ $(".pop-up .close_win").show();});
                }
            }
        },
        error:function(){}
    });
}

/////////////////////////////////////////////////////////////////////////////////////////////// 闭包 begin
$(document).ready(function(){
    //事件告警初始化检查
    eventWarning();

    //关闭告警事件
    $(".pop-up .close_win").click(function(){
    	clearInterval(audioInterval);
        $("#event_content div").each(function(){
            var cookieName = $(this).attr("cookieName");
            if(cookieName != undefined && cookieName != ""){
                //用Cookie将该告警事件设置为已阅
                var options = {
                    name:cookieName,
                    value:cookieName,
                    expireDays:1/24
                }
                Common.Cookie.put(options);

                $(this).empty();
                $(this).attr("cookieName","");
            }
        });
        $(".pop-up .close_win").hide(0);
        $(".pop-up").animate({height:0},"slow",function(){$(".pop-up").hide();});
    });
    
    //定时关闭事件告警
    setTimeout("$('.pop-up .close_win').trigger('click')", 60000);
    
    //等级帮助弹出页
    $("img.level_bg").click(function(){
        $.ajax({
            type: "POST",
            url:contextPath + "/index/levelPage.htm",
            success: function(result){
                $("#levelHelp").html(result);
            }
        });
        $("#levelHelp").dialog("option","title","等级帮助");
        $('#levelHelp').dialog({
            close: function() {}
        });
        $("#levelHelp").dialog("open");
    });
    $("#levelHelp").dialog({
        position: ['center','center'],
        width:780,
        height:590,
        modal:true,
        autoOpen:false,
        title:"",
        resizable:false
    });

    /*弹出窗口控件初始化*/
    $("#confirmD").add("#alertD").dialog({
        width:280,
        height:145,
        modal:false,
        autoOpen:false,
        title:'',
        resizable:false
    });

    //修改密码
    $("#modifyPassword").click(function(){
        $('#passwordForm input').each(function(){
            $(this).val("");
            $('#passwordForm').validator('hideMsg', "#"+$(this).attr("id"));
        });
        $(".modifyPassword").dialog("open");
        $(this).addClass("pass_on");
    });

    //大屏点击事件
    $("#fullScreen").click(function(){
        $.ajax({
            type:"post",
            url:contextPath+"/frameController/checkLinyang.htm",
            success:function(msg){
                if(msg){
                    var url = contextPath+"/frameController/linyang.htm";
                    window.open(url,"大屏","height="+screen.height+",width="+screen.width+",top=0,left=0,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no");
                }else {
                    Common.Xtip({msg:"该用户没有设置大屏显示"});
                }
            }
        })
    });
    
    /**
     * 第三方网页链接
     */
    $("#thirdLink").click(function(){
    	$.ajax({
            type:"post",
            url:contextPath+"/frameController/checkThirdLink.htm",
            success:function(msg){
                if(msg.hasUrl){
                    window.open(msg.url,"第三方外链","height="+screen.height+",width="+screen.width+",top=0,left=0,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no");
                }else {
                	Common.Xtip({msg:"该用户没有设置第三方链接"});
                }
            }
        })
    });

    //联系我们弹出界面
    $("#contactBt").click(function(){
        $.ajax({
            type: "POST",
            url:contextPath + "/index/contactShow.htm",
            success: function(result){
                $("#contact").html(result);
            }
        });
        $("#contact").dialog("option","title","联系我们");
        $("#contact").dialog("open");
    });
    $("#contact").dialog({
        position: ['center','center'],
        width:616,
        height:511,
        modal:true,
        autoOpen:false,
        title:"",
        resizable:false
    });

    //退出登陆
    $("#logout").click(function(){
        $("#confirmD").dialog("option","title","");
        $("#confirmD").dialog("option","buttons",{
            '确定':function(){
                Common.Cookie.del("auto_login");
                $(this).dialog("close");
                window.location.href = contextPath+"/frameController/logout.htm";
                $("#logout").removeClass("exit_on");
            },
            '取消':function(){
                $(this).dialog("close");
                $("#logout").removeClass("exit_on");
            }
        });
        $("#confirmD").dialog("option","close",function(){
            $("#logout").removeClass("exit_on");
        });
        $("#confirmD .dialog-tip-text").text("确定退出系统吗?");
        $("#confirmD").dialog("open");
        $(this).addClass("exit_on");
    });

    //角色切换事件
    $(".global-top-right .role_change").hover(function(){
        $(".role_choose-list").show(100);
    });
    $(".global-top-right .role_change").mouseleave(function(){
        $(".role_choose-list").hide(100);
    });

});
/////////////////////////////////////////////////////////////////////////////////////////////// 闭包 end


//密码验证
$('#passwordForm').validator({
    theme: 'default',
    stopOnError: false,
    timely: 1,
    rules: {
        // 自定义验证函数
        isPasswordEq: function(element){
            var conPassword = $(element).val();
            if(conPassword == $("#newPassword").val())
                return true;
            else
                return false;
        },
        isFormatedPWD: function(element){
            var pwd= $(element).val();
            var objRegExp = /^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9]{6,}$/;
            if(objRegExp.test(pwd))
                return true;
            else
                return false;
        }
    },
    fields: {
        "password": {
            rule: "required; remote["+contextPath+"/frameController/isPasswordCorrect.htm]",  // 远程验证,多个参数之间逗号分隔且逗号后的空格是必须
            tip: needTip,
            msg: {required:needTip},
            dataFilter: function(msg){
                var result = {};
                if(msg.flag || msg.flag == "true"){
                    result.ok = "";
                }else if(!msg.flag || msg.flag == "false"){
                    result.error = errorTip;
                }
                return result;
            }
        },
        "newPassword": {
            rule: "required;length[6~];isFormatedPWD;",
            tip: passwordLength,
            msg: {
                required: notEmpty,
                length: passwordLength,
                isFormatedPWD:isFor
            }
        },
        "conPassword": {
            rule: "required;length[6~];isPasswordEq;",
            tip:passwordLength,
            msg: {
                required: notEmpty,
                length: passwordLength,
                isPasswordEq:isEq
            }
        }
    },
    // 提交表单之前的回调, 返回false表示取消本次提交
    beforeSubmit: function(form){
        return true;
    }
});

/* 检测表单是否所有字段都验证通过 */
function isValid(){
    var flag = false;
    $('#passwordForm').isValid(function(isValid){
        if(isValid)
            flag = true;
    });
    return flag;
}


//初始化修改密码弹出框
function InitPassword(){
    $(".modifyPassword").dialog({
        width:450,
        height:200,
        modal:false,
        autoOpen:false,
        title:modifyPassword,
        resizable:false,
        dialogClass: "show-dialog_close",
        close:function(){
            $("#modifyPassword").removeClass("pass_on");
        },
        buttons:{
            '确定':function(event,ui){
                $('#passwordForm').trigger("validate");
                if(isValid()){
                    $.ajax({
                        type: "POST",
                        url: contextPath+"/frameController/changePassword.htm",
                        data: "password="+$("#newPassword").val(),
                        async: false,
                        success: function(msg){
                            if(msg){
                                $("input[type='password']").each(function(){
                                    $(this).val("");
                                });
                                Common.Xtip({type:"ok",msg:successTip});
                            }else{
                                Common.Xtip({msg:failedTip});
                            }
                        }
                    });
                    $(this).dialog("close");
                }
                $("#modifyPassword").removeClass("pass_on");
            },
            '取消':function(event,ui){
                $(this).dialog("close");
                $("#modifyPassword").removeClass("pass_on");
            }
        }
    });
    //更新最后登陆时间
    UpdateLastDate();
}

//第一次登陆修改密码
function UpdateLastDate(){
    $.ajax({
        type: "POST",
        url: contextPath+"/frameController/updateLastDate.htm",
        //data: "",
        async: false,
        success: function(msg){
            //if(msg){
            //alert("更新成功");
            //}else{
            //alert("更新失败");
            //}
        }
    });
}

//同步roleType
function synchronizeRoleType(_this){
    $(".role_change .selectedRole").html(_this.innerHTML);
    roleType = _this.value;
    $.ajax({url:contextPath+"/frameController/checkRoleType.htm?roleType="+roleType,async: false,cache:false});
}

/**
 * 第三方平台：设置logo、颜色
 * @param {Object} colorRgb
 */
function fSetLogoColor(colorRgb){
    $(".bg-theme0,.header").css('background-color',"#"+colorRgb);
    // $(".menu").css('background-color',"#"+colorRgb);
}
function fSetLogoIcon(iconUrl){
    $(".header").css('background-image',"url("+iconUrl+")");
}