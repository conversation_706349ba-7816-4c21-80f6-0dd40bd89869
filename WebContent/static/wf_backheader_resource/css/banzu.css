/**
 * Created by <PERSON> on 2017/2/14.
 */
#left_i,#right_i {
    width: 21px;
    height: 30px;
    display: inline-block;
    text-align: center;
    line-height: 26px;
    border: 1px solid #cecece;
}
.input_date {
    height:32px;
}
#search_top {
    border-bottom: 1px solid #cecece;
    border-top: 1px solid #cecece;
}
.dian {
    float: left;
    width: 35px;
    height: 30px;
    border: 1px solid black;
    text-align: center;
    line-height: 30px;
}
#title {
    height: 50px;
    text-align: center;
    font-size: 20px;
    line-height: 50px;
    position: relative;
}
#title > span {
    font-size: 14px;
    margin-top: 11px;
    position: absolute;
    right: 0;
}
.rows {
    border-bottom: 1px solid #cecece;
    padding: 8px;
}
.cols {
    padding:0px !important;
}
table {
    text-align: center;
}
table th {
    text-align: center;
}
table td {
    display: table-cell;
    vertical-align: middle !important;
}
#foot-title {
    font-size: 20px;
    margin-top: 20px;
    display: inherit;
    text-align: center
}
#ech3 {
    height: 400px;
    padding: 0;
    border:1px solid #cecece;
}
.main3{
	width:100%;
	height:600px;
	overflow-Y:auto;
}
.bottom{
	width:100%;
	height:40px;
}
.bot{
	width:100%;
	height:100px;
}