<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>

	<title>FixedHeader examples - FixedHeader examples</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>FixedHeader example <span>FixedHeader examples</span></h1>

			<div class="info">
				<p>At times it can be useful to ensure that column titles will remain always visible on a table, even
				when a user scrolls down a table. The FixedHeader plug-in for DataTables will float the <code class=
				"tag" title="HTML tag">thead</code> element above the table at all times to help address this issue.
				The column titles also remain click-able to perform sorting. Key features include:</p>

				<ul class="markdown">
					<li>Fix the header to the top of the window</li>
					<li>Ability to fix the footer and left / right columns as well</li>
					<li>z-Index ordering options</li>
				</ul>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Examples</a></h3>
						<ul class="toc">
							<li><a href="./simple.html">Basic initialisation</a></li>
							<li><a href="./header_footer.html">Header and footer fixed</a></li>
							<li><a href="./top_left_right.html">Header, left and right all fixed</a></li>
							<li><a href="./two_tables.html">Multiple tables</a></li>
							<li><a href="./zIndexes.html">z-index order control</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full
					information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and
					<a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of
					DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href=
					"http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2014<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>