<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>AdminLTE 2 | Documentation</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <!-- Bootstrap 3.3.6 -->
    <link rel="stylesheet" href="../bootstrap/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.5.0/css/font-awesome.min.css">
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="../dist/css/AdminLTE.min.css">
    <link rel="stylesheet" href="../dist/css/skins/_all-skins.min.css">
    <link rel="stylesheet" href="style.css">

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
        <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
  </head>
  <body class="skin-blue fixed" data-spy="scroll" data-target="#scrollspy">
    <div class="wrapper">

      <header class="main-header">
        <!-- Logo -->
        <!-- Logo -->
        <a href="../index2.html" class="logo">
          <!-- mini logo for sidebar mini 50x50 pixels -->
          <span class="logo-mini"><b>A</b>LT</span>
          <!-- logo for regular state and mobile devices -->
          <span class="logo-lg"><b>Admin</b>LTE</span>
        </a>
        <!-- Header Navbar: style can be found in header.less -->
        <nav class="navbar navbar-static-top" role="navigation">
          <!-- Sidebar toggle button-->
          <a href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button">
            <span class="sr-only">Toggle navigation</span>
          </a>
          <!-- Navbar Right Menu -->
          <div class="navbar-custom-menu">
            <ul class="nav navbar-nav">
              <li><a href="http://almsaeedstudio.com">Almsaeed Studio</a></li>
              <li><a href="http://almsaeedstudio.com/premium">Premium Templates</a></li>
            </ul>
          </div>
        </nav>
      </header>
      <!-- Left side column. contains the logo and sidebar -->
      <aside class="main-sidebar">
        <!-- sidebar: style can be found in sidebar.less -->
        <div class="sidebar" id="scrollspy">

          <!-- sidebar menu: : style can be found in sidebar.less -->
          <ul class="nav sidebar-menu">
            <li class="header">TABLE OF CONTENTS</li>
            <li class="active"><a href="#introduction"><i class="fa fa-circle-o"></i> Introduction</a></li>
            <li><a href="#download"><i class="fa fa-circle-o"></i> Download</a></li>
            <li><a href="#dependencies"><i class="fa fa-circle-o"></i> Dependencies</a></li>
            <li><a href="#advice"><i class="fa fa-circle-o"></i> Advice</a></li>
            <li><a href="#layout"><i class="fa fa-circle-o"></i> Layout</a></li>
            <li><a href="#adminlte-options"><i class="fa fa-circle-o"></i> Javascript Options</a></li>
            <li class="treeview" id="scrollspy-components">
              <a href="javascript:void(0)"><i class="fa fa-circle-o"></i> Components</a>
              <ul class="nav treeview-menu">
                <li><a href="#component-main-header">Main Header</a></li>
                <li><a href="#component-sidebar">Sidebar</a></li>
                <li><a href="#component-control-sidebar">Control Sidebar</a></li>
                <li><a href="#component-info-box">Info Box</a></li>
                <li><a href="#component-box">Boxes</a></li>
                <li><a href="#component-direct-chat">Direct Chat</a></li>
              </ul>
            </li>
            <li><a href="#plugins"><i class="fa fa-circle-o"></i> Plugins</a></li>
            <li><a href="#browsers"><i class="fa fa-circle-o"></i> Browser Support</a></li>
            <li><a href="#upgrade"><i class="fa fa-circle-o"></i> Upgrade Guide</a></li>
            <li><a href="#implementations"><i class="fa fa-circle-o"></i> Implementations</a></li>
            <li><a href="#faq"><i class="fa fa-circle-o"></i> FAQ</a></li>
            <li><a href="#license"><i class="fa fa-circle-o"></i> License</a></li>
          </ul>
        </div>
        <!-- /.sidebar -->
      </aside>

      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <div class="content-header">
          <h1>
            AdminLTE Documentation
            <small>Current version 2.3.0</small>
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li class="active">Documentation</li>
          </ol>
        </div>

        <!-- Main content -->
        <div class="content body">

<section id="introduction">
  <h2 class="page-header"><a href="#introduction">Introduction</a></h2>
  <p class="lead">
    <b>AdminLTE</b> is a popular open source WebApp template for admin dashboards and control panels.
    It is a responsive HTML template that is based on the CSS framework Bootstrap 3.
    It utilizes all of the Bootstrap components in its design and re-styles many
    commonly used plugins to create a consistent design that can be used as a user
    interface for backend applications. AdminLTE is based on a modular design, which
    allows it to be easily customized and built upon. This documentation will guide you through
    installing the template and exploring the various components that are bundled with the template.
  </p>
</section><!-- /#introduction -->


<!-- ============================================================= -->

<section id="download">
  <h2 class="page-header"><a href="#download">Download</a></h2>
  <p class="lead">
    AdminLTE can be downloaded in two different versions, each appealing to different skill levels and use case.
  </p>
  <div class="row">
    <div class="col-sm-6">
      <div class="box box-primary">
        <div class="box-header with-border">
          <h3 class="box-title">Ready</h3>
          <span class="label label-primary pull-right"><i class="fa fa-html5"></i></span>
        </div><!-- /.box-header -->
        <div class="box-body">
          <p>Compiled and ready to use in production. Download this version if you don't want to customize AdminLTE's LESS files.</p>
          <a href="http://almsaeedstudio.com/download/AdminLTE-dist" class="btn btn-primary"><i class="fa fa-download"></i> Download</a>
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div><!-- /.col -->
    <div class="col-sm-6">
      <div class="box box-danger">
        <div class="box-header with-border">
          <h3 class="box-title">Source Code</h3>
          <span class="label label-danger pull-right"><i class="fa fa-database"></i></span>
        </div><!-- /.box-header -->
        <div class="box-body">
          <p>All files including the compiled CSS. Download this version if you plan on customizing the template. <b>Requires a LESS compiler.</b></p>
          <a href="http://almsaeedstudio.com/download/AdminLTE" class="btn btn-danger"><i class="fa fa-download"></i> Download</a>
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div><!-- /.col -->
  </div><!-- /.row -->
  <pre class="hierarchy bring-up"><code class="language-bash" data-lang="bash">File Hierarchy of the Source Code Package

AdminLTE/
├── dist/
│   ├── CSS/
│   ├── JS
│   ├── img
├── build/
│   ├── less/
│   │   ├── AdminLTE's Less files
│   └── Bootstrap-less/ (Only for reference. No modifications have been made)
│       ├── mixins/
│       ├── variables.less
│       ├── mixins.less
└── plugins/
    ├── All the customized plugins CSS and JS files</code></pre>
</section>


<!-- ============================================================= -->

<section id="dependencies">
  <h2 class="page-header"><a href="#dependencies">Dependencies</a></h2>
  <p class="lead">AdminLTE depends on two main frameworks.
    The downloadable package contains both of these libraries, so you don't have to manually download them.</p>
  <ul class="bring-up">
    <li><a href="http://getbootstrap.com" target="_blank">Bootstrap 3</a></li>
    <li><a href="http://jquery.com/" target="_blank">jQuery 1.11+</a></li>
    <li><a href="#plugins">All other plugins are listed below</a></li>
  </ul>
</section>


<!-- ============================================================= -->

<section id="advice">
  <h2 class="page-header"><a href="#advice">A Word of Advice</a></h2>
  <p class="lead">
    Before you go to see your new awesome theme, here are few tips on how to familiarize yourself with it:
  </p>

  <ul>
    <li><b>AdminLTE is based on <a href="http://getbootstrap.com/" target="_blank">Bootstrap 3</a>.</b> If you are unfamiliar with Bootstrap, visit their website and read through the documentation. All of Bootstrap components have been modified to fit the style of AdminLTE and provide a consistent look throughout the template. This way, we guarantee you will get the best of AdminLTE.</li>
    <li><b>Go through the pages that are bundled with the theme.</b> Most of the template example pages contain quick tips on how to create or use a component which can be really helpful when you need to create something on the fly.</li>
    <li><b>Documentation.</b> We are trying our best to make your experience with AdminLTE be smooth. One way to achieve that is to provide documentation and support. If you think that something is missing from the documentation, please do not hesitate to create an issue to tell us about it. Also, if you would like to contribute, email the support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</li>
    <li><b>Built with <a href="http://lesscss.org/" target="_blank">LESS</a>.</b> This theme uses the LESS compiler to make it easier to customize and use. LESS is easy to learn if you know CSS or SASS. It is not necessary to learn LESS but it will benefit you a lot in the future.</li>
    <li><b>Hosted on <a href="https://github.com/almasaeed2010/AdminLTE/" target="_blank">GitHub</a>.</b> Visit our GitHub repository to view issues, make requests, or contribute to the project.</li>
  </ul>
  <p>
    <b>Note:</b> LESS files are better commented than the compiled CSS file.
  </p>
</section>


<!-- ============================================================= -->

<section id="layout">
  <h2 class="page-header"><a href="#layout">Layout</a></h2>
  <p class="lead">The layout consists of four major parts:</p>
  <ul>
    <li>Wrapper <code>.wrapper</code>. A div that wraps the whole site.</li>
    <li>Main Header <code>.main-header</code>. Contains the logo and navbar.</li>
    <li>Sidebar <code>.sidebar-wrapper</code>. Contains the user panel and sidebar menu.</li>
    <li>Content <code>.content-wrapper</code>. Contains the page header and content.</li>
  </ul>
  <div class="callout callout-danger lead">
    <h4>Tip!</h4>
    <p>The <a href="../starter.html">starter page</a> is a good place to start building your app if you'd like to start from scratch.</p>
  </div>

  <h3>Layout Options</h3>
  <p class="lead">AdminLTE 2.0 provides a set of options to apply to your main layout. Each on of these classes can be added
    to the body tag to get the desired goal.</p>
  <ul>
    <li><b>Fixed:</b> use the class <code>.fixed</code> to get a fixed header and sidebar.</li>
    <li><b>Collapsed Sidebar:</b> use the class <code>.sidebar-collapse</code> to have a collapsed sidebar upon loading.</li>
    <li><b>Boxed Layout:</b> use the class <code>.layout-boxed</code> to get a boxed layout that stretches only to 1250px.</li>
    <li><b>Top Navigation</b> use the class <code>.layout-top-nav</code> to remove the sidebar and have your links at the top navbar.</li>
  </ul>
  <p><b>Note:</b> you cannot use both layout-boxed and fixed at the same time. Anything else can be mixed together.</p>

  <h3>Skins</h3>
  <p class="lead">Skins can be found in the dist/css/skins folder.
    Choose and the skin file that you want then add the appropriate
    class to the body tag to change the template's appearance. Here is the list of available skins:</p>
  <div class="box box-solid" style="max-width: 300px;">
    <div class="box-body no-padding">
      <table id="layout-skins-list" class="table table-striped bring-up nth-2-center">
        <thead>
          <tr>
            <th style="width: 210px;">Skin Class</th>
            <th>Preview</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>skin-blue</code></td>
            <td><a href="#" data-skin="skin-blue" class="btn btn-primary btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-blue-light</code></td>
            <td><a href="#" data-skin="skin-blue-light" class="btn btn-primary btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-yellow</code></td>
            <td><a href="#" data-skin="skin-yellow" class="btn btn-warning btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-yellow-light</code></td>
            <td><a href="#" data-skin="skin-yellow-light" class="btn btn-warning btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-green</code></td>
            <td><a href="#" data-skin="skin-green" class="btn btn-success btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-green-light</code></td>
            <td><a href="#" data-skin="skin-green-light" class="btn btn-success btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-purple</code></td>
            <td><a href="#" data-skin="skin-purple" class="btn bg-purple btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-purple-light</code></td>
            <td><a href="#" data-skin="skin-purple-light" class="btn bg-purple btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-red</code></td>
            <td><a href="#" data-skin="skin-red" class="btn btn-danger btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-red-light</code></td>
            <td><a href="#" data-skin="skin-red-light" class="btn btn-danger btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-black</code></td>
            <td><a href="#" data-skin="skin-black" class="btn bg-black btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
          <tr>
            <td><code>skin-black-light</code></td>
            <td><a href="#" data-skin="skin-black-light" class="btn bg-black btn-xs"><i class="fa fa-eye"></i></a></td>
          </tr>
        </tbody>
      </table>
    </div><!-- /.box-body -->
  </div><!-- /.box -->
</section>


<!-- ============================================================= -->

<section id="adminlte-options">
  <h2 class="page-header"><a href="#adminlte-options">AdminLTE Javascript Options</a></h2>
  <p class="lead">Modifying the options of AdminLTE's app.js can be done using one of the following ways.</p>

  <h3>Editing app.js</h3>
  <p>Within the main Javascript file, modify the <code>$.AdminLTE.options</code> object to suit your use case.</p>

  <h3>Defining AdminLTEOptions</h3>
  <p>Alternatively, you can define a global options variable named <code>AdminLTEOptions</code> and initialize it before loading app.js.</p>
  <p>Example</p>
  <pre class="prettyprint"><code class="html">&LT;script>
  var AdminLTEOptions = {
    //Enable sidebar expand on hover effect for sidebar mini
    //This option is forced to true if both the fixed layout and sidebar mini
    //are used together
    sidebarExpandOnHover: true,
    //BoxRefresh Plugin
    enableBoxRefresh: true,
    //Bootstrap.js tooltip
    enableBSToppltip: true
  };
&LT;/script>
&LT;script src="dist/js/app.js" type="text/javascript">&LT;/script></code></pre>

  <h3>Available AdminLTE Options</h3>
  <pre class="prettyprint"><code class="javascript">{
  //Add slimscroll to navbar menus
  //This requires you to load the slimscroll plugin
  //in every page before app.js
  navbarMenuSlimscroll: true,
  navbarMenuSlimscrollWidth: "3px", //The width of the scroll bar
  navbarMenuHeight: "200px", //The height of the inner menu
  //General animation speed for JS animated elements such as box collapse/expand and
  //sidebar treeview slide up/down. This options accepts an integer as milliseconds,
  //'fast', 'normal', or 'slow'
  animationSpeed: 500,
  //Sidebar push menu toggle button selector
  sidebarToggleSelector: "[data-toggle='offcanvas']",
  //Activate sidebar push menu
  sidebarPushMenu: true,
  //Activate sidebar slimscroll if the fixed layout is set (requires SlimScroll Plugin)
  sidebarSlimScroll: true,
  //Enable sidebar expand on hover effect for sidebar mini
  //This option is forced to true if both the fixed layout and sidebar mini
  //are used together
  sidebarExpandOnHover: false,
  //BoxRefresh Plugin
  enableBoxRefresh: true,
  //Bootstrap.js tooltip
  enableBSToppltip: true,
  BSTooltipSelector: "[data-toggle='tooltip']",
  //Enable Fast Click. Fastclick.js creates a more
  //native touch experience with touch devices. If you
  //choose to enable the plugin, make sure you load the script
  //before AdminLTE's app.js
  enableFastclick: true,
  //Control Sidebar Options
  enableControlSidebar: true,
  controlSidebarOptions: {
    //Which button should trigger the open/close event
    toggleBtnSelector: "[data-toggle='control-sidebar']",
    //The sidebar selector
    selector: ".control-sidebar",
    //Enable slide over content
    slide: true
  },
  //Box Widget Plugin. Enable this plugin
  //to allow boxes to be collapsed and/or removed
  enableBoxWidget: true,
  //Box Widget plugin options
  boxWidgetOptions: {
    boxWidgetIcons: {
      //Collapse icon
      collapse: 'fa-minus',
      //Open icon
      open: 'fa-plus',
      //Remove icon
      remove: 'fa-times'
    },
    boxWidgetSelectors: {
      //Remove button selector
      remove: '[data-widget="remove"]',
      //Collapse button selector
      collapse: '[data-widget="collapse"]'
    }
  },
  //Direct Chat plugin options
  directChat: {
    //Enable direct chat by default
    enable: true,
    //The button to open and close the chat contacts pane
    contactToggleSelector: '[data-widget="chat-pane-toggle"]'
  },
  //Define the set of colors to use globally around the website
  colors: {
    lightBlue: "#3c8dbc",
    red: "#f56954",
    green: "#00a65a",
    aqua: "#00c0ef",
    yellow: "#f39c12",
    blue: "#0073b7",
    navy: "#001F3F",
    teal: "#39CCCC",
    olive: "#3D9970",
    lime: "#01FF70",
    orange: "#FF851B",
    fuchsia: "#F012BE",
    purple: "#8E24AA",
    maroon: "#D81B60",
    black: "#222222",
    gray: "#d2d6de"
  },
  //The standard screen sizes that bootstrap uses.
  //If you change these in the variables.less file, change
  //them here too.
  screenSizes: {
    xs: 480,
    sm: 768,
    md: 992,
    lg: 1200
  }
}</code></pre>
</section>


<!-- ============================================================= -->

<section id="components" data-spy="scroll" data-target="#scrollspy-components">
  <h2 class="page-header"><a href="#components">Components</a></h2>
  <div class="callout callout-info lead">
    <h4>Reminder!</h4>
    <p>
      AdminLTE uses all of Bootstrap 3 components. It's a good start to review
      the <a href="http://getbootstrap.com">Bootstrap documentation</a> to get an idea of the various components
      that this documentation <b>does not</b> cover.
    </p>
  </div>
  <div class="callout callout-danger lead">
    <h4>Tip!</h4>
    <p>
      If you go through the example pages and would like to copy a component, right-click on
      the component and choose "inspect element" to get to the HTML quicker than scanning
      the HTML page.
    </p>
  </div>
  <h3 id="component-main-header">Main Header</h3>
  <p class="lead">The main header contains the logo and navbar. Construction of the
    navbar differs slightly from Bootstrap because it has components that Bootstrap doesn't provide.
    The navbar can be constructed in two way. This an example for the normal navbar and next we will provide an example for
    the top nav layout.</p>
  <div class="box box-solid">
    <div class="box-body" style="position: relative;">
      <span class="eg">Main Header Example</span>
      <header class="main-header" style="position: relative;">
        <!-- Logo -->
        <a href="index2.html" class="logo" style="position: relative;"><b>Admin</b>LTE</a>
        <!-- Header Navbar: style can be found in header.less -->
        <nav class="navbar" role="navigation" style="border: 0;max-height: 50px;">
          <!-- Sidebar toggle button-->
          <a href="#" class="sidebar-toggle" role="button">
            <span class="sr-only">Toggle navigation</span>
          </a>
          <!-- Navbar Right Menu -->
          <div class="navbar-custom-menu">
            <ul class="nav navbar-nav">
              <!-- Messages: style can be found in dropdown.less-->
              <li class="dropdown messages-menu">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                  <i class="fa fa-envelope-o"></i>
                  <span class="label label-success">4</span>
                </a>
                <ul class="dropdown-menu">
                  <li class="header">You have 4 messages</li>
                  <li>
                    <!-- inner menu: contains the actual data -->
                    <ul class="menu">
                      <li><!-- start message -->
                        <a href="#">
                          <div class="pull-left">
                            <img src="../dist/img/user2-160x160.jpg" class="img-circle" alt="User Image">
                          </div>
                          <h4>
                            Support Team
                            <small><i class="fa fa-clock-o"></i> 5 mins</small>
                          </h4>
                          <p>Why not buy a new awesome theme?</p>
                        </a>
                      </li><!-- end message -->
                    </ul>
                  </li>
                  <li class="footer"><a href="#">See All Messages</a></li>
                </ul>
              </li>
              <!-- Notifications: style can be found in dropdown.less -->
              <li class="dropdown notifications-menu">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                  <i class="fa fa-bell-o"></i>
                  <span class="label label-warning">10</span>
                </a>
                <ul class="dropdown-menu">
                  <li class="header">You have 10 notifications</li>
                  <li>
                    <!-- inner menu: contains the actual data -->
                    <ul class="menu">
                      <li>
                        <a href="#">
                          <i class="fa fa-users text-aqua"></i> 5 new members joined today
                        </a>
                      </li>
                    </ul>
                  </li>
                  <li class="footer"><a href="#">View all</a></li>
                </ul>
              </li>
              <!-- Tasks: style can be found in dropdown.less -->
              <li class="dropdown tasks-menu">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                  <i class="fa fa-flag-o"></i>
                  <span class="label label-danger">9</span>
                </a>
                <ul class="dropdown-menu">
                  <li class="header">You have 9 tasks</li>
                  <li>
                    <!-- inner menu: contains the actual data -->
                    <ul class="menu">
                      <li><!-- Task item -->
                        <a href="#">
                          <h3>
                            Design some buttons
                            <small class="pull-right">20%</small>
                          </h3>
                          <div class="progress xs">
                            <div class="progress-bar progress-bar-aqua" style="width: 20%" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">
                              <span class="sr-only">20% Complete</span>
                            </div>
                          </div>
                        </a>
                      </li><!-- end task item -->
                    </ul>
                  </li>
                  <li class="footer">
                    <a href="#">View all tasks</a>
                  </li>
                </ul>
              </li>
              <!-- User Account: style can be found in dropdown.less -->
              <li class="dropdown user user-menu">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                  <img src="../dist/img/user2-160x160.jpg" class="user-image" alt="User Image">
                  <span class="hidden-xs">Alexander Pierce</span>
                </a>
                <ul class="dropdown-menu">
                  <!-- User image -->
                  <li class="user-header">
                    <img src="../dist/img/user2-160x160.jpg" class="img-circle" alt="User Image">
                    <p>
                      Alexander Pierce - Web Developer
                      <small>Member since Nov. 2012</small>
                    </p>
                  </li>
                  <!-- Menu Body -->
                  <li class="user-body">
                    <div class="col-xs-4 text-center">
                      <a href="#">Followers</a>
                    </div>
                    <div class="col-xs-4 text-center">
                      <a href="#">Sales</a>
                    </div>
                    <div class="col-xs-4 text-center">
                      <a href="#">Friends</a>
                    </div>
                  </li>
                  <!-- Menu Footer-->
                  <li class="user-footer">
                    <div class="pull-left">
                      <a href="#" class="btn btn-default btn-flat">Profile</a>
                    </div>
                    <div class="pull-right">
                      <a href="#" class="btn btn-default btn-flat">Sign out</a>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </nav>
      </header>
    </div>
  </div>
  <pre class="prettyprint">&LT;header class="main-header">
  &LT;a href="../../index2.html" class="logo">
    &LT;!-- LOGO -->
    AdminLTE
  &LT;/a>
  &LT;!-- Header Navbar: style can be found in header.less -->
  &LT;nav class="navbar navbar-static-top" role="navigation">
    &LT;!-- Navbar Right Menu -->
    &LT;div class="navbar-custom-menu">
      &LT;ul class="nav navbar-nav">
        &LT;!-- Messages: style can be found in dropdown.less-->
        &LT;li class="dropdown messages-menu">
          &LT;a href="#" class="dropdown-toggle" data-toggle="dropdown">
            &LT;i class="fa fa-envelope-o">&LT;/i>
            &LT;span class="label label-success">4&LT;/span>
          &LT;/a>
          &LT;ul class="dropdown-menu">
            &LT;li class="header">You have 4 messages&LT;/li>
            &LT;li>
              &LT;!-- inner menu: contains the actual data -->
              &LT;ul class="menu">
                &LT;li>&LT;!-- start message -->
                  &LT;a href="#">
                    &LT;div class="pull-left">
                      &LT;img src="dist/img/user2-160x160.jpg" class="img-circle" alt="User Image">
                    &LT;/div>
                    &LT;h4>
                      Sender Name
                      &LT;small>&LT;i class="fa fa-clock-o">&LT;/i> 5 mins&LT;/small>
                    &LT;/h4>
                    &LT;p>Message Excerpt&LT;/p>
                  &LT;/a>
                &LT;/li>&LT;!-- end message -->
                ...
              &LT;/ul>
            &LT;/li>
            &LT;li class="footer">&LT;a href="#">See All Messages&LT;/a>&LT;/li>
          &LT;/ul>
        &LT;/li>
        &LT;!-- Notifications: style can be found in dropdown.less -->
        &LT;li class="dropdown notifications-menu">
          &LT;a href="#" class="dropdown-toggle" data-toggle="dropdown">
            &LT;i class="fa fa-bell-o">&LT;/i>
            &LT;span class="label label-warning">10&LT;/span>
          &LT;/a>
          &LT;ul class="dropdown-menu">
            &LT;li class="header">You have 10 notifications&LT;/li>
            &LT;li>
              &LT;!-- inner menu: contains the actual data -->
              &LT;ul class="menu">
                &LT;li>
                  &LT;a href="#">
                    &LT;i class="ion ion-ios-people info">&LT;/i> Notification title
                  &LT;/a>
                &LT;/li>
                ...
              &LT;/ul>
            &LT;/li>
            &LT;li class="footer">&LT;a href="#">View all&LT;/a>&LT;/li>
          &LT;/ul>
        &LT;/li>
        &LT;!-- Tasks: style can be found in dropdown.less -->
        &LT;li class="dropdown tasks-menu">
          &LT;a href="#" class="dropdown-toggle" data-toggle="dropdown">
            &LT;i class="fa fa-flag-o">&LT;/i>
            &LT;span class="label label-danger">9&LT;/span>
          &LT;/a>
          &LT;ul class="dropdown-menu">
            &LT;li class="header">You have 9 tasks&LT;/li>
            &LT;li>
              &LT;!-- inner menu: contains the actual data -->
              &LT;ul class="menu">
                &LT;li>&LT;!-- Task item -->
                  &LT;a href="#">
                    &LT;h3>
                      Design some buttons
                      &LT;small class="pull-right">20%&LT;/small>
                    &LT;/h3>
                    &LT;div class="progress xs">
                      &LT;div class="progress-bar progress-bar-aqua" style="width: 20%" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">
                        &LT;span class="sr-only">20% Complete&LT;/span>
                      &LT;/div>
                    &LT;/div>
                  &LT;/a>
                &LT;/li>&LT;!-- end task item -->
                ...
              &LT;/ul>
            &LT;/li>
            &LT;li class="footer">
              &LT;a href="#">View all tasks&LT;/a>
            &LT;/li>
          &LT;/ul>
        &LT;/li>
        &LT;!-- User Account: style can be found in dropdown.less -->
        &LT;li class="dropdown user user-menu">
          &LT;a href="#" class="dropdown-toggle" data-toggle="dropdown">
            &LT;img src="dist/img/user2-160x160.jpg" class="user-image" alt="User Image">
            &LT;span class="hidden-xs">Alexander Pierce&LT;/span>
          &LT;/a>
          &LT;ul class="dropdown-menu">
            &LT;!-- User image -->
            &LT;li class="user-header">
              &LT;img src="dist/img/user2-160x160.jpg" class="img-circle" alt="User Image">
              &LT;p>
                Alexander Pierce - Web Developer
                &LT;small>Member since Nov. 2012&LT;/small>
              &LT;/p>
            &LT;/li>
            &LT;!-- Menu Body -->
            &LT;li class="user-body">
              &LT;div class="col-xs-4 text-center">
                &LT;a href="#">Followers&LT;/a>
              &LT;/div>
              &LT;div class="col-xs-4 text-center">
                &LT;a href="#">Sales&LT;/a>
              &LT;/div>
              &LT;div class="col-xs-4 text-center">
                &LT;a href="#">Friends&LT;/a>
              &LT;/div>
            &LT;/li>
            &LT;!-- Menu Footer-->
            &LT;li class="user-footer">
              &LT;div class="pull-left">
                &LT;a href="#" class="btn btn-default btn-flat">Profile&LT;/a>
              &LT;/div>
              &LT;div class="pull-right">
                &LT;a href="#" class="btn btn-default btn-flat">Sign out&LT;/a>
              &LT;/div>
            &LT;/li>
          &LT;/ul>
        &LT;/li>
      &LT;/ul>
    &LT;/div>
  &LT;/nav>
&LT;/header></pre>
  <h4>Top Nav Layout. Main Header Example.</h4>
  <div class="callout callout-info lead">
    <h4>Reminder!</h4>
    <p>To use this main header instead of the regular one, you must add the <code>layout-top-nav</code> class to the body tag.</p>
  </div>
  <div class="box box-solid">
    <div class="box-body layout-top-nav">
      <span class="eg">Top Nav Example</span>
      <header class="main-header">
        <nav class="navbar navbar-static-top">
          <div class="container-fluid">
            <div class="navbar-header">
              <a href="../../index2.html" class="navbar-brand"><b>Admin</b>LTE</a>
              <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar-collapse">
                <i class="fa fa-bars"></i>
              </button>
            </div>

            <!-- Collect the nav links, forms, and other content for toggling -->
            <div class="collapse navbar-collapse" id="navbar-collapse">
              <ul class="nav navbar-nav">
                <li class="active"><a href="#">Link <span class="sr-only">(current)</span></a></li>
                <li><a href="#">Link</a></li>
                <li class="dropdown">
                  <a href="#" class="dropdown-toggle" data-toggle="dropdown">Dropdown <span class="caret"></span></a>
                  <ul class="dropdown-menu" role="menu">
                    <li><a href="#">Action</a></li>
                    <li><a href="#">Another action</a></li>
                    <li><a href="#">Something else here</a></li>
                    <li class="divider"></li>
                    <li><a href="#">Separated link</a></li>
                    <li class="divider"></li>
                    <li><a href="#">One more separated link</a></li>
                  </ul>
                </li>
              </ul>
              <form class="navbar-form navbar-left" role="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="navbar-search-input" placeholder="Search">
                </div>
              </form>
              <ul class="nav navbar-nav navbar-right">
                <li><a href="#">Link</a></li>
                <li class="dropdown">
                  <a href="#" class="dropdown-toggle" data-toggle="dropdown">Dropdown <span class="caret"></span></a>
                  <ul class="dropdown-menu" role="menu">
                    <li><a href="#">Action</a></li>
                    <li><a href="#">Another action</a></li>
                    <li><a href="#">Something else here</a></li>
                    <li class="divider"></li>
                    <li><a href="#">Separated link</a></li>
                  </ul>
                </li>
              </ul>
            </div><!-- /.navbar-collapse -->
          </div><!-- /.container-fluid -->
        </nav>
      </header>
    </div>
  </div>
  <pre class="prettyprint">
&LT;header class="main-header">
  &LT;nav class="navbar navbar-static-top">
    &LT;div class="container-fluid">
    &LT;div class="navbar-header">
      &LT;a href="../../index2.html" class="navbar-brand">&LT;b>Admin&LT;/b>LTE&LT;/a>
      &LT;button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar-collapse">
        &LT;i class="fa fa-bars">&LT;/i>
      &LT;/button>
    &LT;/div>

    &LT;!-- Collect the nav links, forms, and other content for toggling -->
    &LT;div class="collapse navbar-collapse" id="navbar-collapse">
      &LT;ul class="nav navbar-nav">
        &LT;li class="active">&LT;a href="#">Link &LT;span class="sr-only">(current)&LT;/span>&LT;/a>&LT;/li>
        &LT;li>&LT;a href="#">Link&LT;/a>&LT;/li>
        &LT;li class="dropdown">
          &LT;a href="#" class="dropdown-toggle" data-toggle="dropdown">Dropdown &LT;span class="caret">&LT;/span>&LT;/a>
          &LT;ul class="dropdown-menu" role="menu">
            &LT;li>&LT;a href="#">Action&LT;/a>&LT;/li>
            &LT;li>&LT;a href="#">Another action&LT;/a>&LT;/li>
            &LT;li>&LT;a href="#">Something else here&LT;/a>&LT;/li>
            &LT;li class="divider">&LT;/li>
            &LT;li>&LT;a href="#">Separated link&LT;/a>&LT;/li>
            &LT;li class="divider">&LT;/li>
            &LT;li>&LT;a href="#">One more separated link&LT;/a>&LT;/li>
          &LT;/ul>
        &LT;/li>
      &LT;/ul>
      &LT;form class="navbar-form navbar-left" role="search">
        &LT;div class="form-group">
          &LT;input type="text" class="form-control" id="navbar-search-input" placeholder="Search">
        &LT;/div>
      &LT;/form>
      &LT;ul class="nav navbar-nav navbar-right">
        &LT;li>&LT;a href="#">Link&LT;/a>&LT;/li>
        &LT;li class="dropdown">
          &LT;a href="#" class="dropdown-toggle" data-toggle="dropdown">Dropdown &LT;span class="caret">&LT;/span>&LT;/a>
          &LT;ul class="dropdown-menu" role="menu">
            &LT;li>&LT;a href="#">Action&LT;/a>&LT;/li>
            &LT;li>&LT;a href="#">Another action&LT;/a>&LT;/li>
            &LT;li>&LT;a href="#">Something else here&LT;/a>&LT;/li>
            &LT;li class="divider">&LT;/li>
            &LT;li>&LT;a href="#">Separated link&LT;/a>&LT;/li>
          &LT;/ul>
        &LT;/li>
      &LT;/ul>
    &LT;/div>&LT;!-- /.navbar-collapse -->
    &LT;/div>&LT;!-- /.container-fluid -->
  &LT;/nav>
&LT;/header></pre>

  <!-- ===================================================================== -->

  <h3 id="component-sidebar">Sidebar</h3>
  <p class="lead">
    The sidebar used in this page to the left provides an example of what your sidebar should like.
    Construction of a sidebar:
  </p>
  <pre class="prettyprint">
&LT;div class="main-sidebar">
  &LT;!-- Inner sidebar -->
  &LT;div class="sidebar">
    &LT;!-- user panel (Optional) -->
    &LT;div class="user-panel">
      &LT;div class="pull-left image">
        &LT;img src="dist/img/user2-160x160.jpg" class="img-circle" alt="User Image">
      &LT;/div>
      &LT;div class="pull-left info">
        &LT;p>User Name&LT;/p>

        &LT;a href="#">&LT;i class="fa fa-circle text-success">&LT;/i> Online&LT;/a>
      &LT;/div>
    &LT;/div>&LT;!-- /.user-panel -->

    &LT;!-- Search Form (Optional) -->
    &LT;form action="#" method="get" class="sidebar-form">
      &LT;div class="input-group">
        &LT;input type="text" name="q" class="form-control" placeholder="Search...">
        &LT;span class="input-group-btn">
          &LT;button type="submit" name="search" id="search-btn" class="btn btn-flat">&LT;i class="fa fa-search">&LT;/i>&LT;/button>
        &LT;/span>
      &LT;/div>
    &LT;/form>&LT;!-- /.sidebar-form -->

    &LT;!-- Sidebar Menu -->
    &LT;ul class="sidebar-menu">
      &LT;li class="header">HEADER&LT;/li>
      &LT;!-- Optionally, you can add icons to the links -->
      &LT;li class="active">&LT;a href="#">&LT;span>Link&LT;/span>&LT;/a>&LT;&LT;/li>
      &LT;li>&LT;a href="#">&LT;span>Another Link&LT;/span>&LT;/a>&LT;/li>
      &LT;li class="treeview">
        &LT;a href="#">&LT;span>Multilevel&LT;/span> &LT;i class="fa fa-angle-left pull-right">&LT;/i>&LT;/a>
        &LT;ul class="treeview-menu">
          &LT;li>&LT;a href="#">Link in level 2&LT;/a>&LT;/li>
          &LT;li>&LT;a href="#">Link in level 2&LT;/a>&LT;/li>
        &LT;/ul>
      &LT;/li>
    &LT;/ul>&LT;!-- /.sidebar-menu -->

  &LT;/div>&LT;!-- /.sidebar -->
&LT;/div>&LT;!-- /.main-sidebar --></pre>

  <h3 id="component-control-sidebar">Control Sidebar</h3>
  <p class="lead">Control sidebar is the right side bar. It can be used for many purposes and is extremely easy
    to create. The sidebar ships with two different show/hide styles. The first allows the sidebar to
  slide over the content. The second pushes the content to make space for the sidebar. Either of
  these methods can be set through the <a href="#adminlte-options">Javascript options</a>.</p>
  <p class="lead">The following code should be placed within the <code>.wrapper</code> div. I prefer
  to place it right after the footer.</p>
  <p class="lead">Dark Sidebar Markup</p>
<pre class="prettyprint"><code class="lang-html">&LT;!-- The Right Sidebar -->
&LT;aside class="control-sidebar control-sidebar-dark">
  &LT;!-- Content of the sidebar goes here -->
&LT;/aside>
&LT;!-- The sidebar's background -->
&LT;!-- This div must placed right after the sidebar for it to work-->
&LT;div class="control-sidebar-bg">&LT;/div></code></pre>

  <p class="lead">Light Sidebar Markup</p>
<pre class="prettyprint"><code class="lang-html">&LT;!-- The Right Sidebar -->
&LT;aside class="control-sidebar control-sidebar-light">
  &LT;!-- Content of the sidebar goes here -->
&LT;/aside>
&LT;!-- The sidebar's background -->
&LT;!-- This div must placed right after the sidebar for it to work-->
&LT;div class="control-sidebar-bg">&LT;/div></code></pre>

  <p class="lead">Once you create the sidebar, you will need a toggle button to open/close it.
  By adding the attribute <code>data-toggle="control-sidebar"</code> to any button, it will
  automatically act as the toggle button.</p>

  <p class="lead">Toggle Button Example</p>
  <button class="btn btn-primary" data-toggle="control-sidebar">Toggle Right Sidebar</button><br><br>

  <p class="lead">Sidebar Toggle Markup</p>
  <pre class="prettyprint"><code class="lang-html">&LT;button class="btn btn-default" data-toggle="control-sidebar">Toggle Right Sidebar&LT;/button></code></pre>
  <!-- ===================================================================== -->

  <h3 id="component-info-box">Info Box</h3>
  <p class="lead">Info boxes are used to display statistical snippets. There are two types of info boxes.</p>
  <h4>First Type of Info Boxes</h4>
  <!-- Info Boxes -->
  <div class="row">
    <div class="col-md-3 col-sm-6 col-xs-12">
      <div class="info-box">
        <span class="info-box-icon bg-aqua"><i class="fa fa-envelope-o"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Messages</span>
          <span class="info-box-number">1,410</span>
        </div><!-- /.info-box-content -->
      </div><!-- /.info-box -->
    </div><!-- /.col -->
    <div class="col-md-3 col-sm-6 col-xs-12">
      <div class="info-box">
        <span class="info-box-icon bg-green"><i class="fa fa-flag-o"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Bookmarks</span>
          <span class="info-box-number">410</span>
        </div><!-- /.info-box-content -->
      </div><!-- /.info-box -->
    </div><!-- /.col -->
    <div class="col-md-3 col-sm-6 col-xs-12">
      <div class="info-box">
        <span class="info-box-icon bg-yellow"><i class="fa fa-files-o"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Uploads</span>
          <span class="info-box-number">13,648</span>
        </div><!-- /.info-box-content -->
      </div><!-- /.info-box -->
    </div><!-- /.col -->
    <div class="col-md-3 col-sm-6 col-xs-12">
      <div class="info-box">
        <span class="info-box-icon bg-red"><i class="fa fa-star-o"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Likes</span>
          <span class="info-box-number">93,139</span>
        </div><!-- /.info-box-content -->
      </div><!-- /.info-box -->
    </div><!-- /.col -->
  </div><!-- /.row -->
  <p class="lead">Markup</p>
  <pre class="prettyprint"><code class="lang-html">&LT;div class="info-box">
  &LT;!-- Apply any bg-* class to to the icon to color it -->
  &LT;span class="info-box-icon bg-red">&LT;i class="fa fa-star-o">&LT;/i>&LT;/span>
  &LT;div class="info-box-content">
    &LT;span class="info-box-text">Likes&LT;/span>
    &LT;span class="info-box-number">93,139&LT;/span>
  &LT;/div>&LT;!-- /.info-box-content -->
&LT;/div>&LT;!-- /.info-box --></code></pre>

  <h4>Second Type of Info Boxes</h4>
  <div class="row">
    <div class="col-md-3 col-sm-6 col-xs-12">
      <div class="info-box bg-aqua">
        <span class="info-box-icon"><i class="fa fa-bookmark-o"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Bookmarks</span>
          <span class="info-box-number">41,410</span>
          <div class="progress">
            <div class="progress-bar" style="width: 70%"></div>
          </div>
          <span class="progress-description">
            70% Increase in 30 Days
          </span>
        </div><!-- /.info-box-content -->
      </div><!-- /.info-box -->
    </div><!-- /.col -->
    <div class="col-md-3 col-sm-6 col-xs-12">
      <div class="info-box bg-green">
        <span class="info-box-icon"><i class="fa fa-thumbs-o-up"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Likes</span>
          <span class="info-box-number">41,410</span>
          <div class="progress">
            <div class="progress-bar" style="width: 70%"></div>
          </div>
          <span class="progress-description">
            70% Increase in 30 Days
          </span>
        </div><!-- /.info-box-content -->
      </div><!-- /.info-box -->
    </div><!-- /.col -->
    <div class="col-md-3 col-sm-6 col-xs-12">
      <div class="info-box bg-yellow">
        <span class="info-box-icon"><i class="fa fa-calendar"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Events</span>
          <span class="info-box-number">41,410</span>
          <div class="progress">
            <div class="progress-bar" style="width: 70%"></div>
          </div>
          <span class="progress-description">
            70% Increase in 30 Days
          </span>
        </div><!-- /.info-box-content -->
      </div><!-- /.info-box -->
    </div><!-- /.col -->
    <div class="col-md-3 col-sm-6 col-xs-12">
      <div class="info-box bg-red">
        <span class="info-box-icon"><i class="fa fa-comments-o"></i></span>
        <div class="info-box-content">
          <span class="info-box-text">Comments</span>
          <span class="info-box-number">41,410</span>
          <div class="progress">
            <div class="progress-bar" style="width: 70%"></div>
          </div>
          <span class="progress-description">
            70% Increase in 30 Days
          </span>
        </div><!-- /.info-box-content -->
      </div><!-- /.info-box -->
    </div><!-- /.col -->
  </div><!-- /.row -->
  <p class="lead">Markup</p>
  <pre class="prettyprint"><code class="lang-html">&LT;!-- Apply any bg-* class to to the info-box to color it -->
&LT;div class="info-box bg-red">
  &LT;span class="info-box-icon">&LT;i class="fa fa-comments-o">&LT;/i>&LT;/span>
  &LT;div class="info-box-content">
    &LT;span class="info-box-text">Likes&LT;/span>
    &LT;span class="info-box-number">41,410&LT;/span>
    &LT;!-- The progress section is optional -->
    &LT;div class="progress">
      &LT;div class="progress-bar" style="width: 70%">&LT;/div>
    &LT;/div>
    &LT;span class="progress-description">
      70% Increase in 30 Days
    &LT;/span>
  &LT;/div>&LT;!-- /.info-box-content -->
&LT;/div>&LT;!-- /.info-box --></code></pre>
  <p class="lead">The only thing you need to change to alternate between these style is change the placement of the bg-* class. For the
    first style apply any bg-* class to the icon itself. For the other style, apply the bg-* class to the info-box div.</p>
  <!-- ===================================================================== -->

  <h3 id="component-box">Box</h3>
  <p class="lead">The box component is the most widely used component through out this template. You can
    use it for anything from displaying charts to just blocks of text. It comes in many different
    styles that we will explore below.</p>
  <h4>Default Box Markup</h4>
  <div class="box">
    <div class="box-header with-border">
      <h3 class="box-title">Default Box Example</h3>
      <div class="box-tools pull-right">
        <!-- Buttons, labels, and many other things can be placed here! -->
        <!-- Here is a label for example -->
        <span class="label label-primary">Label</span>
      </div><!-- /.box-tools -->
    </div><!-- /.box-header -->
    <div class="box-body">
      The body of the box
    </div><!-- /.box-body -->
    <div class="box-footer">
      The footer of the box
    </div><!-- box-footer -->
  </div><!-- /.box -->
  <pre class="prettyprint">&LT;div class="box">
  &LT;div class="box-header with-border">
    &LT;h3 class="box-title">Default Box Example&LT;/h3>
    &LT;div class="box-tools pull-right">
      &LT;!-- Buttons, labels, and many other things can be placed here! -->
      &LT;!-- Here is a label for example -->
      &LT;span class="label label-primary">Label&LT;/span>
    &LT;/div>&LT;!-- /.box-tools -->
  &LT;/div>&LT;!-- /.box-header -->
  &LT;div class="box-body">
    The body of the box
  &LT;/div>&LT;!-- /.box-body -->
  &LT;div class="box-footer">
    The footer of the box
  &LT;/div>&LT;!-- box-footer -->
&LT;/div>&LT;!-- /.box --></pre>
  <h4>Box Variants</h4>
  <p class="lead">You can change the style of the box by adding any of the contextual classes.</p>
  <div class="row">
    <div class="col-md-4">
      <div class="box">
        <div class="box-header with-border">
          <h3 class="box-title">Default Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
    <div class="col-md-4">
      <div class="box box-primary">
        <div class="box-header with-border">
          <h3 class="box-title">Primary Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
    <div class="col-md-4">
      <div class="box box-info">
        <div class="box-header with-border">
          <h3 class="box-title">Info Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
    <div class="clearfix"></div>
    <div class="col-md-4">
      <div class="box box-warning">
        <div class="box-header with-border">
          <h3 class="box-title">Warning Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
    <div class="col-md-4">
      <div class="box box-success">
        <div class="box-header with-border">
          <h3 class="box-title">Success Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
    <div class="col-md-4">
      <div class="box box-danger">
        <div class="box-header with-border">
          <h3 class="box-title">Danger Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
  </div><!-- /.row -->
  <pre class="prettyprint">&LT;div class="box box-default">...&LT;/div>
&LT;div class="box box-primary">...&LT;/div>
&LT;div class="box box-info">...&LT;/div>
&LT;div class="box box-warning">...&LT;/div>
&LT;div class="box box-success">...&LT;/div>
&LT;div class="box box-danger">...&LT;/div></pre>

  <h4>Solid Box</h4>
  <p class="lead">Solid Boxes are alternative ways to display boxes.
    They can be created by simply adding the box-solid class to the box component.
    You may also use contextual classes with you solid boxes.</p>
  <div class="row">
    <div class="col-md-4">
      <div class="box box-solid box-default">
        <div class="box-header">
          <h3 class="box-title">Default Solid Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
    <div class="col-md-4">
      <div class="box box-solid box-primary">
        <div class="box-header">
          <h3 class="box-title">Primary Solid Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
    <div class="col-md-4">
      <div class="box box-solid box-info">
        <div class="box-header">
          <h3 class="box-title">Info Solid Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
    <div class="clearfix"></div>
    <div class="col-md-4">
      <div class="box box-solid box-warning">
        <div class="box-header">
          <h3 class="box-title">Warning Solid Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
    <div class="col-md-4">
      <div class="box box-solid box-success">
        <div class="box-header">
          <h3 class="box-title">Success Solid Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
    <div class="col-md-4">
      <div class="box box-solid box-danger">
        <div class="box-header">
          <h3 class="box-title">Danger Solid Box Example</h3>
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
    </div>
  </div><!-- /.row -->
  <pre class="prettyprint">
&LT;div class="box box-solid box-default">...&LT;/div>
&LT;div class="box box-solid box-primary">...&LT;/div>
&LT;div class="box box-solid box-info">...&LT;/div>
&LT;div class="box box-solid box-warning">...&LT;/div>
&LT;div class="box box-solid box-success">...&LT;/div>
&LT;div class="box box-solid box-danger">...&LT;/div></pre>
  <h4>Box Tools</h4>
  <p class="lead">Boxes can contain tools to deploy a specific event or provide simple info. The following examples makes use
    of multiple AdminLTE components within the header of the box.</p>
  <p>AdminLTE data-widget attribute provides boxes with the ability to collapse or be removed. The buttons
    are placed in the box-tools which is placed in the box-header.</p>
  <pre class="prettyprint">
&LT;!-- This will cause the box to be removed when clicked -->
&LT;button class="btn btn-box-tool" data-widget="remove" data-toggle="tooltip" title="Remove">&LT;i class="fa fa-times">&LT;/i>&LT;/button>
&LT;!-- This will cause the box to collapse when clicked -->
&LT;button class="btn btn-box-tool" data-widget="collapse" data-toggle="tooltip" title="Collapse">&LT;i class="fa fa-minus">&LT;/i>&LT;/button></pre>
  <div class="row">
    <div class="col-md-4">
      <div class="box box-default">
        <div class="box-header with-border">
          <h3 class="box-title">Collapsable</h3>
          <div class="box-tools pull-right">
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
          </div><!-- /.box-tools -->
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
      <pre class="prettyprint">
&LT;div class="box box-default">
  &LT;div class="box-header with-border">
    &LT;h3 class="box-title">Collapsable&LT;/h3>
    &LT;div class="box-tools pull-right">
      &LT;button class="btn btn-box-tool" data-widget="collapse">&LT;i class="fa fa-minus">&LT;/i>&LT;/button>
    &LT;/div>&LT;!-- /.box-tools -->
  &LT;/div>&LT;!-- /.box-header -->
  &LT;div class="box-body">
    The body of the box
  &LT;/div>&LT;!-- /.box-body -->
&LT;/div>&LT;!-- /.box --></pre>
    </div>
    <div class="col-md-4">
      <div class="box box-default">
        <div class="box-header with-border">
          <h3 class="box-title">Removable</h3>
          <div class="box-tools pull-right">
            <button class="btn btn-box-tool" data-widget="remove"><i class="fa fa-times"></i></button>
          </div><!-- /.box-tools -->
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
      <pre class="prettyprint">
&LT;div class="box box-default">
  &LT;div class="box-header with-border">
    &LT;h3 class="box-title">Removable&LT;/h3>
    &LT;div class="box-tools pull-right">
      &LT;button class="btn btn-box-tool" data-widget="remove">&LT;i class="fa fa-times">&LT;/i>&LT;/button>
    &LT;/div>&LT;!-- /.box-tools -->
  &LT;/div>&LT;!-- /.box-header -->
  &LT;div class="box-body">
    The body of the box
  &LT;/div>&LT;!-- /.box-body -->
&LT;/div>&LT;!-- /.box --></pre>
    </div>
    <div class="col-md-4">
      <div class="box box-default collapsed-box">
        <div class="box-header with-border">
          <h3 class="box-title">Expandable</h3>
          <div class="box-tools pull-right">
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i></button>
          </div><!-- /.box-tools -->
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
      <pre class="prettyprint">
&LT;div class="box box-default collapsed-box">
  &LT;div class="box-header with-border">
    &LT;h3 class="box-title">Expandable&LT;/h3>
    &LT;div class="box-tools pull-right">
      &LT;button class="btn btn-box-tool" data-widget="collapse">&LT;i class="fa fa-plus">&LT;/i>&LT;/button>
    &LT;/div>&LT;!-- /.box-tools -->
  &LT;/div>&LT;!-- /.box-header -->
  &LT;div class="box-body">
    The body of the box
  &LT;/div>&LT;!-- /.box-body -->
&LT;/div>&LT;!-- /.box --></pre>
    </div>
  </div><!-- /.row -->
  <p>We can also add labels, badges, pagination, tooltips, inputs and many more in the box tools. A few examples:</p>
  <div class="row">
    <div class="col-md-4">
      <div class="box box-default">
        <div class="box-header with-border">
          <h3 class="box-title">Labels</h3>
          <div class="box-tools pull-right">
            <span class="label label-default">Some Label</span>
          </div><!-- /.box-tools -->
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
      <pre class="prettyprint">
&LT;div class="box box-default">
  &LT;div class="box-header with-border">
    &LT;h3 class="box-title">Labels&LT;/h3>
    &LT;div class="box-tools pull-right">
      &LT;span class="label label-default">8 New Messages&LT;/span>
    &LT;/div>&LT;!-- /.box-tools -->
  &LT;/div>&LT;!-- /.box-header -->
  &LT;div class="box-body">
    The body of the box
  &LT;/div>&LT;!-- /.box-body -->
&LT;/div>&LT;!-- /.box --></pre>
    </div>
    <div class="col-md-4">
      <div class="box box-default">
        <div class="box-header with-border">
          <h3 class="box-title">Input</h3>
          <div class="box-tools pull-right">
            <div class="has-feedback">
              <input type="text" class="form-control input-sm" placeholder="Search...">
              <span class="glyphicon glyphicon-search form-control-feedback text-muted"></span>
            </div>
          </div><!-- /.box-tools -->
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
      <pre class="prettyprint">
&LT;div class="box box-default">
  &LT;div class="box-header with-border">
    &LT;h3 class="box-title">Input&LT;/h3>
    &LT;div class="box-tools pull-right">
      &LT;div class="has-feedback">
        &LT;input type="text" class="form-control input-sm" placeholder="Search...">
        &LT;span class="glyphicon glyphicon-search form-control-feedback">&LT;/span>
      &LT;/div>
    &LT;/div>&LT;!-- /.box-tools -->
  &LT;/div>&LT;!-- /.box-header -->
  &LT;div class="box-body">
    The body of the box
  &LT;/div>&LT;!-- /.box-body -->
&LT;/div>&LT;!-- /.box --></pre>
    </div>
    <div class="col-md-4">
      <div class="box box-default">
        <div class="box-header with-border">
          <h3 class="box-title">Tootips on buttons</h3>
          <div class="box-tools pull-right">
            <button class="btn btn-box-tool" data-widget="collapse" data-toggle="tooltip" title="Collapse"><i class="fa fa-minus"></i></button>
            <button class="btn btn-box-tool" data-widget="remove" data-toggle="tooltip" title="Remove"><i class="fa fa-times"></i></button>
          </div><!-- /.box-tools -->
        </div><!-- /.box-header -->
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
      </div><!-- /.box -->
      <pre class="prettyprint">
&LT;div class="box box-default">
  &LT;div class="box-header with-border">
    &LT;h3 class="box-title">Tooltips on buttons&LT;/h3>
    &LT;div class="box-tools pull-right">
      &LT;button class="btn btn-box-tool" data-widget="collapse" data-toggle="tooltip" title="Collapse">&LT;i class="fa fa-minus">&LT;/i>&LT;/button>
      &LT;button class="btn btn-box-tool" data-widget="remove" data-toggle="tooltip" title="Remove">&LT;i class="fa fa-times">&LT;/i>&LT;/button>
    &LT;/div>&LT;!-- /.box-tools -->
  &LT;/div>&LT;!-- /.box-header -->
  &LT;div class="box-body">
    The body of the box
  &LT;/div>&LT;!-- /.box-body -->
&LT;/div>&LT;!-- /.box --></pre>
    </div><!-- /.col -->
  </div><!-- /.row -->
  <p>
    If you inserted a box into the document after <code>app.js</code> was loaded, you have to activate
    the collapse/remove buttons explicitly by calling <code>.activateBox()</code>:
  </p>
  <pre class="prettyprint"><code class="html">&LT;script>
    $("#box-widget").activateBox();
&LT;/script></code></pre>

  <h4>Loading States</h4>
  <div class="row">
    <div class="col-md-6">
      <div class="box box-default">
        <div class="box-header with-border">
          <h3 class="box-title">Loading state</h3>
        </div>
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
        <!-- Loading (remove the following to stop the loading)-->
        <div class="overlay">
          <i class="fa fa-refresh fa-spin"></i>
        </div>
        <!-- end loading -->
      </div><!-- /.box -->
    </div><!-- /.col -->

    <div class="col-md-6">
      <div class="box box-default box-solid">
        <div class="box-header with-border">
          <h3 class="box-title">Loading state (.box-solid)</h3>
        </div>
        <div class="box-body">
          The body of the box
        </div><!-- /.box-body -->
        <!-- Loading (remove the following to stop the loading)-->
        <div class="overlay">
          <i class="fa fa-refresh fa-spin"></i>
        </div>
        <!-- end loading -->
      </div><!-- /.box -->
    </div><!-- /.col -->
  </div><!-- /.row -->
  <p class="lead">
    To simulate a loading state, simply place this code before the <code>.box</code> closing tag.
  </p>
  <pre class="prettyprint"><code class="html">&LT;div class="overlay">
  &LT;i class="fa fa-refresh fa-spin">&LT;/i>
&LT;/div>
</code></pre>
  <h3 id="component-direct-chat">Direct Chat</h3>
  <p class="lead">The direct chat widget extends the box component to create a beautiful chat interface. This widget
    consists of a required messages pane and an <b>optional</b> contacts pane. Examples:</p>
  <!-- Direct Chat -->
  <div class="row">
    <div class="col-md-3">
      <!-- DIRECT CHAT PRIMARY -->
      <div class="box box-primary direct-chat direct-chat-primary">
        <div class="box-header with-border">
          <h3 class="box-title">Direct Chat</h3>
          <div class="box-tools pull-right">
            <span data-toggle="tooltip" title="3 New Messages" class="badge bg-light-blue">3</span>
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            <button class="btn btn-box-tool" data-toggle="tooltip" title="Contacts" data-widget="chat-pane-toggle"><i class="fa fa-comments"></i></button>
            <button class="btn btn-box-tool" data-widget="remove"><i class="fa fa-times"></i></button>
          </div>
        </div><!-- /.box-header -->
        <div class="box-body">
          <!-- Conversations are loaded here -->
          <div class="direct-chat-messages">
            <!-- Message. Default to the left -->
            <div class="direct-chat-msg">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-left">Alexander Pierce</span>
                <span class="direct-chat-timestamp pull-right">23 Jan 2:00 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user1-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                Is this template really for free? That's unbelievable!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->

            <!-- Message to the right -->
            <div class="direct-chat-msg right">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-right">Sarah Bullock</span>
                <span class="direct-chat-timestamp pull-left">23 Jan 2:05 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user3-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                You better believe it!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->
          </div><!--/.direct-chat-messages-->

          <!-- Contacts are loaded here -->
          <div class="direct-chat-contacts">
            <ul class="contacts-list">
              <li>
                <a href="#">
                  <img class="contacts-list-img" src="../dist/img/user1-128x128.jpg" alt="Contact Avatar">
                  <div class="contacts-list-info">
                    <span class="contacts-list-name">
                      Count Dracula
                      <small class="contacts-list-date pull-right">2/28/2015</small>
                    </span>
                    <span class="contacts-list-msg">How have you been? I was...</span>
                  </div><!-- /.contacts-list-info -->
                </a>
              </li><!-- End Contact Item -->
            </ul><!-- /.contatcts-list -->
          </div><!-- /.direct-chat-pane -->
        </div><!-- /.box-body -->
        <div class="box-footer">
          <form action="#" method="post">
            <div class="input-group">
              <input type="text" name="message" placeholder="Type Message ..." class="form-control">
              <span class="input-group-btn">
                <button type="button" class="btn btn-primary btn-flat">Send</button>
              </span>
            </div>
          </form>
        </div><!-- /.box-footer-->
      </div><!--/.direct-chat -->
    </div><!-- /.col -->

    <div class="col-md-3">
      <!-- DIRECT CHAT SUCCESS -->
      <div class="box box-success direct-chat direct-chat-success">
        <div class="box-header with-border">
          <h3 class="box-title">Direct Chat</h3>
          <div class="box-tools pull-right">
            <span data-toggle="tooltip" title="3 New Messages" class="badge bg-green">3</span>
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            <button class="btn btn-box-tool" data-toggle="tooltip" title="Contacts" data-widget="chat-pane-toggle"><i class="fa fa-comments"></i></button>
            <button class="btn btn-box-tool" data-widget="remove"><i class="fa fa-times"></i></button>
          </div>
        </div><!-- /.box-header -->
        <div class="box-body">
          <!-- Conversations are loaded here -->
          <div class="direct-chat-messages">
            <!-- Message. Default to the left -->
            <div class="direct-chat-msg">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-left">Alexander Pierce</span>
                <span class="direct-chat-timestamp pull-right">23 Jan 2:00 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user1-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                Is this template really for free? That's unbelievable!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->

            <!-- Message to the right -->
            <div class="direct-chat-msg right">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-right">Sarah Bullock</span>
                <span class="direct-chat-timestamp pull-left">23 Jan 2:05 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user3-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                You better believe it!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->
          </div><!--/.direct-chat-messages-->

          <!-- Contacts are loaded here -->
          <div class="direct-chat-contacts">
            <ul class="contacts-list">
              <li>
                <a href="#">
                  <img class="contacts-list-img" src="../dist/img/user1-128x128.jpg" alt="Contact Avatar">
                  <div class="contacts-list-info">
                    <span class="contacts-list-name">
                      Count Dracula
                      <small class="contacts-list-date pull-right">2/28/2015</small>
                    </span>
                    <span class="contacts-list-msg">How have you been? I was...</span>
                  </div><!-- /.contacts-list-info -->
                </a>
              </li><!-- End Contact Item -->
            </ul><!-- /.contatcts-list -->
          </div><!-- /.direct-chat-pane -->
        </div><!-- /.box-body -->
        <div class="box-footer">
          <form action="#" method="post">
            <div class="input-group">
              <input type="text" name="message" placeholder="Type Message ..." class="form-control">
              <span class="input-group-btn">
                <button type="button" class="btn btn-success btn-flat">Send</button>
              </span>
            </div>
          </form>
        </div><!-- /.box-footer-->
      </div><!--/.direct-chat -->
    </div><!-- /.col -->

    <div class="col-md-3">
      <!-- DIRECT CHAT WARNING -->
      <div class="box box-warning direct-chat direct-chat-warning">
        <div class="box-header with-border">
          <h3 class="box-title">Direct Chat</h3>
          <div class="box-tools pull-right">
            <span data-toggle="tooltip" title="3 New Messages" class="badge bg-yellow">3</span>
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            <button class="btn btn-box-tool" data-toggle="tooltip" title="Contacts" data-widget="chat-pane-toggle"><i class="fa fa-comments"></i></button>
            <button class="btn btn-box-tool" data-widget="remove"><i class="fa fa-times"></i></button>
          </div>
        </div><!-- /.box-header -->
        <div class="box-body">
          <!-- Conversations are loaded here -->
          <div class="direct-chat-messages">
            <!-- Message. Default to the left -->
            <div class="direct-chat-msg">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-left">Alexander Pierce</span>
                <span class="direct-chat-timestamp pull-right">23 Jan 2:00 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user1-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                Is this template really for free? That's unbelievable!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->

            <!-- Message to the right -->
            <div class="direct-chat-msg right">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-right">Sarah Bullock</span>
                <span class="direct-chat-timestamp pull-left">23 Jan 2:05 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user3-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                You better believe it!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->
          </div><!--/.direct-chat-messages-->

          <!-- Contacts are loaded here -->
          <div class="direct-chat-contacts">
            <ul class="contacts-list">
              <li>
                <a href="#">
                  <img class="contacts-list-img" src="../dist/img/user1-128x128.jpg" alt="Contact Avatar">
                  <div class="contacts-list-info">
                    <span class="contacts-list-name">
                      Count Dracula
                      <small class="contacts-list-date pull-right">2/28/2015</small>
                    </span>
                    <span class="contacts-list-msg">How have you been? I was...</span>
                  </div><!-- /.contacts-list-info -->
                </a>
              </li><!-- End Contact Item -->
            </ul><!-- /.contatcts-list -->
          </div><!-- /.direct-chat-pane -->
        </div><!-- /.box-body -->
        <div class="box-footer">
          <form action="#" method="post">
            <div class="input-group">
              <input type="text" name="message" placeholder="Type Message ..." class="form-control">
              <span class="input-group-btn">
                <button type="button" class="btn btn-warning btn-flat">Send</button>
              </span>
            </div>
          </form>
        </div><!-- /.box-footer-->
      </div><!--/.direct-chat -->
    </div><!-- /.col -->

    <div class="col-md-3">
      <!-- DIRECT CHAT DANGER -->
      <div class="box box-danger direct-chat direct-chat-danger">
        <div class="box-header with-border">
          <h3 class="box-title">Direct Chat</h3>
          <div class="box-tools pull-right">
            <span data-toggle="tooltip" title="3 New Messages" class="badge bg-red">3</span>
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            <button class="btn btn-box-tool" data-toggle="tooltip" title="Contacts" data-widget="chat-pane-toggle"><i class="fa fa-comments"></i></button>
            <button class="btn btn-box-tool" data-widget="remove"><i class="fa fa-times"></i></button>
          </div>
        </div><!-- /.box-header -->
        <div class="box-body">
          <!-- Conversations are loaded here -->
          <div class="direct-chat-messages">
            <!-- Message. Default to the left -->
            <div class="direct-chat-msg">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-left">Alexander Pierce</span>
                <span class="direct-chat-timestamp pull-right">23 Jan 2:00 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user1-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                Is this template really for free? That's unbelievable!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->

            <!-- Message to the right -->
            <div class="direct-chat-msg right">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-right">Sarah Bullock</span>
                <span class="direct-chat-timestamp pull-left">23 Jan 2:05 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user3-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                You better believe it!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->
          </div><!--/.direct-chat-messages-->

          <!-- Contacts are loaded here -->
          <div class="direct-chat-contacts">
            <ul class="contacts-list">
              <li>
                <a href="#">
                  <img class="contacts-list-img" src="../dist/img/user1-128x128.jpg" alt="Contact Avatar">
                  <div class="contacts-list-info">
                    <span class="contacts-list-name">
                      Count Dracula
                      <small class="contacts-list-date pull-right">2/28/2015</small>
                    </span>
                    <span class="contacts-list-msg">How have you been? I was...</span>
                  </div><!-- /.contacts-list-info -->
                </a>
              </li><!-- End Contact Item -->
            </ul><!-- /.contatcts-list -->
          </div><!-- /.direct-chat-pane -->
        </div><!-- /.box-body -->
        <div class="box-footer">
          <form action="#" method="post">
            <div class="input-group">
              <input type="text" name="message" placeholder="Type Message ..." class="form-control">
              <span class="input-group-btn">
                <button type="button" class="btn btn-danger btn-flat">Send</button>
              </span>
            </div>
          </form>
        </div><!-- /.box-footer-->
      </div><!--/.direct-chat -->
    </div><!-- /.col -->
  </div><!-- /.row -->
  <p class="lead">Direct Chat Markup</p>
  <pre class="prettyprint"><code class="html">
&LT;!-- Construct the box with style you want. Here we are using box-danger -->
&LT;!-- Then add the class direct-chat and choose the direct-chat-* contexual class -->
&LT;!-- The contextual class should match the box, so we are using direct-chat-danger -->
&LT;div class="box box-danger direct-chat direct-chat-danger">
  &LT;div class="box-header with-border">
    &LT;h3 class="box-title">Direct Chat&LT;/h3>
    &LT;div class="box-tools pull-right">
      &LT;span data-toggle="tooltip" title="3 New Messages" class="badge bg-red">3&LT;/span>
      &LT;button class="btn btn-box-tool" data-widget="collapse">&LT;i class="fa fa-minus">&LT;/i>&LT;/button>
      &LT;!-- In box-tools add this button if you intend to use the contacts pane -->
      &LT;button class="btn btn-box-tool" data-toggle="tooltip" title="Contacts" data-widget="chat-pane-toggle">&LT;i class="fa fa-comments">&LT;/i>&LT;/button>
      &LT;button class="btn btn-box-tool" data-widget="remove">&LT;i class="fa fa-times">&LT;/i>&LT;/button>
    &LT;/div>
  &LT;/div>&LT;!-- /.box-header -->
  &LT;div class="box-body">
    &LT;!-- Conversations are loaded here -->
    &LT;div class="direct-chat-messages">
      &LT;!-- Message. Default to the left -->
      &LT;div class="direct-chat-msg">
        &LT;div class="direct-chat-info clearfix">
          &LT;span class="direct-chat-name pull-left">Alexander Pierce&LT;/span>
          &LT;span class="direct-chat-timestamp pull-right">23 Jan 2:00 pm&LT;/span>
        &LT;/div>&LT;!-- /.direct-chat-info -->
        &LT;img class="direct-chat-img" src="../dist/img/user1-128x128.jpg" alt="message user image">&LT;!-- /.direct-chat-img -->
        &LT;div class="direct-chat-text">
          Is this template really for free? That's unbelievable!
        &LT;/div>&LT;!-- /.direct-chat-text -->
      &LT;/div>&LT;!-- /.direct-chat-msg -->

      &LT;!-- Message to the right -->
      &LT;div class="direct-chat-msg right">
        &LT;div class="direct-chat-info clearfix">
          &LT;span class="direct-chat-name pull-right">Sarah Bullock&LT;/span>
          &LT;span class="direct-chat-timestamp pull-left">23 Jan 2:05 pm&LT;/span>
        &LT;/div>&LT;!-- /.direct-chat-info -->
        &LT;img class="direct-chat-img" src="../dist/img/user3-128x128.jpg" alt="message user image">&LT;!-- /.direct-chat-img -->
        &LT;div class="direct-chat-text">
          You better believe it!
        &LT;/div>&LT;!-- /.direct-chat-text -->
      &LT;/div>&LT;!-- /.direct-chat-msg -->
    &LT;/div>&LT;!--/.direct-chat-messages-->

    &LT;!-- Contacts are loaded here -->
    &LT;div class="direct-chat-contacts">
      &LT;ul class="contacts-list">
        &LT;li>
          &LT;a href="#">
            &LT;img class="contacts-list-img" src="../dist/img/user1-128x128.jpg" alt="Contact Avatar">
            &LT;div class="contacts-list-info">
              &LT;span class="contacts-list-name">
                Count Dracula
                &LT;small class="contacts-list-date pull-right">2/28/2015&LT;/small>
              &LT;/span>
              &LT;span class="contacts-list-msg">How have you been? I was...&LT;/span>
            &LT;/div>&LT;!-- /.contacts-list-info -->
          &LT;/a>
        &LT;/li>&LT;!-- End Contact Item -->
      &LT;/ul>&LT;!-- /.contatcts-list -->
    &LT;/div>&LT;!-- /.direct-chat-pane -->
  &LT;/div>&LT;!-- /.box-body -->
  &LT;div class="box-footer">
    &LT;div class="input-group">
      &LT;input type="text" name="message" placeholder="Type Message ..." class="form-control">
      &LT;span class="input-group-btn">
        &LT;button type="button" class="btn btn-danger btn-flat">Send&LT;/button>
      &LT;/span>
    &LT;/div>
  &LT;/div>&LT;!-- /.box-footer-->
&LT;/div>&LT;!--/.direct-chat -->
</code></pre>

  <p>Of course you can use direct chat with a solid box by adding the class <code>solid-box</code> to the box. Here are a couple of examples:</p>

  <!-- Direct Chat With Solid Boxes -->
  <div class="row">
    <div class="col-md-6">
      <!-- DIRECT CHAT WARNING -->
      <div class="box box-primary box-solid direct-chat direct-chat-primary">
        <div class="box-header">
          <h3 class="box-title">Direct Chat in a Solid Box</h3>
          <div class="box-tools pull-right">
            <span data-toggle="tooltip" title="3 New Messages" class="badge bg-light-blue">3</span>
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            <button class="btn btn-box-tool" data-toggle="tooltip" title="Contacts" data-widget="chat-pane-toggle"><i class="fa fa-comments"></i></button>
            <button class="btn btn-box-tool" data-widget="remove"><i class="fa fa-times"></i></button>
          </div>
        </div><!-- /.box-header -->
        <div class="box-body">
          <!-- Conversations are loaded here -->
          <div class="direct-chat-messages">
            <!-- Message. Default to the left -->
            <div class="direct-chat-msg">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-left">Alexander Pierce</span>
                <span class="direct-chat-timestamp pull-right">23 Jan 2:00 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user1-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                Is this template really for free? That's unbelievable!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->

            <!-- Message to the right -->
            <div class="direct-chat-msg right">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-right">Sarah Bullock</span>
                <span class="direct-chat-timestamp pull-left">23 Jan 2:05 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user3-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                You better believe it!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->
          </div><!--/.direct-chat-messages-->

          <!-- Contacts are loaded here -->
          <div class="direct-chat-contacts">
            <ul class="contacts-list">
              <li>
                <a href="#">
                  <img class="contacts-list-img" src="../dist/img/user1-128x128.jpg" alt="Contact Avatar">
                  <div class="contacts-list-info">
                    <span class="contacts-list-name">
                      Count Dracula
                      <small class="contacts-list-date pull-right">2/28/2015</small>
                    </span>
                    <span class="contacts-list-msg">How have you been? I was...</span>
                  </div><!-- /.contacts-list-info -->
                </a>
              </li><!-- End Contact Item -->
            </ul><!-- /.contatcts-list -->
          </div><!-- /.direct-chat-pane -->
        </div><!-- /.box-body -->
        <div class="box-footer">
          <form action="#" method="post">
            <div class="input-group">
              <input type="text" name="message" placeholder="Type Message ..." class="form-control">
              <span class="input-group-btn">
                <button type="button" class="btn btn-primary btn-flat">Send</button>
              </span>
            </div>
          </form>
        </div><!-- /.box-footer-->
      </div><!--/.direct-chat -->
    </div><!-- /.col -->

    <div class="col-md-6">
      <!-- DIRECT CHAT DANGER -->
      <div class="box box-info box-solid direct-chat direct-chat-info">
        <div class="box-header">
          <h3 class="box-title">Direct Chat in a Solid Box</h3>
          <div class="box-tools pull-right">
            <span data-toggle="tooltip" title="3 New Messages" class="badge bg-aqua">3</span>
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            <button class="btn btn-box-tool" data-toggle="tooltip" title="Contacts" data-widget="chat-pane-toggle"><i class="fa fa-comments"></i></button>
            <button class="btn btn-box-tool" data-widget="remove"><i class="fa fa-times"></i></button>
          </div>
        </div><!-- /.box-header -->
        <div class="box-body">
          <!-- Conversations are loaded here -->
          <div class="direct-chat-messages">
            <!-- Message. Default to the left -->
            <div class="direct-chat-msg">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-left">Alexander Pierce</span>
                <span class="direct-chat-timestamp pull-right">23 Jan 2:00 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user1-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                Is this template really for free? That's unbelievable!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->

            <!-- Message to the right -->
            <div class="direct-chat-msg right">
              <div class="direct-chat-info clearfix">
                <span class="direct-chat-name pull-right">Sarah Bullock</span>
                <span class="direct-chat-timestamp pull-left">23 Jan 2:05 pm</span>
              </div><!-- /.direct-chat-info -->
              <img class="direct-chat-img" src="../dist/img/user3-128x128.jpg" alt="message user image"><!-- /.direct-chat-img -->
              <div class="direct-chat-text">
                You better believe it!
              </div><!-- /.direct-chat-text -->
            </div><!-- /.direct-chat-msg -->
          </div><!--/.direct-chat-messages-->

          <!-- Contacts are loaded here -->
          <div class="direct-chat-contacts">
            <ul class="contacts-list">
              <li>
                <a href="#">
                  <img class="contacts-list-img" src="../dist/img/user1-128x128.jpg" alt="Contact Avatar">
                  <div class="contacts-list-info">
                    <span class="contacts-list-name">
                      Count Dracula
                      <small class="contacts-list-date pull-right">2/28/2015</small>
                    </span>
                    <span class="contacts-list-msg">How have you been? I was...</span>
                  </div><!-- /.contacts-list-info -->
                </a>
              </li><!-- End Contact Item -->
            </ul><!-- /.contatcts-list -->
          </div><!-- /.direct-chat-pane -->
        </div><!-- /.box-body -->
        <div class="box-footer">
          <form action="#" method="post">
            <div class="input-group">
              <input type="text" name="message" placeholder="Type Message ..." class="form-control">
              <span class="input-group-btn">
                <button type="button" class="btn btn-info btn-flat">Send</button>
              </span>
            </div>
          </form>
        </div><!-- /.box-footer-->
      </div><!--/.direct-chat -->
    </div><!-- /.col -->
  </div><!-- /.row -->
</section>


<!-- ============================================================= -->

<section id="plugins">
  <h2 class="page-header"><a href="#plugins">Plugins</a></h2>
  <p class="lead">AdminLTE makes use of the following plugins. For documentation, updates or license information, please visit the provided links.</p>
  <div class="row bring-up">
    <div class="col-sm-3">
      <ul class="list-unstyled">
        <li><h4>Charts</h4></li>
        <li><a href="http://www.chartjs.org/" target="_blank">ChartJS</a></li>
        <li><a href="http://www.flotcharts.org/" target="_blank">Flot</a></li>
        <li><a href="http://morrisjs.github.io/morris.js/" target="_blank">Morris.js</a></li>
        <li><a href="http://omnipotent.net/jquery.sparkline/" target="_blank">Sparkline</a></li>
      </ul>
    </div><!-- /.col -->
    <div class="col-sm-3">
      <ul class="list-unstyled">
        <li><h4>Form Elements</h4></li>
        <li><a href="https://github.com/seiyria/bootstrap-slider/">Bootstrap Slider</a></li>
        <li><a href="http://ionden.com/a/plugins/ion.rangeSlider/en.html" target="_blank">Ion Slider</a></li>
        <li><a href="http://bootstrap-datepicker.readthedocs.org/" target="_blank">Date Picker</a></li>
        <li><a href="http://www.daterangepicker.com/" target="_blank">Date Range Picker</a></li>
        <li><a href="http://mjolnic.com/bootstrap-colorpicker/" target="_blank">Color Picker</a></li>
        <li><a href="https://github.com/jdewit/bootstrap-timepicker/" target="_blank">Time Picker</a></li>
        <li><a href="http://fronteed.com/iCheck/" target="_blank">iCheck</a></li>
        <li><a href="https://github.com/RobinHerbots/jquery.inputmask/" target="_blank">Input Mask</a></li>
      </ul>
    </div><!-- /.col -->
    <div class="col-sm-3">
      <ul class="list-unstyled">
        <li><h4>Editors</h4></li>
        <li><a href="https://github.com/bootstrap-wysiwyg/bootstrap3-wysiwyg/" target="_blank">Bootstrap WYSIHTML5</a></li>
        <li><a href="http://ckeditor.com/" target="_blank">CK Editor</a></li>
      </ul>
    </div><!-- /. col -->
    <div class="col-sm-3">
      <ul class="list-unstyled">
        <li><h4>Other</h4></li>
        <li><a href="https://datatables.net/examples/styling/bootstrap.html" target="_blank">DataTables</a></li>
        <li><a href="http://fullcalendar.io/" target="_blank">Full Calendar</a></li>
        <li><a href="http://jqueryui.com/" target="_blank">jQuery UI</a></li>
        <li><a href="http://anthonyterrien.com/knob/" target="_blank">jQuery Knob</a></li>
        <li><a href="http://jvectormap.com/" target="_blank">jVector Map</a></li>
        <li><a href="http://rocha.la/jQuery-slimScroll/" target="_blank">Slim Scroll</a></li>
        <li><a href="http://github.hubspot.com/pace/docs/welcome/" target="_blank">Pace</a></li>
      </ul>
    </div><!-- /.col -->
  </div><!-- /.row -->
</section>


<!-- ============================================================= -->

<section id="browsers">
  <h2 class="page-header"><a href="#browsers">Browser Support</a></h2>
  <p class="lead">AdminLTE supports the following browsers:</p>
  <ul>
    <li>IE9+</li>
    <li>Firefox (latest)</li>
    <li>Safari (latest)</li>
    <li>Chrome (latest)</li>
    <li>Opera (latest)</li>
  </ul>
  <p><b>Note:</b> IE9 does not support transitions or animations. The template will function properly but it won't use animations/transitions on IE9.</p>
</section>


<!-- ============================================================= -->

<section id="upgrade">
  <h2 class="page-header"><a href="#upgrade">Upgrade Guide</a></h2>
  <p class="lead">To upgrade from version 1.x to the lateset version, follow this guide.</p>
  <h3>New Files</h3>
  <p>Make sure you update all CSS and JS files that are related to AdminLTE. Otherwise, the layout will not
    function properly. Most important files are AdminLTE.css, skins CSS files, and app.js.</p>
  <h3>Layout Changes</h3>
  <ol>
    <li>The .wrapper div must be placed immediately after the body tag rather than after the header</li>
    <li>Change the .header div to .main-header <code>&LT;div class="main-header"></code></li>
    <li>Change the .right-side class to .content-wrapper <code>&LT;div class="content-wrapper"></code></li>
    <li>Change the .left-side class to .main-sidebar <code>&LT;div class="main-sidebar"></code></li>
    <li>In the navbar, change .navbar-right to .navbar-custom-menu <code>&LT;div class="navbar-custom-menu"></code></li>
  </ol>
  <h3>Navbar Custom Dropdown Menus</h3>
  <ol>
    <li>The icons in the notification menu do not need bg-* classes. They should be replaced with contextual text color class such as text-aqua or text-red.</li>
  </ol>
  <h3>Login, Registration and Lockscreen Pages</h3>
  <p>There are major changes to the HTML markup and style to these pages. The best way is to copy the page's code and customize it.</p>
  <p>And you should be set to go!</p>
  <h3>Mailbox</h3>
  <p>Mailbox got an upgrade to include three different views. The views are inbox, read mail, and compose new email. To use these views,
    you should use the provided HTML files in the pages/mailbox folder.</p>
  <p><b class="text-red">Note:</b> the old mailbox layout has been deprecated in favor of the new one and will be removed by the next release.</p>
</section>


<!-- ============================================================= -->

<section id="implementations">
  <h2 class="page-header"><a href="#implementations">Implementations</a></h2>
  <p class="lead">Thanks to many of AdminLTE users, there are multiple implementations of the template
  for easy integration with back-end frameworks. The following are some of them:</p>

  <ul>
    <li><a href="https://github.com/mmdsharifi/AdminLTE-RTL">Persian RTL</a> by <a href="https://github.com/mmdsharifi">Mohammad Sharifi</a></li>
    <li><a href="https://github.com/dmstr/yii2-adminlte-asset" target="_blank">Yii 2</a> by <a href="https://github.com/schmunk42" target="_blank">Tobias Munk</a></li>
    <li><a href="https://github.com/yajra/laravel-admin-template" target="_blank">Laravel</a> by <a href="https://github.com/yajra" target="_blank">Arjay Angeles</a></li>
    <li><a href="https://github.com/acacha/adminlte-laravel" target="_blank">Laravel 5 package</a> by <a href="https://github.com/acacha" target="_blank">Sergi Tur Badenas</a></li>
    <li><a href="https://github.com/avanzu/AdminThemeBundle" target="_blank">Symfony</a> by <a href="https://github.com/avanzu" target="_blank">Marc Bach</a></li>
    <li>Rails gems: <a href="https://github.com/nicolas-besnard/adminlte2-rails" target="_blank">adminlte2-rails</a> by <a href="https://github.com/nicolas-besnard" target="_blank">Nicolas Besnard</a> and <a href="https://github.com/racketlogger/lte-rails" target="_blank">lte-rails</a> (using AdminLTE sources) by <a href="https://github.com/racketlogger" target="_blank">Carlos at RacketLogger</a></li>
  </ul>

  <p><b class="text-red">Note:</b> these implementations are not supported by Almsaeed Studio. However,
    they do provide a good example of how to integrate AdminLTE into different frameworks. For the latest release
    of AdminLTE, please visit our <a href="https://github.com/almasaeed2010/AdminLTE">repository</a> or <a href="https://almsaeedstudio.com">website</a></p>
</section>


<!-- ============================================================= -->

<section id="faq">
  <h2 class="page-header"><a href="#faq">FAQ</a></h2>
  <h3>Can AdminLTE be used with Wordpress?</h3>
  <p class="lead">AdminLTE is an HTML template that can be used for any purpose. However, it is not made to be easily installed on Wordpress. It will require some effort and enough knowledge of the Wordpress script to do so.</p>

  <h3>Is there an integration guide for PHP frameworks such as Yii or Symfony?</h3>
  <p class="lead">Short answer, no. However, there are forks and tutorials around the web that provide info on how to integrate with many different frameworks. There are even versions of AdminLTE that are integrated with jQuery ajax, AngularJS and/or MVC5 ASP .NET.</p>

  <h3>How do I get notified of new AdminLTE versions?</h3>
  <p class="lead">The best option is to subscribe to our mailing list using the <a href="http://almsaeedstudio.com/#subscribe">subscription form on Almsaeed Studio</a>.
    If that's not appealing to you, you may watch the <a href="https://github.com/almasaeed2010/AdminLTE">repository on Github</a> or visit <a href="http://almsaeedstudio.com">Almsaeed Studio</a> every now and then for updates and announcements.</p>
</section>


<!-- ============================================================= -->

<section id="license">
  <h2 class="page-header"><a href="#license">License</a></h2>
  <h3>AdminLTE</h3>
  <p class="lead">
    AdminLTE is an open source project that is licensed under the <a href="http://opensource.org/licenses/MIT">MIT license</a>.
    This allows you to do pretty much anything you want as long as you include
    the copyright in "all copies or substantial portions of the Software."
    Attribution is not required (though very much appreciated).
  </p>
</section>


        </div><!-- /.content -->
      </div><!-- /.content-wrapper -->

      <footer class="main-footer">
        <div class="pull-right hidden-xs">
          <b>Version</b> 2.3.3
        </div>
        <strong>Copyright &copy; 2014-2015 <a href="http://almsaeedstudio.com">Almsaeed Studio</a>.</strong> All rights reserved.
      </footer>

      <!-- Control Sidebar -->
      <aside class="control-sidebar control-sidebar-dark">
        <!-- Create the tabs -->
        <div class="pad">
          This is an example of the control sidebar.
        </div>
      </aside><!-- /.control-sidebar -->
      <!-- Add the sidebar's background. This div must be placed
           immediately after the control sidebar -->
      <div class="control-sidebar-bg"></div>

    </div><!-- ./wrapper -->

    <!-- jQuery 2.2.0 -->
    <script src="../plugins/jQuery/jQuery-2.2.0.min.js"></script>
    <!-- Bootstrap 3.3.6 -->
    <script src="../bootstrap/js/bootstrap.min.js"></script>
    <!-- FastClick -->
    <script src="../plugins/fastclick/fastclick.min.js"></script>
    <!-- AdminLTE App -->
    <script src="../dist/js/app.min.js"></script>
    <!-- SlimScroll 1.3.0 -->
    <script src="../plugins/slimScroll/jquery.slimscroll.min.js"></script>
    <script src="https://google-code-prettify.googlecode.com/svn/loader/run_prettify.js"></script>
    <script src="docs.js"></script>
  </body>
</html>
