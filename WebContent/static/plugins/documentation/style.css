/*
 * Documentation specific stylesheet
 */
.content-wrapper p {
  padding: 0 10px;
  font-size: 16px;
  position: relative;
  z-index: 30;
}
.bring-up {
  position: relative;
  z-index: 30;
}
.nth-2-center > tbody > tr > td:last-of-type {
  text-align: center!important;
}
.content {
  font-size: 16px;
  z-index: 500;
}

#components > h3 {
  font-size: 25px;
  color: #000;
}

#components > h4 {
  font-size: 20px;
  color: #000;
}
ul {
  margin-bottom: 20px;
}
.page-header {
  /*border-bottom: 1px solid #ddd;        */
  margin: 20px 0 10px 0!important;
  position: relative;
  z-index: 1;
  font-size: 30px;
}
.page-header span,
.page-header a {
  z-index: 5;
  display: block;
  background-color: #ecf0f5;
  color: #000;
}
.page-header span::before,
.page-header a::before {
  content: '#';
  font-size: 25px;
  margin-right: 10px;
  color: #3c8dbc;
}
.page-header:before,
#components > h3:before {
  display: block;
  content: " ";
  margin-top: -60px;
  height: 60px;
  visibility: hidden;
  z-index: -10;
}

.lead {
  font-size: 18px;
  font-weight: 400;
}
.eg{
  position: absolute;
  top: 0;
  left: 0;
  display: inline-block;
  background: #d2d6de;
  padding: 5px;
  border-bottom-right-radius: 3px;
  border-top-left-radius: 3px;
  border-bottom: 1px solid #d2d6dc;
  border-right: 1px solid #d2d6dc;
}
.eg + * {
  margin-top: 30px;
}
.content {
  padding: 10px 25px;
}
.hierarchy {
  background: #333;
  color: #fff;
}
.plugins-list li {
  width: 50%;
  float: left;
}
pre {
  border: none;
}

.sidebar {
  margin-top: 0;
  padding-top: 0!important;
}
.box .main-header {
  z-index: 1000;
  position: relative;
}
.treeview .nav li a:hover,
.treeview .nav li a:active {
  background: transparent;
}
p {
  padding: 0!important;
}
/* Hemisu Light */
/* Original theme - http://noahfrederick.com/vim-color-scheme-hemisu/ */
pre.prettyprint {
  background: white;
  font-family: Menlo, 'Bitstream Vera Sans Mono', 'DejaVu Sans Mono', Monaco, Consolas, monospace;
  font-size: 12px;
  line-height: 1.5;
  border: 1px solid #dedede!important;
  padding: 10px;
  max-height: 300px;
  width: auto;
  overflow: auto!important;
}
pre.prettyprint > code {
  width: auto;
  overflow: auto!important;
}

.pln {
  color: #111111;
}

@media screen {
  .str {
    color: #739200;
  }

  .kwd {
    color: #739200;
  }

  .com {
    color: #999999;
  }

  .typ {
    color: #ff0055;
  }

  .lit {
    color: #538192;
  }

  .pun {
    color: #111111;
  }

  .opn {
    color: #111111;
  }

  .clo {
    color: #111111;
  }

  .tag {
    color: #111111;
  }

  .atn {
    color: #739200;
  }

  .atv {
    color: #ff0055;
  }

  .dec {
    color: #111111;
  }

  .var {
    color: #111111;
  }

  .fun {
    color: #538192;
  }
}
@media print, projection {
  .str {
    color: #006600;
  }

  .kwd {
    color: #006;
    font-weight: bold;
  }

  .com {
    color: #600;
    font-style: italic;
  }

  .typ {
    color: #404;
    font-weight: bold;
  }

  .lit {
    color: #004444;
  }

  .pun, .opn, .clo {
    color: #444400;
  }

  .tag {
    color: #006;
    font-weight: bold;
  }

  .atn {
    color: #440044;
  }

  .atv {
    color: #006600;
  }
}
/* Specify class=linenums on a pre to get line numbering */
ol.linenums {
  margin-top: 0;
  margin-bottom: 0;
}

/* IE indents via margin-left */
li.L0,
li.L1,
li.L2,
li.L3,
li.L4,
li.L5,
li.L6,
li.L7,
li.L8,
li.L9 {
  /* */
}

/* Alternate shading for lines */
li.L1,
li.L3,
li.L5,
li.L7,
li.L9 {
  /* */
}
