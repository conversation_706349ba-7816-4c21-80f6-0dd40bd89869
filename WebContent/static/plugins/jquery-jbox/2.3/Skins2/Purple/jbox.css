@charset "utf-8";

*:focus {outline: none;}
/* fade */
.jbox-fade{background-color:#000000;}
/* drag */
.jbox-drag{border:1px dashed #8e0674;}
/* jbox */
div.jbox {padding:0px;border:none;font-size:12px;}
/* border */
div.jbox .jbox-border{background: none repeat scroll 0 0 #000000;filter:alpha(opacity=20);-moz-opacity:0.2;opacity:0.2;}
/* container */
div.jbox .jbox-container{background-color:#ffffff;border:1px solid #999999;}
/* title-panel */
div.jbox .jbox-title-panel{background: #8e0674;background: -webkit-gradient(linear, left top, left bottom, from(#9a1f82), to(#72055d));background: -moz-linear-gradient(top,  #9a1f82,  #72055d);filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#9a1f82', endColorstr='#72055d');border-bottom:1px solid #999999;}
div.jbox .jbox-title{font-weight:bold;color:#ffffff;}
div.jbox .jbox-title-icon{background:url(images/jbox-title-icon.gif) no-repeat scroll 3px 5px transparent;}
div.jbox .jbox-close,div.jbox .jbox-close-hover{background:url(images/jbox-close1.gif) no-repeat scroll 0px 0px transparent;}
div.jbox .jbox-close-hover{background-position:-16px 0;}
/* content */
div.jbox .jbox-content{min-height:24px;line-height:18px;color:#444444;}
div.jbox .jbox-content-loading{background-color:#E6E6E6;}
div.jbox .jbox-content-loading-image{background:url(images/jbox-content-loading.gif) no-repeat bottom center;}
/* button-panel */
div.jbox .jbox-button-panel{border-top:1px solid #CCCCCC;background-color: #EEEEEE;}
div.jbox .jbox-bottom-text{text-indent:10px;color:#444444;}
div.jbox .jbox-button{background: #8e0674;background: -webkit-gradient(linear, left top, left bottom, from(#9a1f82), to(#72055d));background: -moz-linear-gradient(top,  #9a1f82,  #72055d);filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#9a1f82', endColorstr='#72055d');border:#47033a 1px solid;color:#fff;border-radius:3px 3px 3px 3px;margin:1px 7px 0px 0px;height:22px;cursor:default;}
div.jbox .jbox-button-hover{background: #8e0674;background: -webkit-gradient(linear, left top, left bottom, from(#8e0674), to(#550446));background: -moz-linear-gradient(top,  #8e0674,  #550446);filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#8e0674', endColorstr='#550446');}
div.jbox .jbox-button-active{background: -webkit-gradient(linear, left top, left bottom, from(#550446), to(#8e0674));background: -moz-linear-gradient(top,  #550446,  #8e0674);filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#550446', endColorstr='#8e0674');}
div.jbox-warning .jbox .jbox-button-panel{background-color: #FFFFFF;}
/* tip-color */
div.jbox .jbox-tip-color{background: #8e0674;background: -webkit-gradient(linear, left top, left bottom, from(#8e0674), to(#550446));background: -moz-linear-gradient(top,  #8e0674,  #550446);filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#8e0674', endColorstr='#550446');border-color:#47033a;border-radius:3px 3px 3px 3px;color:#fff;}
/* icons */
div.jbox span.jbox-icon{background:url(images/jbox-icons.png) no-repeat scroll 0 0 transparent;_background:url(images/jbox-icons-ie6.gif) no-repeat scroll 0 0 transparent;}
div.jbox span.jbox-icon-info {background-position:0 0;}
div.jbox span.jbox-icon-question {background-position:-36px 0;}
div.jbox span.jbox-icon-success {background-position:-72px 0;}
div.jbox span.jbox-icon-warning {background-position:-108px 0;}
div.jbox span.jbox-icon-error {background-position:-144px 0;}
div.jbox span.jbox-icon-none {display: none; overflow:hidden;}
div.jbox span.jbox-icon-loading {background:url(images/jbox-loading1.gif) no-repeat scroll 0 0 transparent;}