/*
Description: Magula style for highligh.js
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Website: http://rukeba.com/
Version: 1.0
Date: 2009-01-03
Music: Aphex Twin / Xtal
*/

pre { padding:1em; background-color:#eee; border:solid 1px #F3F3F3; -moz-border-radius: 4px; -webkit-border-radius: 4px; border-radius:4px; }
pre code, pre .ruby .subst, pre .lisp .title { color: black; }
pre .string, pre .title, pre .parent, pre .tag .attribute .value, pre .rules .value, pre .rules .value .number, pre .preprocessor, pre .ruby .symbol, pre .ruby .symbol .string, pre .ruby .symbol .keyword, pre .ruby .symbol .keymethods, pre .instancevar, pre .aggregate, pre .template_tag, pre .django .variable, pre .smalltalk .class, pre .addition, pre .flow, pre .stream, pre .bash .variable, pre .apache .cbracket { color: #008200; }
pre .comment, pre .annotation, pre .template_comment, pre .diff .header, pre .chunk { color: #777; }
pre .number, pre .date, pre .regexp, pre .literal, pre .smalltalk .symbol, pre .smalltalk .char, pre .change, pre .tex .special { color: #B50000; }
pre .label, pre .javadoc, pre .ruby .string, pre .decorator, pre .filter .argument, pre .localvars, pre .array, pre .attr_selector, pre .pseudo, pre .pi, pre .doctype, pre .deletion, pre .envvar, pre .shebang, pre .apache .sqbracket, pre .nginx .built_in, pre .tex .formula { color: #00e; }
pre .keyword, pre .id, pre .phpdoc, pre .title, pre .built_in, pre .aggregate, pre .smalltalk .class, pre .winutils, pre .bash .variable, pre .apache .tag, pre .xml .tag, pre .xml .title, pre .tex .command { font-weight: bold; color: navy; }
pre .nginx .built_in { font-weight: normal; }
pre .html .css, pre .html .javascript, pre .html .vbscript, pre .tex .formula { opacity: 0.5; }
/* --- */
pre .apache .tag { font-weight: bold; color: blue; }
