<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() +"://" + request.getServerName() + ":" +request.getServerPort() + path +"/";
%>
<!DOCTYPE html>
<html>
<head>
    <base href="<%=path %>/" />
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <%@include file="../../jsp/indexHead.jsp"%>
    <!-- <link href="static/zui-1.8.1/lib/chosen/chosen.min.css" rel="stylesheet"> -->
    <title>设备档案</title>
    <style>
        .tdCon{padding-left: 0px !important;}
        #leftCon .ztreeZtreeBackground{background-color:rgba(235, 235, 235, 0.8);padding-left:5px;}
        .btn-group>button:last-child{border-bottom-right-radius: 4px;border-top-right-radius: 4px;}
        #rightCon .tab-content{padding:15px 5px 0px;}
        /* #nav_1,#tab1{display: none;} */
        .navBtns{padding-bottom: 10px;}
        tbody tr:hover{cursor: pointer;}

        #rightCon .tdCon>div:last-child{height: calc(100% - 38px) !important;}
        #rightCon .tab-content>div:first-child{height: calc(100% - 72px);}
        #rightCon .tab-content>div:first-child .tabConts,#rightCon .tabConts>div:nth-child(2){height: calc(100% - 42px);}
        /* 表格 */
        #rightCon .tabConts .table,#rightCon .tabConts .table tbody tr:first-child td{border-top-width: 0px;}
        #rightCon .tabConts .table a,#rightCon .tabConts .table th{font-size: 15px;}
        #rightCon .tabConts .table td{font-size: 14px;}
        #rightCon .tabConts .table tr td{height: 32px;line-height: 32px;vertical-align: middle;word-break:break-all;}
        #rightCon .tabConts .table input[type="checkbox"],#rightCon .tabConts .table input[type="radio"]{width:15px;height: 15px;
            margin-top: 0px;vertical-align: middle;}
        #rightCon .tabConts .table label,#rightCon .tabConts .table{margin-bottom: 0px;}
        #rightCon .table-bordered{border-width: 0px !important;}
        #treese{border: 1px solid #EEC211;height:calc(100% - 80px);width: 35%;background-color: #eaeaea;overflow:auto;}
        
#fils .table>thead>tr{height: 32px;}
#fils .table>thead>tr>th{background-color: #f1f1f1;color: #808080;font-weight: bold;font-size: 13px;}
#fils .table tbody{font-size: 14px;}
#fils .table tbody tr:hover{cursor: default;}
#fils .table .addText{wcolor: #145ccd;text-align: center;}
#fils .table .addText:hover{cursor: pointer;}
#fils .table .xuLo{text-align: center;}
#fils .table .laTd{padding-right: 0px;}
#fils .table .laTd i,#fils .table .laTd a:hover i{cursor: pointer;}
#fils .table tbody .text{font-size: 15px;background-color: #f1f1f1;padding: 5px;border-radius:3px;}
#fileTb{min-height: 50px;}
.device-mobile #mode2 .modal-dialog{width: 100% !important;}
#fils .hide{display: none;}
#fils .table td{vertical-align: middle;}
#fils .table .pos{position: relative;display: inline-block;float: left;}
#fils .table .pos span{position: absolute;top: -11px;right: -12px;color: #ea644a;z-index: 2;}
#fils .table .pos span:hover{cursor: pointer;color: #d25e47;}
#fils .table .pos span i{font-size: 18px;}
#fils .table .conts{display: none;}
#fils .table .fils{
   overflow:hidden; 
   text-overflow:ellipsis;}
   
   input[name="defNodeAttr"]{
   	padding-left: 20px !important;
   }
   .input-control-icon-left {
    left: 8px  !important; 
}
button.disabled{cursor:not-allowed;pointer-events: auto;}
    </style>
</head>
<body id="cur" class="container-fluid clears">
<div  class="row">
    <div id="rightCon">
        <div class="row headCon clears" >
            <p class="text-left clears" ></p>
        </div>
        <div class="row tdCon clears">
            <div class="row">
            	<div style="margin-left: 1%;">
            	<input id="btnSize" type="hidden" value="${fn:length(permitBtn)}">
 					 <c:forEach var="pbtn" items="${permitBtn}"    varStatus="status">
											<c:if test="${permitBtn[status.index].btnFun=='holds(0)'}">
												<button onclick="${permitBtn[status.index].btnFun}"  class="btn btn-success" ><i class="icon-plus"></i>${permitBtn[status.index].name}</button>
											</c:if>
											<c:if test="${permitBtn[status.index].btnFun=='holds(1)'}">
												<button onclick="${permitBtn[status.index].btnFun}"  class="btn btn-warning" ><i class="icon-plus"></i>${permitBtn[status.index].name}</button>
											</c:if>
											<c:if test="${permitBtn[status.index].btnFun=='treeDel()'}">
												<button onclick="${permitBtn[status.index].btnFun}"  class="btn btn-danger" ><i class="icon icon-remove"></i>${permitBtn[status.index].name}</button>
											</c:if>
					</c:forEach>           	
                </div>
                <ul id="navCon" class="nav nav-tabs "></ul>
            </div>
        </div>
        <div id="zidingyi" style="display:none;width: 100%;height:100%">
            <div style="float:right;width:65%;height:100%" id="fils">
           
                <form id="sellUserForm" class="form-horizontal" style="margin-top: 2%;height:calc(95% - 100px);overflow-x:hidden;overflow-y:auto;"  method="post" action="">
					<input type="hidden" name="isTypeChange" value="0" >
					<input type="hidden" name="deaNodeId" id="deaNodeId">
					<input type="hidden" name="deaIsvalid" id="deaIsvalid" value="0" />
					<input type="hidden" name="detNodeId" id="detNodeId"  value="" />
					<input type="hidden" name="detNodePath" id="detNodePath"  value="" />
                        			<div class="form-group">
										<div class="switch text-left col-xs-3 col-sm-2" style="margin-left: 5%;">
										  <input  id='isVal' type="checkbox" title="是否启用" value="0"/>
										  <label style="font-weight: bold;padding-left: 40% !important;" >启用</label>
										</div>
										<c:forEach var="pbtn" items="${permitBtn}"    varStatus="status">
											<c:if test="${permitBtn[status.index].btnFun=='edit()'}">
												<button onclick="${permitBtn[status.index].btnFun}" type="button" class="btn btn-success" style="position: absolute;right: 20px;"><i class="fa fa-floppy-o"></i> ${permitBtn[status.index].name}</button>
											</c:if>
										</c:forEach>
										 <!-- <button type="button" class="btn btn-success" onclick="edit();" style="position: absolute;right: 20px;"><i class="fa fa-floppy-o"></i> 保存</button>  -->
									</div>
									<div class="form-group">
									    <label for="deaNodeName" class="col-sm-2 control-label required">设备名称</label>
									    <div class="col-sm-9">
									      <input type="text" name="deaNodeName" maxlength="32"  class="form-control xiaoY" id="deaNodeName" placeholder="设备名称" />
									    </div>
									</div>
									<div class="form-group">
				                        <label class="col-sm-2 control-label required" for="deaNodeRelationDcs">关联站点</label>
				                        <div class="col-sm-9">
				                            <select id="deaNodeRelationDcs" name="deaNodeRelationDcs" class="form-control selectpicker xiaoY" data-live-search="true" data-size="6">
				                            </select>
				                        </div>
				                    </div>
				                    <div class="form-group">
				                        <label class="col-sm-2 control-label " for="deaNodeRelationDev">关联回路</label>
				                        <div class="col-sm-9">
				                            <select id="deaNodeRelationDev" name="deaNodeRelationDev" class="form-control selectpicker" data-live-search="true" data-size="6">
				                            </select>
				                        </div>
				                    </div>
				                    <div class="form-group" id="detNodeIdDiv" style="display:none;">
				                        <label class="col-sm-2 control-label " for="detNodeId">设备类型</label>
				                        <div class="col-sm-9">
				                            <div id="chosedobj" >
							          		</div>
				                        </div>
				                    </div>
									<div id="checkAttr" style="display:none;">
										
										
									</div>
									<!-- <div class="form-group">
									    <label for="dclName" class="col-sm-2 control-label">设备图片</label>
									    <div class="col-sm-9">
									      <input type="file" name="dclName" maxlength="32"  class="form-control xiaoY" id="dclName" placeholder="" />
									    </div>
									</div> -->
									<div  id="deaQrcode" style="display:none;margin-left: 40%;" >
				                        <div class="row">
				                            <img id="deaQrcodeUrl" style="width:200px;height:200px"  alt="二维码">
				                        </div>
				                        <div class="row" style="margin-left: 6%;">
				                             <label id="deaQrcodeId" width="150px"></label>
				                        </div>
				                    </div>
									
					</form>

            </div>

            <div id="treese" class="col-sm-2">
                <div class="row headCon" >
                    <div class="input-control search-box search-box-circle has-icon-left has-icon-right" id="key1">
                        <input id="key" type="search" class="form-control search-input" placeholder="搜索">
                        <label for="key" class="input-control-icon-left search-icon"><i class="icon icon-search"></i></label>
                        <a href="#" class="input-control-icon-right search-clear-btn"><i class="icon icon-remove"></i></a>
                    </div>
                </div>
                <div class="row tdCon">
                    <div class="ztreeZtreeBackground left">
                        <ul id="tree" class="ztree" style="margin-left:2%;"></ul>
                    </div>
                </div>
            </div>


<div style="display:none;">
     <ul id="tree1" ></ul>
</div>

        </div>
    </div>
    <!-- 删除警告 -->
    <div class="modal fade tiM" id="tiMode" style="z-index: 9999;">
        <div class="modal-dialog" style="width:300px;">
            <div class="modal-content">
                <div class="modal-header" style="border-bottom-width:0px;height:100px;line-height: 100px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">×</span><span class="sr-only">关闭</span></button>
                    <div class="text-center">
                        <i class="icon-warning-sign" style="color:#EA644A;"></i>
                        <span></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger sub">确定</button>
                </div>
            </div>
        </div>
    </div>

</div>
    <script id="dclDcsId_1" type="text/html">
		{{if rows != null && rows.length > 0}}
			{{each rows as value index}}
				<option value="{{value.dcsId}}">{{value.dcsName}}</option>
			{{/each}}
			{{else}}
				<option value="">--没有可选条件--</option>
		{{/if}}
	</script>
	    <script id="dclId_1" type="text/html">
		{{if rows != null && rows.length > 0}}
			<option value="-1">不关联</option>
			{{each rows as value index}}
				<option value="{{value.dclId}}">{{value.dclName}}</option>
			{{/each}}
			{{else}}
				<option value="">--没有可选条件--</option>
		{{/if}}
	</script>
<script id="addCons_1" type="text/html">

{{if rows != null && rows.length > 0}}
	{{each rows as value index}}
		<div class="form-group">
			<input type="hidden" name="infos[{{index}}].deoIndex"  value="{{value.deoIndex}}" />
			<input type="hidden" name="infos[{{index}}].deaNodeId"  value="{{deaNodeId}}" />
			<input type="hidden" name="infos[{{index}}].deoDefIndex"  value="{{value.defIndex}}" />
			<label  
				{{if value.defIsReq==1}}
					class="col-sm-2 control-label required"
				{{else}}
					class="col-sm-2 control-label"
				{{/if}}
			>{{value.defNodeAttr}}</label>
			<div class="col-sm-9">
		{{if value.defType==0}}
										      <input type="text" name="infos[{{index}}].deoAttrValue"   
											{{if value.defIsReq==1}}
												class="form-control xiaoY" 
											{{else}}
												class="form-control"
											{{/if}}
     									       placeholder="" value="{{value.deoAttrValue}}"/>
		{{else if value.defType==1}}	
										      <label class="radio-inline col-sm-4">
											  	<input type="radio" name="infos[{{index}}].deoAttrValue" 
													{{if value.deoAttrValue==(value.defNodeVal.split(',')[0])}}
														checked="checked" 
													{{/if}}
													{{if value.defIsReq==1}}
														class="xiaoY" 
													{{/if}}
												 value="{{value.defNodeVal.split(',')[0]}}" style="width: 16px; height: 16px; vertical-align: middle;"> {{value.defNodeVal.split(",")[0]}}
											 </label>
											 <label class="radio-inline col-sm-4">
											  	<input type="radio" name="infos[{{index}}].deoAttrValue" 
													{{if value.deoAttrValue==(value.defNodeVal.split(',')[1])}}
														checked="checked" 
													{{/if}}
													{{if value.defIsReq==1}}
														class="xiaoY" 
													{{/if}}
												value="{{value.defNodeVal.split(',')[1]}}" style="width: 16px; height: 16px; vertical-align: middle;"> {{value.defNodeVal.split(",")[1]}}
											 </label>
										      

		{{else if value.defType==2}}
								{{set temp = value.defNodeVal.split(",")}}
								{{set temp2 = value.deoAttrValue==null?[]:value.deoAttrValue.split(",")}}
								{{each temp as value1 index1}}
										 <label class="radio-inline"><input name="infos[{{index}}].deoAttrValue" type="checkbox" value="{{value1}}"
											{{if value.defIsReq==1}}
												class="xiaoY" 
											{{/if}}
											{{each temp2 as value2 index2}}
												{{if value2==value1}}
														checked="checked" 
												{{/if}}
											{{/each}}
										>{{value1}}</label>
								{{/each}}
										      
		{{else}}
								 <input type="text" 
											{{if value.defIsReq==1}}
												class="form-control time xiaoY" 
											{{else}}
												class="form-control time"
											{{/if}}
								 name="infos[{{index}}].deoAttrValue" value="{{value.deoAttrValue}}" placeholder="时间：yyyy-mm-dd hh:ii">

		{{/if}}
										    </div>
										</div>
			{{/each}}

{{/if}}
</script>
<script id="navCon_1" type="text/html">
    {{each navCont as value index}}
    <li {{if index == navCont.length - 1}} class="active" {{/if}}><a href="###" data-target="#tabContent" value="{{value.val}}" data-toggle="tab">{{value.name}}</a></li>
    {{/each}}
</script>
<%@include file="../../jsp/indexJavaScipt.jsp"%>
<script type="text/javascript" src="static/js/layui/xm-select.js"></script>
<script type="text/javascript" src="static/js/QTSystem/devAccount/devArchives.js"></script>

</body>
</html>