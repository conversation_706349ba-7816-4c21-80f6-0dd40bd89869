<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() +"://" + request.getServerName() + ":" +request.getServerPort() + path +"/";
%>
<!DOCTYPE html>
<html>
<head>
	<base href="<%=path %>/" />
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<%@include file="../../jsp/indexHead.jsp"%>
	<!-- <link href="static/zui-1.8.1/lib/chosen/chosen.min.css" rel="stylesheet"> -->
	<title>缺陷记录</title>
	<style>
		.tdCon{padding-left: 0px !important;}
		#leftCon .ztreeZtreeBackground{background-color:rgba(235, 235, 235, 0.8);padding-left:5px;}
		.btn-group>button:last-child{border-bottom-right-radius: 4px;border-top-right-radius: 4px;}
		#rightCon .tab-content{padding:15px 5px 0px;}
		/* #nav_1,#tab1{display: none;} */
		.navBtns{padding-bottom: 10px;}
		tbody tr:hover{cursor: pointer;}

		#rightCon .tdCon>div:last-child{height: calc(100% - 38px) !important;}
		#rightCon .tab-content>div:first-child{height: calc(100% - 72px);}
		#rightCon .tab-content>div:first-child .tabConts,#rightCon .tabConts>div:nth-child(2){height: calc(100% - 42px);}
		/* 表格 */
		#rightCon .tabConts .table,#rightCon .tabConts .table tbody tr:first-child td{border-top-width: 0px;}
		#rightCon .tabConts .table a,#rightCon .tabConts .table th{font-size: 15px;}
		#rightCon .tabConts .table td{font-size: 14px;}
		#rightCon .tabConts .table tr td{height: 32px;line-height: 32px;vertical-align: middle;word-break:break-all;}
		#rightCon .tabConts .table input[type="checkbox"],#rightCon .tabConts .table input[type="radio"]{width:15px;height: 15px;
			margin-top: 0px;vertical-align: middle;}
		#rightCon .tabConts .table label,#rightCon .tabConts .table{margin-bottom: 0px;}
		#rightCon .table-bordered{border-width: 0px !important;}
		
	</style>
</head>
<body id="cur" class="container-fluid clears">
<div  class="row">
	<div id="rightCon">
		<div class="row headCon clears" >
			<p class="text-left clears" ></p>
		</div>
		<div class="row tdCon clears">
			<div class="row">
				<div class="input-group col-xs-7 col-sm-6 col-md-5 col-lg-4 dat" style="padding-left:5px;">
					<div class="input-control search-box has-icon-left has-icon-right search-example" id="searchboxExample" style="margin:0px;">
						<input id="keyWord" name="keyWord" type="search" class="form-control search-input" placeholder="关键字：缺陷编号、缺陷描述">
						<label for="keyWord" class="input-control-icon-left search-icon"><i class="icon icon-search"></i></label>
						<a href="#" class="input-control-icon-right search-clear-btn"><i class="icon icon-remove"></i></a>
					</div>
					<span class="input-group-btn">
						    <button id='searchBtn' class="btn btn-danger" type="button"><spring:message code="Search"/><!-- 搜索 --></button>
						  </span>
				</div> 
				<ul id="navCon" class="nav nav-tabs "></ul>
			</div>
			<div class="row">
				<div class="row tab-content">
					<div class="row tab-pane fade active in" id="tabContent">
						<div class="row navBtns" id="nav_1">
							<div class="col-xs-7 clears">
								<div class="btn-group">
								<c:forEach var="pbtn" items="${permitBtn}"    varStatus="status">
											<c:if test="${permitBtn[status.index].btnFun=='devDefect.devDefectAddView()'}">
												<button onclick="${permitBtn[status.index].btnFun}"  class="btn btn-success" >${permitBtn[status.index].name}</button>
											</c:if>
											<c:if test="${permitBtn[status.index].btnFun=='devDefect.devDefectEditView()'}">
												<button onclick="${permitBtn[status.index].btnFun}"  class="btn btn-warning" >${permitBtn[status.index].name}</button>
											</c:if>
											<c:if test="${permitBtn[status.index].btnFun=='devDefect.devDefectDel()'}">
												<button onclick="${permitBtn[status.index].btnFun}"  class="btn btn-danger" >${permitBtn[status.index].name}</button>
											</c:if>
								</c:forEach>
								</div>
							</div>

						</div>

						<div class="row tabConts" id="tab1">
							<div class="row clears">
								<table class="table table-bordered table-striped table-hover table-responsive text-center">
									<thead>
									<tr id="tab1_head">
									</tr>
									</thead>
								</table>
							</div>
							<div class="row clears">
								<table class="table table-bordered table-striped table-hover table-responsive text-center">
									<tbody id="tab1_tbod">
									</tbody>
								</table>
							</div>
						</div>

					</div>
					<!--  分页 -->
					<div class="myPagerPe" style="position:absolute;bottom:0px;padding-right:15px;width:100%;"><ul id="myPager" class="pager pull-right"
																												   data-ride="pager" data-page="1" data-rec-total="0" data-max-nav-count="4" data-menu-direction="dropup"
																												   data-page-size-options="10,20,30,50,100"  data-rec-per-page="10"
																												   data-elements="first_icon,prev_icon,pages,next_icon,last_icon,size_menu,goto,page_of_total_text,items_range_text,total_text,"></ul>
					</div>
				</div>

			</div>
		</div>
	</div>
	<!-- 删除警告 -->
	<div class="modal fade tiM" id="tiMode" style="z-index: 9999;">
		<div class="modal-dialog" style="width:300px;">
			<div class="modal-content">
				<div class="modal-header" style="border-bottom-width:0px;height:100px;line-height: 100px;">
					<button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">×</span><span class="sr-only">关闭</span></button>
					<div class="text-center">
						<i class="icon-warning-sign" style="color:#EA644A;"></i>
						<span></span>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" data-dismiss="modal">取消</button>
					<button type="button" class="btn btn-danger sub">确定</button>
				</div>
			</div>
		</div>
	</div>


	<div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header" style='background-color:#33485C;color:#fff;border-top-right-radius:5px;border-top-left-radius:5px;'>
					<button type="button" class="close" data-dismiss="modal" style='color:#FFF;' aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h3 class="modal-title text-center"  id="addhead"></h3>
				</div>
				<div class="modal-body" style="position: relative;">
					<form id="addForm" class='form-horizontal' method="POST" onsubmit="return false;" >
						<input type="hidden" name="dedIndex" value="" id="dedIndex">
						<div class="form-group" id="deaNodeIdDiv">
				                        <label class="col-sm-2 control-label required" for="dedDevId">设备</label>
				                        <div class="col-sm-9">
				                        	<input type="hidden" name="dedDevId" id="dedDevId" class="xiaoY"  />
				                            <div id="chosedobj" >
							          		</div>
				                        </div>
				        </div>
						<div class="form-group">
							<label for="dedDesc" class="col-sm-2 control-label required">缺陷描述</label>
							<div class="col-sm-9">
								<input type="text" name="dedDesc"  class="form-control xiaoY" id="dedDesc" />
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-danger" data-dismiss="modal">关闭</button>
					<button type="button" id="addSubm" class="btn btn-success">保存</button>
				</div>
			</div>
		</div>
	</div>

<div style="display:none;">
     <ul id="tree1" ></ul>
</div>

</div>
<script id="navCon_1" type="text/html">
	{{each navCont as value index}}
	<li {{if index == navCont.length - 1}} class="active" {{/if}}><a href="###" data-target="#tabContent" value="{{value.val}}" data-toggle="tab">{{value.name}}</a></li>
	{{/each}}
</script>
<script id="tab1_th" type="text/html">
	<th style="width:50px;">
		<label class="checkeds"><input type="checkbox" /></label>
	</th>
	{{each navCont[0].tabThName as value index}}
		<th >{{value}}</th>
	{{/each}}
</script>
<script id="tab1_tb" type="text/html">
	{{if navCont[0].tabTbData.length != 0}}
	{{each navCont[0].tabTbData as value index}}
	<tr>
		<td><label ><input name="dedIndex" type="checkbox" value="{{value.dedIndex}}" /></label></td>
		<td>{{value.dedId}}</td>
		<td>{{value.dcsName}}</td>
		<td>{{value.detNodePath}}</td>
		<td>{{value.dedDevName}}</td>
		<td title="{{value.dedDesc}}">{{value.dedDesc}}</td>
		<td>{{value.userName}}</td>
		<td>{{dateFormat(value.dedCreateTime)}}</td>
	</tr>
	{{/each}}
	{{else}}
	<tr>
		<td colspan="{{navCont[0].tabThName.length}}">暂时没有数据</td>
	</tr>
	{{/if}}
</script>
<%@include file="../../jsp/indexJavaScipt.jsp"%>
<script type="text/javascript" src="static/js/layui/xm-select.js"></script>
<script type="text/javascript" src="static/js/QTSystem/devAccount/devDefectRecord.js"></script>
</body>
</html>