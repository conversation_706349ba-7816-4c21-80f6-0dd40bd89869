<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@include file="../includeBaseSet.jsp"%>
<%@include file="../includeSystemSet.jsp"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>自定义报表-统计报表</title> 
<body class="hold-transition skin-blue sidebar-mini">
	<div class="wrapper">
		<!-- Content Wrapper. Contains page content -->
		<div class="po-relative">
			<!-- Content Header (Page header) -->
            <section class="content-header" style="border-bottom: 1px solid #b8b8b8;height:28px; background-color: #efefef; padding: 1px 15px 0 15px">
                <span id="navTitle" style="text-align:left;font-size:18px">统计报表 &gt; 自定义报表</span>
			</section>
			<!-- Main-->
	   <section class="map-content" id="mapContent">
				<div class="tree-content" id="treeContent">
					<div style="text-align: right; background-color: #CCC;height:26px; ">
				         <i id='pic1' class="fa fa-chevron-left"  onclick="maxminDiv()" data-back="true" style="z-index:9999;padding:6px;position:relative;cursor:pointer"></i>
				     </div>
					<div id="TreeBack" class="zTreeDemoBackground" style="overflow: auto;">
						<ul id="treeDemo" class="ztree"></ul>
					</div>
				</div>	

				<div>
					<section class="connectedSortable">
						<!-- Custom tabs (Charts with tabs)-->
						<div class="nav-tabs-custom b-radius">
							<!-- Tabs within a box -->
							<ul class="nav nav-tabs pull-right">
								<li class="active"><a href="#u-alarm" data-toggle="tab">自定义报表</a>
								</li>
							</ul>
							
							
							<div class="tab-pane active bg-s" id="message" style="width: 100%;float: right;" >
		                  <div class="inspection-btn">
		                        <!-- <div class="fl mr10"><button onclick="expInfo.expInfoAdd()" type="button" class="btn btn-block btn-default query-btn">新增</button></div>
		                        <div class="fl mr10"><button onclick="expInfo.expInfoEdit()" type="button" class="btn btn-block btn-default query-btn">编辑</button></div> -->
		                        
		                        <!-- <div class="rg mr10"><button onclick="expInfo.expInfoDel()" type="button" class="btn btn-block btn-default query-btn">删除</button></div> -->
		                      	<!-- <div class="rg mr10"><button onclick="expInfo.expInfoDown()" type="button" class="btn btn-block btn-default query-btn">下载</button></div> -->
		                      	<div class="rg mr10">
                                  <button class="btn btn-block btn-default query-btn" data-toggle="modal" data-target="#myModal1">上传</button>
                                </div>
		                      	
		                      </div>
			              	  <div class="load-table">
		                          <div class="box-body">
		                            <table id="table" data-height="500" border="1" class="table table-hover" width="100%"></table>
		                          </div>  
		                   	  </div>
		                   	  
		                   	  <div class="modal fade" id="myModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">  
								  <div class="modal-dialog">  
								    <div class="modal-content"  backdrop="static">  
								      <div class="modal-header">  
								        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>  
								        <h4 class="modal-title">导入</h4>  
								      </div>  
								      <div class="modal-body">  
								        <form action="" method="post" id="form1" name="form1" enctype="multipart/form-data">
										        文件：<input type="file" name="file"/>
										        <input type="button"  value="上传" onclick="from()"/>
										</form>  
								      </div>  
								    </div>
								  </div> 
								</div>
		                   	  
	              			</div>
						</div>
					</section>
				</div>
			</section>
		</div>
	</div>

<script type="text/javascript" src="${qtpath}/static/js/QTSystem/statistical/customStatistics.js"></script>
</body>
</html>

