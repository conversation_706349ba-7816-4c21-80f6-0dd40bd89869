<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@include file="../includeBaseSet.jsp"%>
<%@include file="../includeSystemSet.jsp"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>用电能效排名</title>

</head>
<body class="hold-transition skin-blue sidebar-mini">
	<div class="wrapper">
		<!-- Content Wrapper. Contains page content -->
		<div class=" po-relative">
			<!-- Content Header (Page header) -->
            <section class="content-header" style="border-bottom: 1px solid #b8b8b8;height:28px; background-color: #efefef; padding: 1px 15px 0 15px">
                <span id="navTitle" style="text-align:left;font-size:18px">能效分析 &gt; 能效排名</span>
			</section>
			<!-- Main content -->
			<section class="connectedSortable">
				<!-- Custom tabs (Charts with tabs)-->
				<div class="nav-tabs-custom b-radius">
					<!-- Tabs within a box -->
					<ul class="nav nav-tabs pull-right">
						<li><a href="#years-load" onclick="tab(3)" data-toggle="tab">地区排名</a></li>
						<li><a href="#month-load" onclick="tab(2)" data-toggle="tab">同类排名</a></li>
						<li class="active" onclick="tab(1)"><a href="#daily-load" data-toggle="tab">系统排名</a></li>
					</ul>
					<div class="tab-content no-padding">
						<!-- Morris chart - Sales -->
						<div class="tab-pane active bg-s" id="daily-load"
							style="position: relative;overflow: auto;">
							<div class="content-nav">
								<div class="fl mr10 l_height34">&nbsp;&nbsp;时间:</div>
								<div class="fl mr10 w_100">
								<div class="input-group date">
								   <!--  <div class="input-group-addon">
											<div class="fa fa-calendar"></div>
									</div> -->
									<div class="input-group date">
										<input type="text" class="form-control pull-right"
										 style="width:100px;"	id="datepicker1">
									</div>
								</div>
								</div>
								<div class="fl mr10">
									<button type="button" onclick="search()"
										class="btn btn-block btn-default query-btn">排名</button>
								</div>
								<div class="rg mr10">
									<button type="button"
										class="btn btn-block btn-default query-btn" onclick="showXLS()">导出</button>
								</div>
								<div class="rg mr10">
									<button type="button"
										class="btn btn-block btn-default query-btn">打印</button>
								</div>
							</div>
							<div style="margin-top: 10px;">
								<div class="load-table">
									<div class="box-body">
										<table id="user_table1" data-height="500" border="1" class="table table-hover" width="100%">
											<thead>
												<tr>
													<th rowspan="2">企业简称</th>
													<th rowspan="2">站点名称</th>
													<th rowspan="2">上月用电</th>
													<th rowspan="2">本月用电</th>
													<th rowspan="2">名次</th>
												</tr>
											</thead>
											<tbody id="value_tbody1"></tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
						<div class="tab-pane bg-s" id="month-load"
							style="position: relative;height: 1200px;overflow: auto;">
							<div>
								<div class="fl mr10 l_height34">&nbsp;&nbsp;时间:</div>
								<div class="fl mr10 w_100">
								<div class="input-group date">
								   <!--  <div class="input-group-addon">
											<div class="fa fa-calendar"></div>
										</div> -->
									<div class="input-group date">
										<input type="text" class="form-control pull-right"
										style="width:100px;"	id="datepicker2">
									</div>
								</div>
								</div>
								<div class="fl mr10 l_height34">企业类型:</div>
								<div class="fl mr10 w_100">
									<select class="form-control pull-right" style="width:100px;" id="datepicker_bsc_type"></select>
								</div>
								<div class="fl mr10">
									<button type="button" onclick="search()"
										class="btn btn-block btn-default query-btn">排名</button>
								</div>
								<div class="rg mr10">
									<button type="button"
										class="btn btn-block btn-default query-btn" onclick="showXLS()">导出</button>
								</div>
								<div class="rg mr10">
									<button type="button"
										class="btn btn-block btn-default query-btn">打印</button>
								</div>
							</div>
							<div style="margin-top: 60px;">
								<div class="load-table">
									<div class="box-body">
										<table id="user_table2" data-height="500" border="1" class="table table-hover" width="100%">
											<thead>
												<tr>
													<th rowspan="2">企业简称</th>
													<th rowspan="2">站点名称</th>
													<th rowspan="2">上月用电</th>
													<th rowspan="2">本月用电</th>
													<th rowspan="2">名次</th>
												</tr>
											</thead>
											<tbody id="value_tbody2"></tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
						<div class="tab-pane bg-s" id="years-load"
							style="position: relative; height: 1200px; overflow: auto;">
							<div>
								<div class="fl mr10 l_height34">&nbsp;&nbsp;时间:</div>
								<div class="fl mr10 w_100">
									<div class="input-group date">
										<!-- <div class="input-group-addon">
											<div class="fa fa-calendar"></div>
										</div> -->
										<input type="text" style="width:100px;" class="form-control pull-right"
											id="datepicker3">
									</div>
								</div>
								<div class="fl mr10 l_height34">地区:</div>
								<div class="fl mr10 w_100">
									<select class="form-control pull-right" style="width:100px;" id="datepicker_area_desc"></select>
								</div>
								<div class="fl mr10">
									<button type="button" onclick="search()"
										class="btn btn-block btn-default query-btn">排名</button>
								</div>
								<div class="rg mr10">
									<button type="button"
										class="btn btn-block btn-default query-btn" onclick="showXLS()">导出</button>
								</div>
								<div class="rg mr10">
									<button type="button"
										class="btn btn-block btn-default query-btn">打印</button>
								</div>
							</div>
							<div style="margin-top: 60px;">
								<div class="load-table">
									<div class="box-body">
										<table id="user_table3" data-height="500" border="1" class="table table-hover" width="100%">
											<thead>
												<tr>
													<th rowspan="2">企业简称</th>
													<th rowspan="2">站点名称</th>
													<th rowspan="2">上月用电</th>
													<th rowspan="2">本月用电</th>
													<th rowspan="2">名次</th>
												</tr>
											</thead>
											<tbody id="value_tbody3"></tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			 </section>
		</div>
	</div>
	<script type="text/javascript">
		//Date picker
		$('#datepicker').datepicker({
			autoclose : true
		});
	</script>
	<script src="${qtpath}/static/js/QTSystem/efficiency/userefficiensy.js"></script>
</body>
</html>
