<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@include file="../includeBaseSet.jsp"%>
<%@include file="../includeSystemSet.jsp"%>
<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<title>节能设备监测</title>
		<!-- Tell the browser to be responsive to screen width -->
		<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
		<!-- 自定义 css -->
<link rel="stylesheet" href="${qtpath}/static/css/style.css">
<!-- Bootstrap 3.3.6 -->
<link rel="stylesheet" href="${qtpath}/static/css/bootstrap/bootstrap.min.css">
<!-- 字体 Font Awesome -->
<link rel="stylesheet"
	href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.5.0/css/font-awesome.min.css">
<!-- 图标 Ionicons -->
<link rel="stylesheet"
	href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css">
<!-- Theme style -->
<link rel="stylesheet" href="${qtpath}/static/plugins/dist/css/AdminLTE.min.css">
<!-- AdminLTE Skins. Choose a skin from the css/skins  folder instead of downloading all of them to reduce the load. -->
<link rel="stylesheet"
	href="${qtpath}/static/plugins/dist/css/skins/_all-skins.min.css">
<!-- iCheck -->
<link rel="stylesheet" href="${qtpath}/static/plugins/iCheck/flat/blue.css">
<!-- Morris chart -->
<link rel="stylesheet" href="${qtpath}/static/plugins/morris/morris.css">
<!-- jvectormap -->
<link rel="stylesheet"
	href="${qtpath}/static/plugins/jvectormap/jquery-jvectormap-1.2.2.css">
<!-- Date Picker -->
<link rel="stylesheet" href="${qtpath}/static/plugins/datepicker/datepicker3.css">
<!-- Daterange picker -->
<link rel="stylesheet"
	href="${qtpath}/static/plugins/daterangepicker/daterangepicker-bs3.css">
<!-- bootstrap wysihtml5 - text editor -->
<link rel="stylesheet"
	href="${qtpath}/static/plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.min.css">

<!-- jQuery 2.2.0 -->
<script src="${qtpath}/static/plugins/jQuery/jQuery-2.2.0.min.js"></script>
<!-- Bootstrap 3.3.6 -->
<script src="${qtpath}/static/bootstrap/js/bootstrap.min.js"></script>
	</head>

	<body class="hold-transition skin-blue sidebar-mini">
		<div class="wrapper">
			<!-- Content Wrapper. Contains page content -->
			<div class=" po-relative">
				<!-- Content Header (Page header) -->
				<section class="content-header">
					<h1>
        <!--监测点对比-->
        <small><!--监测点对比--></small>
      </h1>
					<ol class="breadcrumb">
						<li>
							<a href="#"><i class="fa fa-dashboard"></i> 在线监测</a>
						</li>
						<li class="active">节能设备监测</li>
					</ol>
				</section>
				<!-- Main content -->
				<section class="data-content">
					<div class="tree-content">
						<div class="p10">
							<select class="form-control">
								<option>光谷软件园配电房</option>
								<option>金融港配电房</option>
								<option>option 3</option>
								<option>option 4</option>
								<option>option 5</option>
							</select>
						</div>
						<div class="zTreeDemoBackground left">
							<ul id="treeDemo" class="ztree"></ul>
						</div>
					</div>

					<!-- Main row -->
					<div class="map-mainBox">
						<!-- Left col -->
						<section class="connectedSortable">
							<!-- Custom tabs (Charts with tabs)-->
							<div class="nav-tabs-custom b-radius" id="myTab">
								<!-- Tabs within a box -->
								<ul class="nav nav-tabs pull-right">
									<li>
										<a href="#load" data-toggle="tab">负荷对比</a>
									</li>
									<li class="active">
										<a href="#electricity" data-toggle="tab">电量对比</a>
									</li>
								</ul>
								<div class="tab-content no-padding">
									<!-- Morris chart - Sales -->
									<div class="tab-pane active bg-s" id="electricity" style="position: relative; height: 600px;">
										<div class="row">
											<div class="col-lg-3 col-xs-6 l_height34">
												<div class="fl mr10">1#变压器</div>
												<div class="fl with_200">
													<select class="form-control">
														<option>2016-06-23 00:00</option>
														<option>option 2</option>
														<option>option 3</option>
														<option>option 4</option>
														<option>option 5</option>
													</select>
												</div>
											</div>
											<div class="col-lg-3 col-xs-6 l_height34">
												<div class="fl mr10">2#变压器</div>
												<div class="fl with_200">
													<select class="form-control">
														<option>2016-06-23 00:00</option>
														<option>option 2</option>
														<option>option 3</option>
														<option>option 4</option>
														<option>option 5</option>
													</select>
												</div>
											</div>
											<div class="col-lg-3 col-xs-6 l_height34">
												<div class="fl mr10">3#变压器</div>
												<div class="fl with_200">
													<select class="form-control">
														<option>2016-06-23 00:00</option>
														<option>option 2</option>
														<option>option 3</option>
														<option>option 4</option>
														<option>option 5</option>
													</select>
												</div>
											</div>
											<div class="col-lg-3 col-xs-6">
												<button type="button" class="btn btn-block btn-default">对比</button>
											</div>
										</div>
										<div class="box-body">
											<div class="monitor-chart-title">回路对比分析<br><span>某某站点</span></div>
											<div class="chart">
												<div id="barChart" style="height:230px;"></div>
											</div>
										</div>
									</div>
									<div class="tab-pane bg-s" id="load" style="position: relative; height:600px">
										<div class="row">
											<div class="col-lg-3 col-xs-6 l_height34">
												<div class="fl mr10">1#变压器</div>
												<div class="fl with_200">
													<select class="form-control">
														<option>2016-06-23 00:00</option>
														<option>option 2</option>
														<option>option 3</option>
														<option>option 4</option>
														<option>option 5</option>
													</select>
												</div>
											</div>
											<div class="col-lg-3 col-xs-6 l_height34">
												<div class="fl mr10">2#变压器</div>
												<div class="fl with_200">
													<select class="form-control">
														<option>2016-06-23 00:00</option>
														<option>option 2</option>
														<option>option 3</option>
														<option>option 4</option>
														<option>option 5</option>
													</select>
												</div>
											</div>
											<div class="col-lg-3 col-xs-6 l_height34">
												<div class="fl mr10">3#变压器</div>
												<div class="fl with_200">
													<select class="form-control">
														<option>2016-06-23 00:00</option>
														<option>option 2</option>
														<option>option 3</option>
														<option>option 4</option>
														<option>option 5</option>
													</select>
												</div>
											</div>
											<div class="col-lg-3 col-xs-6">
												<button type="button" class="btn btn-block btn-default">对比</button>
											</div>
										</div>
										<div class="box-body">
											<div class="monitor-chart-title">回路对比分析<br><span>某某站点</span></div>
											<div class="chart" style="height:230px;width:100%;">
												<div id="loadChart" style="height:230px;width:1100px;"></div>
											</div>
										</div>
									</div>
								</div>
						</section>
						</div>
				</section>
				</div>
			</div>
			<!-- echart 1.0.1 -->
			<script type="text/javascript" src="${qtpath}/static/js/echarts.js"></script>
			<!--电量对比-->
			<script type="text/javascript">
				function elebar(optionID1) {
					var myChart1 = echarts.init(document.getElementById("barChart"));
					var option1 = {
						title: {
							text: '',
							subtext: ''
						},
						tooltip: {
							trigger: 'axis',
							axisPointer: {
								type: 'shadow'
							}
						},
						legend: {
							data: ['1#变压器', '2#变压器', '3#变压器']
						},
						grid: {
							left: '3%',
							right: '4%',
							bottom: '3%',
							containLabel: true
						},
						yAxis: {
							type: 'value',
							boundaryGap: [0, 0.01]
						},
						xAxis: {
							type: 'category',
							data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']
						},
						series: [{
							name: '1#变压器',
							type: 'bar',
							data: [182, 23]
						}, {
							name: '2#变压器',
							type: 'bar',
							data: [25, 38]
						}, {
							name: '3#变压器',
							type: 'bar',
							data: [193, 38]
						}]
					};
					myChart1.setOption(option1);
				}
			</script>
			<!--负荷对比-->
			<script type="text/javascript">
				function loadbar(optionID1) {
					var myChart1 = echarts.init(document.getElementById("loadChart"));
					var option1 = {
						title: {
							text: '',
							subtext: ''
						},
						tooltip: {
							trigger: 'axis',
							axisPointer: {
								type: 'shadow'
							}
						},
						legend: {
							data: ['1#变压器', '2#变压器', '3#变压器']
						},
						grid: {
							left: '3%',
							right: '4%',
							bottom: '3%',
							containLabel: true
						},
						yAxis: {
							type: 'value',
							boundaryGap: [0, 0.01]
						},
						xAxis: {
							type: 'category',
							data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']
						},
						series: [{
							name: '1#变压器',
							type: 'bar',
							data: [182, 23, 214, 70, 131, 230, 82, 123, 14, 40, 21, 110]
						}, {
							name: '2#变压器',
							type: 'bar',
							data: [25, 38, 100, 121, 141, 47, 125, 138, 200, 161, 41, 211]
						}, {
							name: '3#变压器',
							type: 'bar',
							data: [193, 38, 50, 213, 164, 123, 93, 138, 210, 113, 134, 153]
						}]
					};
					myChart1.setOption(option1);
				}
			</script>
			<script type="text/javascript">
				$(function() {
					elebar();
					loadbar();
				})
			</script>
	</body>

</html>