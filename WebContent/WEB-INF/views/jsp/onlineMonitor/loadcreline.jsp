<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@include file="../includeBaseSet.jsp"%>
<%@include file="../includeSystemSet.jsp"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title></title>

</head>
<body class="hold-transition skin-blue sidebar-mini">
	<div class="wrapper">
		<!-- Content Wrapper. Contains page content -->
		<div class=" po-relative">
			<!-- Content Header (Page header) -->
			<section class="content-header">
				<h1>
					<small></small>
				</h1>
				<ol class="breadcrumb">
					<li><a href="#"><i class="fa fa-dashboard"></i>当前监测点:</a></li>
					<li class="active">实时数据</li>
				</ol>
			</section>

			<!-- Main content -->
			<section class="data-content">
				<div class="tree-content">
					<div class="box-tools">
						<div class="input-group input-group-sm p10" style="width: 150px;">
							<input type="text" name="table_search"
								class="form-control pull-right" placeholder="Search">

							<div class="input-group-btn">
								<button type="submit" class="btn btn-default">
									<i class="fa fa-search"></i>
								</button>
							</div>
						</div>
					</div>
					<div class="zTreeDemoBackground left">
						<ul id="treeDemo" class="ztree"></ul>
					</div>
				</div>

				<!-- Main row -->
				<div class="map-mainBox">
					<!-- Left col -->
					<section class="connectedSortable">
						<!-- Custom tabs (Charts with tabs)-->
						<div class="nav-tabs-custom b-radius">
							<!-- Tabs within a box -->
							<ul class="nav nav-tabs pull-right">
								<li ><a href="#temperature" data-toggle="tab">温度</a></li>
								<li><a href="#harmonic" data-toggle="tab">谐波</a></li>
								<li><a href="#power" data-toggle="tab">功率因数</a></li>
								<li class="active"><a href="#load" data-toggle="tab">负荷</a></li>
								<li><a href="#pressure" data-toggle="tab">电压</a></li>
								<li><a href="#current" data-toggle="tab">电流</a></li>
							</ul>
							<div class="tab-content no-padding">
								<div class="tab-pane active bg-s" id="current"
									style="position: relative; height: 600px;">
									<div class="row">
										<div id="line" style="height: 400px;"></div>
									</div>
									<div class="load-table">
										<div class="box-body">
											<table id="table" class="display"></table>
										</div>
									</div>
								</div>
								<div class="tab-pane bg-s" id="pressure"
									style="position: relative; height: 600px;">
									<div class="row">
										<div id="voltline" style="height: 400px; width: 1500px;"></div>
									</div>
									<div class="load-table">
										<div class="box-body">
											<table id="table1" class="display"></table>
										</div>
									</div>
								</div>
								<div class="tab-pane bg-s" id="load"
									style="position: relative; height: 600px;">
									<div class="row">
										<div id="loadline" style="height: 400px; width: 1500px;"></div>
									</div>
									<div class="load-table">
										<div class="box-body">
											<table id="table2" class="display"></table>
										</div>
									</div>
								</div>


								<div class="tab-pane bg-s" id="power"
									style="position: relative; height: 600px;">
									<div class="row">
										<div id="powerline" style="height: 400px; width: 1500px;"></div>
									</div>
									<div class="load-table">
										<div class="box-body">
											<table id="table3" class="display"></table>
										</div>
									</div>
								</div>

								<div class="tab-pane bg-s" id="harmonic"
									style="position: relative; height: 600px;">
									<div class="row">
										<div id="harmonicline" style="height: 400px; width: 1500px;"></div>
									</div>
									<div class="load-table">
										<div class="box-body">
											<table id="table4" class="display"></table>
										</div>
									</div>
								</div>

								<div class="tab-pane bg-s" id="temperature"
									style="position: relative; height: 600px;">
									<div class="row">
										<div id="temperatureline"
											style="height: 400px; width: 1500px;"></div>
									</div>
									<div class="load-table">
										<div class="box-body">
											<table id="table5" class="display"></table>
										</div>
									</div>
								</div>
							</div>
						</div>
					</section>
				</div>
			</section>
		</div>
	</div>
		<script
		src="${qtpath}/static/js/QTSystem/onlineMonitor/realtimeData.js"></script>
	<script src="${qtpath}/static/js/QTSystem/onlineMonitor/realtime.js"></script>
	<script type="text/javascript">
		$(function() {
			line();
			voltline();
			loadline();
			powerline();
			harmonicline();
			temperatureline();
		});
		
		function temperatureline(optionID) {
			var myChart = echarts.init(document.getElementById('temperatureline'));
			var option = {
				title : {
					text : '',
					subtext : ''
				},
				tooltip : {
					trigger : 'axis'
				},
				legend : {
					data : [ 'A相温度', 'B相温度', 'C相温度' ]
				},
				toolbox : {
					show : false,
					feature : {
						dataZoom : {
							yAxisIndex : 'none'
						},
						dataView : {
							readOnly : false
						},
						magicType : {
							type : [ 'line', 'bar' ]
						},
						restore : {},
						saveAsImage : {}
					}
				},
				xAxis : {
					type : 'category',
					boundaryGap : false,
					data : [ '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
							'10', '11', '12', '13', '14', '15', '16', '17',
							'18', '19', '20', '21', '22', '23' ]
				},
				yAxis : {
					type : 'value',
					axisLabel : {
						formatter : '{value}℃'
					}
				},
				series : [
						{
							name : 'A相温度',
							type : 'line',
							data : [ 5.8, 6.8, 15.7, 13.9, 11.4, 13, 20, 26.5,
									21.8, 22.9, 23.6, 26.0, 14.7, 11.4, 15, 13,
									12, 16.6, 17.4, 11, 19, 28.1, 22.7, 21.8 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						{
							name : 'B相温度',
							type : 'line',
							data : [ 3.3, 12.8, 15.4, 23.8, 27.4, 29.5, 12.6,
									23, 15, 13, 12, 14, 10.6, 11.4, 15.8, 13.5,
									12.6, 12.5, 10.9, 11.2, 25, 22.2, 21.8,
									28.9 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						{
							name : 'C相温度',
							type : 'line',
							data : [ 6, 17, 19, 13.5, 10.5, 6.5, 2.5, 11, 15,
									13, 12, 17, 10, 18, 13.6, 12.8, 10.4, 11.9,
									5.5, 3.7, 6.9, 3.6, 7.8, 23 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						} ]
			};
			myChart.setOption(option);
		}
		
		function line(optionID) {
			var myChart = echarts.init(document.getElementById('line'));
			var option = {
				title : {
					text : '',
					subtext : ''
				},
				tooltip : {
					trigger : 'axis'
				},
				legend : {
					data : [ 'A相电流', 'B相电流', 'C相电流' ]
				},
				toolbox : {
					show : false,
					feature : {
						dataZoom : {
							yAxisIndex : 'none'
						},
						dataView : {
							readOnly : false
						},
						magicType : {
							type : [ 'line', 'bar' ]
						},
						restore : {},
						saveAsImage : {}
					}
				},
				xAxis : {
					type : 'category',
					boundaryGap : false,
					data : [ '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
							'10', '11', '12', '13', '14', '15', '16', '17',
							'18', '19', '20', '21', '22', '23' ]
				},
				yAxis : {
					type : 'value',
					axisLabel : {
						formatter : '{value}A'
					}
				},
				series : [
						{
							name : 'A相电流',
							type : 'line',
							data : [ 5.8, 6.8, 15.7, 13.9, 11.4, 13, 20, 26.5,
									21.8, 22.9, 23.6, 26.0, 14.7, 11.4, 15, 13,
									12, 16.6, 17.4, 11, 19, 28.1, 22.7, 21.8 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						{
							name : 'B相电流',
							type : 'line',
							data : [ 3.3, 12.8, 15.4, 23.8, 27.4, 29.5, 12.6,
									23, 15, 13, 12, 14, 10.6, 11.4, 15.8, 13.5,
									12.6, 12.5, 10.9, 11.2, 25, 22.2, 21.8,
									28.9 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						{
							name : 'C相电流',
							type : 'line',
							data : [ 6, 17, 19, 13.5, 10.5, 6.5, 2.5, 11, 15,
									13, 12, 17, 10, 18, 13.6, 12.8, 10.4, 11.9,
									5.5, 3.7, 6.9, 3.6, 7.8, 23 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						} ]
			};
			myChart.setOption(option);
		}

		function voltline(optionID) {
			var myChart = echarts.init(document.getElementById('voltline'));
			var option = {
				title : {
					text : '',
					subtext : ''
				},
				tooltip : {
					trigger : 'axis'
				},
				legend : {
					data : [ 'A相电压', 'B相电压', 'C相电压' ]
				},
				toolbox : {
					show : false,
					feature : {
						dataZoom : {
							yAxisIndex : 'none'
						},
						dataView : {
							readOnly : false
						},
						magicType : {
							type : [ 'line', 'bar' ]
						},
						restore : {},
						saveAsImage : {}
					}
				},
				xAxis : {
					type : 'category',
					boundaryGap : false,
					data : [ '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
							'10', '11', '12', '13', '14', '15', '16', '17',
							'18', '19', '20', '21', '22', '23' ]
				},
				yAxis : {
					type : 'value',
					axisLabel : {
						formatter : '{value}V'
					}
				},
				series : [
						{
							name : 'A相电压',
							type : 'line',
							data : [ 5.8, 6.8, 15.7, 13.9, 11.4, 13, 20, 26.5,
									21.8, 22.9, 23.6, 26.0, 14.7, 11.4, 15, 13,
									12, 16.6, 17.4, 11, 19, 28.1, 22.7, 21.8 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						{
							name : 'B相电压',
							type : 'line',
							data : [ 3.3, 12.8, 15.4, 23.8, 27.4, 29.5, 12.6,
									23, 15, 13, 12, 14, 10.6, 11.4, 15.8, 13.5,
									12.6, 12.5, 10.9, 11.2, 25, 22.2, 21.8,
									28.9 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						{
							name : 'C相电压',
							type : 'line',
							data : [ 6, 17, 19, 13.5, 10.5, 6.5, 2.5, 11, 15,
									13, 12, 17, 10, 18, 13.6, 12.8, 10.4, 11.9,
									5.5, 3.7, 6.9, 3.6, 7.8, 23 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						} ]
			};
			myChart.setOption(option);
		}

		function loadline(optionID) {
			var myChart = echarts.init(document.getElementById('loadline'));
			var option = {
				title : {
					text : '',
					subtext : ''
				},
				tooltip : {
					trigger : 'axis'
				},
				legend : {
					data : [ 'A相负荷', 'B相负荷', 'C相负荷' ]
				},
				toolbox : {
					show : false,
					feature : {
						dataZoom : {
							yAxisIndex : 'none'
						},
						dataView : {
							readOnly : false
						},
						magicType : {
							type : [ 'line', 'bar' ]
						},
						restore : {},
						saveAsImage : {}
					}
				},
				xAxis : {
					type : 'category',
					boundaryGap : false,
					data : [ '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
							'10', '11', '12', '13', '14', '15', '16', '17',
							'18', '19', '20', '21', '22', '23' ]
				},
				yAxis : {
					type : 'value',
					axisLabel : {
						formatter : '{value}kw'
					}
				},
				series : [
						{
							name : 'A相负荷',
							type : 'line',
							data : [ 5.8, 6.8, 15.7, 13.9, 11.4, 13, 20, 26.5,
									21.8, 22.9, 23.6, 26.0, 14.7, 11.4, 15, 13,
									12, 16.6, 17.4, 11, 19, 28.1, 22.7, 21.8 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						{
							name : 'B相负荷',
							type : 'line',
							data : [ 3.3, 12.8, 15.4, 23.8, 27.4, 29.5, 12.6,
									23, 15, 13, 12, 14, 10.6, 11.4, 15.8, 13.5,
									12.6, 12.5, 10.9, 11.2, 25, 22.2, 21.8,
									28.9 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						{
							name : 'C相负荷',
							type : 'line',
							data : [ 6, 17, 19, 13.5, 10.5, 6.5, 2.5, 11, 15,
									13, 12, 17, 10, 18, 13.6, 12.8, 10.4, 11.9,
									5.5, 3.7, 6.9, 3.6, 7.8, 23 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						} ]
			};
			myChart.setOption(option);
		}

		function powerline(optionID) {
			var myChart = echarts.init(document.getElementById('powerline'));
			var option = {
				title : {
					text : '',
					subtext : ''
				},
				tooltip : {
					trigger : 'axis'
				},
				legend : {
					data : [ '有功功率', '无功功率' ]
				},
				toolbox : {
					show : false,
					feature : {
						dataZoom : {
							yAxisIndex : 'none'
						},
						dataView : {
							readOnly : false
						},
						magicType : {
							type : [ 'line', 'bar' ]
						},
						restore : {},
						saveAsImage : {}
					}
				},
				xAxis : {
					type : 'category',
					boundaryGap : false,
					data : [ '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
							'10', '11', '12', '13', '14', '15', '16', '17',
							'18', '19', '20', '21', '22', '23' ]
				},
				yAxis : {
					type : 'value',
					axisLabel : {
						formatter : '{value}%'
					}
				},
				series : [
						{
							name : '有功功率',
							type : 'line',
							data : [ 5.8, 6.8, 15.7, 13.9, 11.4, 13, 20, 26.5,
									21.8, 22.9, 23.6, 26.0, 14.7, 11.4, 15, 13,
									12, 16.6, 17.4, 11, 19, 28.1, 22.7, 21.8 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						{
							name : '无功功率',
							type : 'line',
							data : [ 3.3, 12.8, 15.4, 23.8, 27.4, 29.5, 12.6,
									23, 15, 13, 12, 14, 10.6, 11.4, 15.8, 13.5,
									12.6, 12.5, 10.9, 11.2, 25, 22.2, 21.8,
									28.9 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						} ]
			};
			myChart.setOption(option);
		}

		function harmonicline(optionID) {
			var myChart = echarts.init(document.getElementById('harmonicline'));
			var option = {
				title : {
					text : '',
					subtext : ''
				},
				tooltip : {
					trigger : 'axis'
				},
				legend : {
					data : [ '电流1-31次谐波', '电压1-31次谐波']
				},
				toolbox : {
					show : false,
					feature : {
						dataZoom : {
							yAxisIndex : 'none'
						},
						dataView : {
							readOnly : false
						},
						magicType : {
							type : [ 'line', 'bar' ]
						},
						restore : {},
						saveAsImage : {}
					}
				},
				xAxis : {
					type : 'category',
					boundaryGap : false,
					data : [ '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
							'10', '11', '12', '13', '14', '15', '16', '17',
							'18', '19', '20', '21', '22', '23' ]
				},
				yAxis : {
					type : 'value',
					axisLabel : {
						formatter : '{value}A'
					}
				},
				series : [
						{
							name : '电流1-31次谐波',
							type : 'line',
							data : [ 5.8, 6.8, 15.7, 13.9, 11.4, 13, 20, 26.5,
									21.8, 22.9, 23.6, 26.0, 14.7, 11.4, 15, 13,
									12, 16.6, 17.4, 11, 19, 28.1, 22.7, 21.8 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						{
							name : '电压1-31次谐波',
							type : 'line',
							data : [ 3.3, 12.8, 15.4, 23.8, 27.4, 29.5, 12.6,
									23, 15, 13, 12, 14, 10.6, 11.4, 15.8, 13.5,
									12.6, 12.5, 10.9, 11.2, 25, 22.2, 21.8,
									28.9 ],
						/* markPoint : {
							data : [ {
								type : 'max',
								name : '最大值'
							}, {
								type : 'min',
								name : '最小值'
							} ]
						},
						markLine : {
							data : [ {
								type : 'average',
								name : '平均值'
							} ]
						} */
						},
						 ]
			};
			myChart.setOption(option);
		}
	</script>
	<script
		src="${qtpath}/static/js/QTSystem/onlineMonitor/realtimeData.js"></script>
</body>
</html>
