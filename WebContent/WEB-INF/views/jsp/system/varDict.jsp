<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@include file="../includeBaseSet.jsp"%>
<%@include file="../includeSystemSet.jsp"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>变量字典</title>
<body class="hold-transition skin-blue sidebar-mini">
	<div class="wrapper">

		<!-- Content Wrapper. Contains page content -->
		<div class="po-relative">
			<!-- Content Header (Page header) -->
            <section class="content-header" style="border-bottom: 1px solid #b8b8b8;height:28px; background-color: #efefef; padding: 1px 15px 0 15px">
                <span id="navTitle" style="text-align:left;font-size:18px">系统管理 &gt; 变量字典</span>
			</section>
			<!-- Main content -->
		   <section class="connectedSortable">
				<!-- <div class="tree-content">
					<div class="box-tools">
						<div class="input-group input-group-sm p10" style="width: 150px;">
							<input type="text" name="table_search"
								class="form-control pull-right" placeholder="Search">
							<div class="input-group-btn">
								<button type="submit" class="btn btn-default">
									<i class="fa fa-search"></i>
								</button>
							</div>
						</div>
					</div>
					<div class="zTreeDemoBackground left" style="overflow: auto;">
						<ul id="treeDemo" class="ztree"></ul>
					</div>
				</div> -->
                <div class="nav-tabs-custom b-radius">
				<!-- Main row -->
				<!-- <div class="map-mainBox" style="left:0px;"> -->
					<!-- Left col -->
					<section class="connectedSortable">
						<!-- Custom tabs (Charts with tabs)-->
						<div class="nav-tabs-custom b-radius">
							<!-- Tabs within a box -->
							<ul class="nav nav-tabs pull-right">
								<li class="active"><a href="#u-alarm" data-toggle="tab">变量字典</a>
								</li>
							</ul>
							
							
							<div class="tab-pane active bg-s" id="message" style="width: 100%;float: right;" >
		                  <div class="inspection-btn">
		                        <div class="fl mr10"><button onclick="zskInfo.zskInfoAdd()" type="button" class="btn btn-block btn-default query-btn">新增</button></div>
		                        <div class="fl mr10"><button onclick="zskInfo.zskInfoEdit()" type="button" class="btn btn-block btn-default query-btn">编辑</button></div>
		                        <div class="fl mr10"><button onclick="zskInfo.zskInfoDel()" type="button" class="btn btn-block btn-default query-btn">删除</button></div>
		                      </div>
			              	  <div class="load-table">
		                          <div class="box-body">
		                            <table id="zsk_info_table"></table>
		                          </div>  
		                   	  </div>
		                   	  <div id="zsk_info_map" ></div>
		                   	   <!-- 新增和编辑企业信息 -->
				        	<div id="zsk_info_edit" class="box box-info" style="display: none">
				            <div class="box-header with-border"></div>
				            <form id="zsk_info_form" class="form-horizontal" method="post">
				            <input type="hidden" id="zskVarIndex" name="zskVarIndex"/>
				            <input type="hidden" id="zskVarSyncTime" name="zskVarSyncTime"/>
				              <div class="box-body" style="overflow: hidden;">
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">一级类别：</label>
				                  <div class="col-sm-8">
				                    <input id="zskVarMastType" name="zskVarMastType" type="text" class="form-control">
				                  </div>
				                </div>
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">二级类别：</label>
				                  <div class="col-sm-8">
				                    <input id="zskVarSubType" name="zskVarSubType" type="text" class="form-control">
				                  </div>
				                </div>
				               <div class="form-group">
				                  <label class="col-sm-3 control-label">变量名：</label>
				                  <div class="col-sm-8">
				                    <input id="zskVarName" name="zskVarName" type="text" class="form-control">
				                  </div>
				                </div>
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">描述：</label>
				                  <div class="col-sm-8">
				                  	<input id="zskVarDesc" name="zskVarDesc" class="form-control"/>
				                  </div>
				                </div>
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">备注：</label>
				                  <div class="col-sm-8">
				                  	<input id="zskvarNote" name="zskvarNote" class="form-control"/>
				                  </div>
				                </div>
				              </div>
				              <div class="box-footer">
				                <button onclick="zskInfo.zskInfoSave()" type="button" class="btn btn-info" style="margin-left: 31%;">确定</button>
				                <button onclick="zskInfo.zskInfoCancel()" type="button" class="btn btn-default" style="margin-left: 15%;">取消</button>
				              </div>
				            </form>
				          </div> 
		                   	  
	              		</div>
						</div>
					</section>
				</div>
			</section>
		</div>
	</div>

<script type="text/javascript" src="${qtpath}/static/js/QTSystem/system/varDict.js"></script>
</body>
</html>

