<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@include file="../includeBaseSet.jsp"%>
<%@include file="../includeSystemSet.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>经验知识</title>
</head>
<body class="hold-transition skin-blue sidebar-mini">
<div class="wrapper">
    <!-- Content Wrapper. Contains page content -->
    <div class="po-relative">
        <!-- Content Header (Page header) -->
        <section class="content-header" style="border-bottom: 1px solid #b8b8b8;height:28px; background-color: #efefef; padding: 1px 15px 0 15px">
            <span id="navTitle" style="text-align:left;font-size:18px">系统管理 &gt; 知识库设置</span>
		</section>
        <!-- Main content -->

            <!-- Main row -->
                <!-- Left col -->
                <section class="connectedSortable">
                    <!-- Custom tabs (Charts with tabs)-->
                    <div class="nav-tabs-custom b-radius">
                        <!-- Tabs within a box -->
                        <ul class="nav nav-tabs pull-right" id="myTabs">
                           <!--  <li><a href="#Y-electricity" onclick="tab(3)" data-toggle="tab">电缆分析报告</a></li> -->
                            <li class="active"><a href="#M-electricity"  data-toggle="tab">知识库</a></li>
                            <!-- <li class="active" ><a href="#D-electricity" data-toggle="tab">用户电缆基本信息</a></li> -->
                        </ul>
                        <div class="tab-content no-padding">
                            <!-- Morris chart - Sales -->
                            <div class="tab-pane active bg-s" id="D-electricity"
                                 style="position: relative; height: 500px;overflow:auto;">
                                <div class="content-nav">
                                    <c:choose>
										<c:when test="${currentAccount.roleName=='超级管理员'}">
											<div class="rg mr10">
											<button class="btn btn-block btn-default query-btn" data-toggle="modal" data-target="#myModal1">导入</button>
										    </div>
										</c:when>
										<c:otherwise>
										</c:otherwise>
								    </c:choose>
	                                <div class="rg mr10">
										<button type="button"
											class="btn btn-block btn-default query-btn" onclick="showXLS1()">导出
										</button>
									</div>
									<c:choose>
										<c:when test="${currentAccount.roleName=='超级管理员'}">
											 <div class="rg mr10">
											 <button onclick="alarmInfo1.zskInfoDel()" type="button" class="btn btn-block btn-default query-btn">删除</button>
										     </div>
										</c:when>
										<c:otherwise>
										</c:otherwise>
								    </c:choose>
                                </div>
                                <div class="load-table">
                                    <div class="box-body">
                                        <table id="table1" class="display" width="100%"></table>
                                    </div>
                                </div>
                                <div class="modal fade" id="myModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">  
								  <div class="modal-dialog">  
								    <div class="modal-content">  
								      <div class="modal-header">  
								        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>  
								        <h4 class="modal-title">导入</h4>  
								      </div>  
								      <div class="modal-body">  
								        <form action="<%=request.getContextPath()%>/zskSysDl/importFileZskSysDl" method="post" enctype="multipart/form-data">
										        文件：<input type="file" name="file"/>
										        <input type="submit" value="导入"/>
										</form>  
								      </div>  
								    </div>
								  </div> 
								</div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
    </div>
<script src="${qtpath}/static/js/QTSystem/system/knowledgeSet.js"></script>
</body>
</html>
