<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@include file="../includeBaseSet.jsp"%>
<%@include file="../includeSystemSet.jsp"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>新闻管理</title>
<script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=39c84239558daf36d465bbcb444fbfd9 "></script>
</head>
<body class="hold-transition skin-blue sidebar-mini">
<div class="wrapper">
  <div class=" po-relative">
    <section class="content-header">
      <h1>
        <!--企业档案-->
        <small><!--企业档案--></small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i>新闻管理</a></li>
        <li class="active">新闻管理</li>
      </ol>
    </section>
    <!-- Main content -->
    <section class="data-content" style="margin-left: 10px">
      	<!-- Main row -->
      	<div class="map-mainBox">
	        <!-- Left col -->
	        <section class="connectedSortable">
	          	<!-- Custom tabs (Charts with tabs)-->
	          	<div class="nav-tabs-custom b-radius">
	            	<!-- Tabs within a box -->
	            	<ul class="nav nav-tabs pull-right">
                      <li class="active"><a href="#equipment" data-toggle="tab">新闻管理</a></li>
	            	</ul>
	            	<div class="tab-content no-padding">
	              		<!-- 企业信息 -->
	              		<div class="tab-pane active bg-s" id="message" style="position: relative; height: 600px;">
		                    <div class="content-nav">
		                        
		                        <div class="fl mr10 w_200">
		                          <input type="text" class="form-control" placeholder="这里输入关键字">
		                        </div>
		                        <div class="fl mr10 l_height34">状态:</div>
		                        <div class="fl with_100 ">
						           <select class=form-control>
								    <option>全部</option>
								    <option>有效</option>
								    <option>无效</option>
					   	           </select>
					            </div>
		                        <div class="fl mr10"><button type="button" class="btn btn-block btn-default query-btn">查询</button></div>
		                        
		                      </div>
		                      <div class="inspection-btn">
		                        <div class="fl mr10"><button onclick="dccInfo.dccInfoAdd()" type="button" class="btn btn-block btn-default query-btn">新增</button></div>
		                        <div class="fl mr10"><button onclick="dccInfo.dccInfoEdit()" type="button" class="btn btn-block btn-default query-btn">编辑</button></div>
		                        <div class="fl mr10"><button onclick="dccInfo.dccInfoDel()" type="button" class="btn btn-block btn-default query-btn">删除</button></div>
		                      </div>
			              	  <div class="load-table">
		                          <div class="box-body">
		                            <table id="dcc_info_table"></table>
		                          </div>  
		                   	  </div>
			                  <div id="dcc_info_map" ></div>
	                   	  <!-- 新增和编辑企业信息 -->
				          <div id="dcc_info_edit" class="box box-info" style="display: none">
			                  <!-- 设置经纬度 -->
				            <div class="box-header with-border"></div>
				            <form id="dcc_info_form" class="form-horizontal" method="post">
				            <input type="hidden" id="dccId" name="dccId"/>
				              <div class="box-body" style="overflow: hidden;">
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">序号：</label>
				                  <div class="col-sm-8">
				                    <input id="dccFirstName" name="dccFirstName" type="text" class="form-control">
				                  </div>
				                </div>
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">登录名：</label>
				                  <div class="col-sm-8">
				                    <input id="dccFullName" name="dccFullName" type="text" class="form-control">
				                  </div>
				                </div>
				               <div class="form-group">
				                  <label class="col-sm-3 control-label">用户名：</label>
				                  <div class="col-sm-8">
				                    <input id="dccBscId" name="dccBscId" type="text" class="form-control">
				                  </div>
				                </div>
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">用户角色：</label>
				                  <div class="col-sm-8">
				                  	<input id="dccNature" name="dccNature" class="form-control"/>
				                  </div>
				                </div>
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">电子邮箱：</label>
				                  <div class="col-sm-8">
				                    <input id="dccRunCapacity" name="dccRunCapacity" type="text" class="form-control">
				                  </div>
				                </div>
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">状态：</label>
				                  <div class="col-sm-8">
				                    <textarea id="dccAddr" name="dccAddr" rows="2" class="form-control"></textarea>
				                  </div>
				                </div>
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">最近登录：</label>
				                  <div class="col-sm-8">
				                    <input id="dccRunCapacity" name="dccRunCapacity" type="text" class="form-control">
				                  </div>
				                </div>
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">最近登录IP：</label>
				                  <div class="col-sm-8">
				                    <input id="dccRunCapacity" name="dccRunCapacity" type="text" class="form-control">
				                  </div>
				                </div>
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">操作：</label>
				                  <div class="col-sm-8">
				                    <input id="dccRunCapacity" name="dccRunCapacity" type="text" class="form-control">
				                  </div>
				                </div>
				              </div>
				              <div class="box-footer">
				                <button onclick="dccInfo.dccInfoSave()" type="button" class="btn btn-info" style="margin-left: 31%;">确定</button>
				                <button onclick="dccInfo.dccInfoCancel()" type="button" class="btn btn-default" style="margin-left: 15%;">取消</button>
				              </div>
				            </form>
				          </div>
	              		</div>
	            	</div>
	          	</div>
	        </section>
	    </div>
    </section>
  </div>
</div>
<script type="text/javascript" src="${qtpath}/static/js/QTSystem/system/news.js"></script>
</body>
</html>
