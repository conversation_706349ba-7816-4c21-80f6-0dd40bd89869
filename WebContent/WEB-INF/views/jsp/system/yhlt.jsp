<%@page import="com.qt.entity.system.yhlt.Yhlt"%>
<%@page import="java.util.List"%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@include file="../includeBaseSet.jsp"%>
<%@include file="../includeSystemSet.jsp"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>企业档案</title>
<script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=39c84239558daf36d465bbcb444fbfd9 "></script>
</head>
<body class="hold-transition skin-blue sidebar-mini">
<div class="wrapper">
  <div class=" po-relative">
    <section class="content-header">
      <h1>
        <!--企业档案-->
        <small><!--企业档案--></small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i> 系统管理</a></li>
        <li class="active">用户论坛</li>
      </ol>
    </section>
    <!-- Main content -->
    <section class="data-content" style="margin-left: 10px">
      	<!-- Main row -->
      	<div class="map-mainBox">
	        <!-- Left col -->
	        <section class="connectedSortable">
	          	<!-- Custom tabs (Charts with tabs)-->
	          	<div class="nav-tabs-custom b-radius">
	            	<!-- Tabs within a box -->
	            	<ul class="nav nav-tabs pull-right" id="myTabs">
                      <li class='active'><a href="#yhlt" data-toggle="tab">用户论坛</a></li>
	            	</ul>
	            	<div class="tab-content no-padding">
	              		<!-- 企业信息 -->
	              		<div class="tab-pane active bg-s" id="yhlt" style="position: relative; height: 600px;">
		                    <div class="content-nav">
		                        <div class="fl mr10 l_height34">查询条件:</div>
		                        <div class="fl mr10 w_200">
		                          <input type="text" id="yhlt_info_search" class="form-control" placeholder="标题、内容、发布者">
		                        </div>
		                        <div class="fl mr10"><button onclick="yhltInfo.search()" type="button" class="btn btn-block btn-default query-btn">查询</button></div>
		                      </div>
		                      <div class="inspection-btn">
		                        <div class="fl mr10"><button onclick="yhltInfo.yhltInfoAdd()" type="button" class="btn btn-block btn-default query-btn">新增</button></div>
		                        <div class="fl mr10"><button onclick="yhltInfo.yhltInfoEdit()" type="button" class="btn btn-block btn-default query-btn">编辑</button></div>
		                        <c:choose>
									<c:when test="${currentAccount.roleName=='超级管理员'}">
										<div class="fl mr10"><button onclick="yhltInfo.yhltInfoDel()" type="button" class="btn btn-block btn-default query-btn">删除</button></div>
									</c:when>
									<c:otherwise>
									
									</c:otherwise>
								</c:choose>
		                        <div class="rg mr10"><button onclick="yhltInfo.dccInfoExport()" type="button" class="btn btn-block btn-default query-btn">导出excel</button></div>
		                      </div>
			              	  <div class="load-table">
		                          <div class="box-body">
		                            <table id="yhlt_info_table" class="table table-bordered table-hover scrolltable"></table>
		                          </div>  
		                   	  </div>
	                   	  <!-- 新增和编辑企业信息 -->
				          <div id="yhlt_info_edit" class="box box-info" style="display: none">
				            <div class="box-header with-border"></div>
				            <form id="yhlt_info_form" class="form-horizontal" method="post">
				              <input type="hidden"  id="yhltId" name="yhltId" value="0"/>
				              <input type="hidden"  id="publisher" name="publisher" value="0"/>
				              <div class="box-body" style="overflow: hidden;">
				                <div class="form-group">
				                  <label class="col-sm-3 control-label">标题：</label>
				                  <div class="col-sm-8">
				                    <input id="title" name="title" type="text" class="form-control">
				                  </div>
				                </div>
				                 <div class="form-group">
				                  <label class="col-sm-3 control-label">内容：</label>
				                  <div class="col-sm-8">
				                    <textarea id="content" name="content" rows="5" class="form-control"></textarea>
				                  </div>
				                </div>
				              </div>
				              <div class="box-footer">
				                <button onclick="yhltInfo.yhltInfoSave()" type="button" class="btn btn-info" style="margin-left: 31%;">确定</button>
				                <button onclick="yhltInfo.yhltInfoCancel()" type="button" class="btn btn-default" style="margin-left: 15%;">取消</button>
				              </div>
				            </form>
				          </div>
	              		</div>
			         </div>
                    </div>
                  </section>
	          	</div>
	        </section>
	    </div>
  </div>
	
<script type="text/javascript" src="${qtpath}/static/js/QTSystem/system/yhlt.js"></script>
</body>
</html>
