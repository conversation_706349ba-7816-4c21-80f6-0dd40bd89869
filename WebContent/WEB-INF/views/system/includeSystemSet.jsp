<%@ page contentType="text/html;charset=UTF-8"%>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
<!-- Tell the browser to be responsive to screen width -->
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<!-- 自定义 css -->
<link rel="stylesheet" href="${qtpath}/static/css/style.css">
<!-- Bootstrap 3.3.6 -->
<link rel="stylesheet" href="${qtpath}/static/css/bootstrap/bootstrap.min.css">
<!-- 字体 Font Awesome -->
<!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.5.0/css/font-awesome.min.css"> -->
<link rel="stylesheet" href="${qtpath}/static/plugins/font-awesome/css/font-awesome.min.css">

<!-- 图标 Ionicons -->
<!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css"> -->
<link rel="stylesheet" href="${qtpath}/static/plugins/ionicons/css/ionicons.min.css">

<!-- Theme style -->
<link rel="stylesheet" href="${qtpath}/static/plugins/dist/css/AdminLTE.min.css">
<%-- <link rel="stylesheet" href="${qtpath}/static/plugins/atl/css/AdminLTE.min.css"> --%>
<!-- AdminLTE Skins. Choose a skin from the css/skins  folder instead of downloading all of them to reduce the load. -->
<link rel="stylesheet" href="${qtpath}/static/plugins/dist/css/skins/_all-skins.min.css">
<!-- iCheck -->
<link rel="stylesheet" href="${qtpath}/static/plugins/iCheck/flat/blue.css">
<!-- Morris chart -->
<link rel="stylesheet" href="${qtpath}/static/plugins/morris/morris.css">
<!-- jvectormap -->
<link rel="stylesheet" href="${qtpath}/static/plugins/jvectormap/jquery-jvectormap-1.2.2.css">
<!-- Date Picker -->
<link rel="stylesheet" href="${qtpath}/static/plugins/datepicker/datepicker3.css">
<!-- Daterange picker -->
<link rel="stylesheet" href="${qtpath}/static/plugins/daterangepicker/daterangepicker-bs3.css">
<!-- bootstrap wysihtml5 - text editor -->
<link rel="stylesheet" href="${qtpath}/static/plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.min.css">
	
<!-- bootstrap-table -->
<link rel="stylesheet" href="${qtpath}/static/css/bootstrap/bootstrap-table.css">

<!-- select2插件css -->
<link rel="stylesheet" href="${qtpath}/static/plugins/select2/select2.min.css">

<!-- jQuery 2.2.0 -->
<script type="text/javascript" src="${qtpath}/static/plugins/jQuery/jQuery-2.2.0.min.js"></script>
<!-- Bootstrap 3.3.6 -->
<script src="${qtpath}/static/js/bootstrap/bootstrap.min.js"></script>

<!--jQuery datatable-->
<link rel="stylesheet" href="${qtpath}/static/js/datatables/css/jquery.dataTables.min.css">
<script src="${qtpath}/static/js/datatables/js/jquery.dataTables.min.js"></script>
<!-- jQuery UI 1.11.4 -->
<script src="${qtpath}/static/plugins/jQueryUI/jquery-ui.min.js"></script>
<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
<script>
	$.widget.bridge('uibutton', $.ui.button);
</script>
<!-- AdminLTE App -->
<script src="${qtpath}/static/plugins/dist/js/app.min.js"></script>

<!--zTree-->
<link rel="stylesheet" href="${qtpath}/static/plugins/zTree/3.5/zTreeStyle.css" type="text/css">
<script type="text/javascript" src="${qtpath}/static/plugins/zTree/3.5/jquery.ztree.core-3.5.js"></script>

<!-- bootstrap-table -->
<script type="text/javascript" src="${qtpath}/static/js/bootstrap/bootstrap-table.js"></script>
<script type="text/javascript" src="${qtpath}/static/js/bootstrap/locales/bootstrap-table-zh-CN.js"></script>

<!-- layer弹出框插件 -->
<script type="text/javascript" src="${qtpath}/static/plugins/layer/2.1/layer.js"></script>
<!--弹窗组件end-->
<script src="${qtpath}/static/js/system/jy/jy.main.js"></script>

<!-- select2插件 -->
<script type="text/javascript" src="${qtpath}/static/plugins/select2/select2.min.js"></script>

<!-- 时间日期插件 -->
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment.min.js"></script> -->
<script src="${qtpath}/static/plugins/moment/moment.min.js"></script>
<script src="${qtpath}/static/plugins/daterangepicker/daterangepicker.js"></script>
<script src="${qtpath}/static/plugins/datepicker/bootstrap-datepicker.js"></script>
<script src="${qtpath}/static/plugins/datepicker/locales/bootstrap-datepicker.zh-CN.js"></script>


<!-- echart -->
<script type="text/javascript" src="${qtpath}/static/plugins/echarts/2.2.7/echarts-all.js"></script>
 

<!-- jquery form 表单插件 -->
<script type="text/javascript" src="${qtpath}/static/js/jquery/jquery-form.js"></script>

<!-- easyui 表单反序列化插件 -->
<script type="text/javascript" src="${qtpath}/static/plugins/easyui/jquery.easyui.min.js"></script>


<script type="text/javascript">

/********** 公用方法 ************/
Date.prototype.pattern = function(fmt) {
	var o = {
		"M+" : this.getMonth() + 1, //月份      
		"d+" : this.getDate(), //日      
		"h+" : this.getHours() % 12 == 0 ? 12 : this.getHours() % 12, //小时      
		"H+" : this.getHours(), //小时      
		"m+" : this.getMinutes(), //分      
		"s+" : this.getSeconds(), //秒      
		"q+" : Math.floor((this.getMonth() + 3) / 3), //季度      
		"S" : this.getMilliseconds()
	//毫秒      
	};
	var week = {
		"0" : "\u65e5",
		"1" : "\u4e00",
		"2" : "\u4e8c",
		"3" : "\u4e09",
		"4" : "\u56db",
		"5" : "\u4e94",
		"6" : "\u516d"
	};
	if (/(y+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
	}
	if (/(E+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, ((RegExp.$1.length > 1) ? (RegExp.$1.length > 2 ? "\u661f\u671f" : "\u5468") : "") + week[this.getDay() + ""]);
	}
	for ( var k in o) {
		if (new RegExp("(" + k + ")").test(fmt)) {
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
		}
	}
	return fmt;
}
</script>
