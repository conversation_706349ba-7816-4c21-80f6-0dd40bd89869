<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() +"://" + request.getServerName() + ":" +request.getServerPort() + path +"/";
%>
<!DOCTYPE html>
<html>
<head>
<base href="<%=path %>/" />
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<%@include file="../../jsp/indexHead.jsp"%>
<link href="static/css/system/alarm/alarmSetIndex.css" rel="stylesheet">
<title>用户权限</title>
</head>
<body id="cur" class="container-fluid clears">
	<div  class="row">
	  <div id="rightCon">
            <div class="row headCon clears" >
                <p class="text-left clears" ></p>
            </div>
            <div class="row tdCon clears">
                <div class="row">
                	<div id="alrTil" class="row">
                		<div class="col-xs-5"><span id="alrys" class="btn btn-success" data-toggle="tooltip" title="（开启/关闭）当前用户告警配置方案">告警启用</span></div>
                		<div class="col-xs-7 text-right">
                			<div class="radio-inls1">
                				<label class="radio-inline">
								  	<input type="radio" checked="checked" name="unlock" value="1"> 启用
								</label>
                			</div>
							<div class="radio-inls1">
								<label class="radio-inline">
								  	<input type="radio" name="unlock" value="0"> 禁用
								</label>
							</div>
                		</div>
                	</div>
                </div>
                <div class="row">
                	<div class="row" id="alarTab">
			   			<div class="row clears">
			   				<table class="table table-bordered table-striped table-hover table-responsive text-center"
              					style="margin-bottom:0px;border-bottom-width:0px;">
              					<thead>
						  			<tr style="height: 45px;">
						  				<th><span class="btn btn-primary" id="alarAdd" data-toggle="tooltip" title="添加告警配置"><i class="icon icon-plus"></i></span></th>
										<th style="width: px;">创建账号</th>
										<th style="width: px;">创建用户</th>
										<th style="width: 80px;">报警级别</th>
										<th style="width: 80px;">报警方式</th>
										<th style="width: 80px;">是否全局</th>
										<th style="width: 75px;">状态</th>
										<th style="width: px;">修改用户</th>
										<th >修改时间</th>
										<th id="coI">操作</th>
						  			</tr>
						  		</thead>
			   				</table>
			   			</div>
			   			<div class="row clears tbods">
			   				<table class="table table-bordered table-striped table-hover table-responsive text-center"
              					style="margin-bottom:0px;border-top-width:0px; ">
              						<tbody id="taTbod"></tbody>
			   				</table>
			   			</div>
				    </div>
				    <!--  分页 -->
				 	<div class="myPagerPe" style="position:absolute;bottom:0px;width: 100%;"><ul id="myPager" class="pager pull-right" 
				 	data-ride="pager" data-page="1" data-rec-total="0" data-max-nav-count="4" data-menu-direction="dropup"
				  	data-page-size-options="10,20,30,50,100"  data-rec-per-page="10"
				  	data-elements="first_icon,prev_icon,pages,next_icon,last_icon,size_menu,goto,page_of_total_text,items_range_text,total_text,"></ul>
				 	</div>				    
				    
                </div>        
            </div>
      </div>
      <!-- 增加 -->
	  <div class="modal fade" id="alarDiv" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		  <div class="modal-dialog " role="document">
		    <div class="modal-content">
		      <div class="modal-header" style='background-color:#33485C;color:#fff;border-top-right-radius:5px;border-top-left-radius:5px;'>
		        <button type="button" class="close" data-dismiss="modal" style='color:#FFF;' aria-label="Close"><span aria-hidden="true">&times;</span></button>
		        <h3 class="modal-title text-center"  id="myModalLabel"></h3>
		      </div>
		      <div class="modal-body" style="position: relative;">
		        	<form id="alarForm" class='form-horizontal' >
		        		<input type="hidden" class="form-control" name="accountId" id="accountId" value="" />
						<input type="hidden" class="form-control" name="isValid" id="isValid" value="1" />
						<input type="hidden" name="menu" />
						<div class="form-group">
							<div class='col-xs-12 col-sm-12'>
								<div class="switch text-left col-xs-3 col-sm-2">
								  <input name='isVal' id='isVal' type="checkbox" checked="checked" title="是否启用" value="1" />
								  <label style="font-weight: bold;">状态</label>
								</div>
							</div>
						</div>
						<div class="form-group">
						    <label for="level" class="col-xs-3 col-sm-2 control-label required">报警级别</label>
						    <div class="col-xs-8 col-sm-9">
						    	<select class="form-control xiaoY" name="level" id="level">
									  <option value="">--请选择--</option>
								</select>
						    </div>
						</div>
						<div class="form-group">
						    <label for="way" class="col-xs-3 col-sm-2 control-label">报警方式</label>
						    <div class="col-xs-8 col-sm-9">
						      	<select class="form-control" name="way" id="way">
									  <option value="1">弹窗</option>
									  <option value="2">语音</option>
									  <!-- <option value="3">短信</option>
									  <option value="4">邮件</option> -->
								</select>
						    </div>
						</div>
						<div class="form-group">
						    <label for="whole" class="col-xs-3 col-sm-2 control-label required">是否全局</label>
						    <div class="col-xs-8 col-sm-9">
						    	<label class="radio-inline">
								  <input type="radio" name="whole" value="1" checked="checked" /> 是
								</label>
								<label class="radio-inline">
								  <input type="radio" name="whole" value="0" /> 否
								</label>
						    </div>
						</div>
						<div class="form-group hide">
						    <label for="relation" class="col-xs-3 col-sm-2 control-label required">站点关联</label>
						    <div class="col-xs-8 col-sm-9">
						    	<input type="hidden" class="form-control xiaoY" name="relation" id="relation" value="" />
						    	<button type="button" class="btn btn-purple" onclick="alrRelev(this)">关联</button>
						    	<div id="relaTitle" class="help-block"></div>
						    </div>
						</div>
						<input class="hide" type="reset" />
					</form>
		      </div>
		      <div class="modal-footer">
		        <button type="button" class="btn btn-danger" data-dismiss="modal">关闭</button>
		        <button type="button" id="subMit" class="btn btn-success">保存</button>
		      </div>
		    </div>
		  </div>
		</div>
		<!-- 站点关联 -->
		<div class="modal fade" id="tiMode3" >
			  <div class="modal-dialog modal-lg">
			    <div class="modal-content">
			      <div class="modal-header" style='background-color:#33485C;color:#fff;border-top-right-radius:5px;border-top-left-radius:5px;'>
			        <button type="button" class="close" data-dismiss="modal" style='color:#FFF;'><span aria-hidden="true">×</span><span class="sr-only">关闭</span></button>
			      	<h3 class="modal-title text-center" ><i class="icon icon-cogs"></i>&nbsp;&nbsp;站点关联</h3>
			      </div>
			      <div class="modal-body row clears" style="position: relative; padding:10px 15px;">
			      		<div class="row">
			      			<div class="col-sm-8 clears">
			      				<div class="input-group">
					      			<div class="input-control search-box search-box-circle has-icon-left has-icon-right" id="searchboxExample">
									  	<input id="inputSearch1" type="search" class="form-control search-input chosen-select" placeholder="请输入站点名称的关键字">
									  	<label for="inputSearch1" class="input-control-icon-left search-icon"><i class="icon icon-search"></i></label>
										<a href="#" class="input-control-icon-right search-clear-btn"><i class="icon icon-remove"></i></a>
									</div>
									<span class="input-group-btn">
									    <button class="btn btn-danger" type="button" id="sev">搜索</button>
									</span>
								</div>
								<!--  存储资源结构 -->
							    <div class="zTreeDemoBackground" style="display: none;">
						      		<ul id="modeTree3" class="ztree" ></ul>
						    	</div>
							</div>
			      		</div>
			      		<div class="clearfix" style="height:15px;"></div>
			      		<div class="row" id="droppCon">
						  <div id="multiDroppableContainer" class="col-sm-6" style="padding-left:0px;">
						    <div class="panel">
						      <div class="panel-heading text-center" style="color: #BD7B46">
						      	<div id="iconN" style="left:7px;">
						      		<span val="0" class="icon icon-check-empty" ></span>
						      	</div>
						      	<div id="iconN1" title="关联" style="right:10px;">
						      		<span class="icon icon-share-alt" ></span>
						      	</div>可关联站点
						      </div>
						      <div class="panel-body reN clears" style="min-height: 400px;height: 400px;">
						        	<div id="relationNo" class="row">
						        		<!-- 内容 -->
						        	</div>
						      </div>
						    </div>
						    <p class="pag1">已勾选<span id="tN" class="xss1">0</span>/<span id="tNs" class="xss1">0</span>项</p>
						  </div>
						  <div class="col-sm-6" style="padding-right:0px;">
						    <div class="panel droppable-target">
						      <div class="panel-heading text-center" style="color: #38B03F;">
						      	<div id="iconY1" title="移除关联" style="left:10px;">
						      		<span class="icon icon-reply"></span>
						      	</div>
						      	<div id="iconY" style="right:7px;">
						      		<span  val="0"  class="icon icon-check-empty" ></span>
						      	</div>
						      	已关联站点
						      </div>
						      <div class="panel-body reY clears" style="min-height: 400px;height: 400px;">
						        	<div id="relationYES" class="row">
						        		<!-- 内容 -->
						        	</div>
						      </div>
						    </div>
						    <p class="pag1">已勾选<span id="tY" class="xss1">0</span>/<span id="tYs" class="xss1">0</span>项</p>
						  </div>
			      		</div>
			      		
			      </div>
			      <div class="modal-footer">
			        <button type="button" class="btn btn-primary" data-dismiss="modal">取消</button>
			        <button type="button" id="subPa_1" class="btn btn-danger">确定</button>
			      </div>
			    </div>
			  </div>
		</div>

    </div>
    
<script id="level_con" type="text/html">
	<option value="">--请选择--</option>
	{{each data as value index}}
		{{if index <= 9 && index > 0}}
			<option value="{{index}}">{{value}}</option>
		{{/if}}
	{{/each}}
</script> 
<script id="taTbod_1" type="text/html">
	{{if data != null && data.length != 0}}
	{{each data as value index}}
		<tr>
            <td>{{index + 1}}</td>
            <td>{{value.loginName}}</td>
            <td>{{value.name}}</td>
            <td><span class="label label-badge label-warning level_{{value.loginName}}" value="{{value.level}}">{{levelCons[value.level]}}</span></td>
            <td><span class="label label-badge label-info wayType" value="{{value.way}}">
			{{if value.way == 1}}弹窗
				{{else if value.way == 2}}响铃
				{{else if value.way == 3}}语音
				{{else if value.way == 4}}邮件
			{{/if}}</span></td>
            <td>{{if value.whole == 1}} <span class="label label-badge label-primary">是</span>
				{{else}} <span class="label label-badge hl-primary">否</span>
			{{/if}}</td>
            <td>{{if value.isValid == 1}} <span class="label label-badge label-success">启用</span>
				{{else}} <span class="label label-badge">禁用</span>
			{{/if}}</td>
            <td>{{value.updateName}}</td>
            <td>{{value.updateTime.substring(0,19)}}</td>
            <td>
	        	<div class="btn-group">
					{{if value.isValid == 0}}
							<button type="button" class="btn btn-success" onclick="alrState({{value}})">启用</button>
						{{else}}
							<button type="button" class="btn btn-danger" onclick="alrState({{value}})">禁用</button>
					{{/if}}
			  		<button type="button" class="btn btn-warning" onclick="alrEdit({{value}})">修改</button>
				</div>
			</td>
        </tr>
	{{/each}}
	{{else}}
		<tr><td colspan="100">暂时没有数据</td></tr>
	{{/if}}
</script>
<!-- 可关联站点 -->
<script id="relation_s" type="text/html">
	<table class="table table-bordered table-striped table-hover table-responsive" style="border-width:0px;">
	{{if noCheckedStations != null}}
		{{each noCheckedStations as value index}}
			<tr class="G_{{value.dcsId}}">
				<td style="height:15px;line-height:15px;width:50px;border-top:0px;" class="text-center res_s"><input type="checkbox" value="{{value.dcsId}}" /></td>
				<td style="vertical-align:middle;height:15px;line-height:15px;border-top:0px;" class="text-center">{{value.dcsName}}</td>
			</tr>
		{{/each}}
	{{/if}}
	</table>
</script>
<!-- 以关联站点 -->
<script id="relationYES_s" type="text/html">
	<table class="table table-bordered table-striped table-hover table-responsive" style="border-width:0px;">
	{{if checkedStations != null }}
		{{each checkedStations as value1 index1}}
			<tr class="G_{{value1.dcsId}}">
				<td style="vertical-align:middle;height:15px;line-height:15px;border-top:0px;" class="text-center">{{value1.dcsName}}</td>
				<td style="height:15px;line-height:15px;width:50px;border-top:0px;" class="text-center res_s"><input type="checkbox" value="{{value1.dcsId}}" /></td>
			</tr>
		{{/each}}
	{{/if}}
	</table>	
</script>
<%@include file="../../jsp/indexJavaScipt.jsp"%>
<script src="static/js/system/alarm/alarmSetIndex.js"></script>
</body>
</html>